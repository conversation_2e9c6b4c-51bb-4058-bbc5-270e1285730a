/**
 * User repository implementation
 * Handles CRUD operations for users and their preferences
 */

import { BaseRepository } from '../BaseRepository';
import { DatabaseConnection, UserEntity } from '../types';
import { User, UserPreferences, CreateUserRequest } from '../../types/models/User';

/**
 * Repository for user entities
 */
export class UserRepository extends BaseRepository<User> {
  constructor(db: DatabaseConnection) {
    super(db, 'users');
  }

  /**
   * Find user by username
   */
  async findByUsername(username: string): Promise<User | null> {
    return this.findOne({ username });
  }

  /**
   * Find user by email
   */
  async findByEmail(email: string): Promise<User | null> {
    return this.findOne({ email });
  }

  /**
   * Find active users
   */
  async findActiveUsers(): Promise<User[]> {
    return this.findAll({ 
      isActive: true,
      orderBy: 'last_active_at',
      orderDirection: 'DESC'
    });
  }

  /**
   * Update user preferences
   */
  async updatePreferences(userId: string, preferences: Partial<UserPreferences>): Promise<void> {
    const user = await this.findById(userId);
    if (!user) {
      throw new Error(`User with ID ${userId} not found`);
    }

    const updatedPreferences = {
      ...user.preferences,
      ...preferences
    };

    await this.update(userId, { preferences: updatedPreferences });
  }

  /**
   * Update user activity
   */
  async updateActivity(userId: string): Promise<void> {
    const sql = `
      UPDATE ${this.tableName} 
      SET last_active_at = CURRENT_TIMESTAMP,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    await this.db.run(sql, [userId]);
  }

  /**
   * Increment prompt count
   */
  async incrementPromptCount(userId: string, delta: number = 1): Promise<void> {
    const sql = `
      UPDATE ${this.tableName} 
      SET total_prompts = total_prompts + ?,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    await this.db.run(sql, [delta, userId]);
  }

  /**
   * Get user statistics
   */
  async getStatistics(): Promise<{
    total: number;
    active: number;
    withEmail: number;
    averagePrompts: number;
  }> {
    const [total, active, withEmail] = await Promise.all([
      this.count(),
      this.count({ isActive: true }),
      this.query<{ count: number }>('SELECT COUNT(*) as count FROM users WHERE email IS NOT NULL')
        .then(rows => rows[0]?.count || 0)
    ]);

    const avgResult = await this.queryOne<{ avg_prompts: number }>(
      'SELECT AVG(total_prompts) as avg_prompts FROM users'
    );

    return {
      total,
      active,
      withEmail,
      averagePrompts: avgResult?.avg_prompts || 0
    };
  }

  /**
   * Create default user
   */
  async createDefaultUser(): Promise<string> {
    const defaultUser: CreateUserRequest = {
      displayName: 'User',
      preferences: this.getDefaultPreferences()
    };

    return this.createUser(defaultUser);
  }

  /**
   * Create user with validation
   */
  async createUser(request: CreateUserRequest): Promise<string> {
    // Validate unique constraints
    if (request.username) {
      const existingByUsername = await this.findByUsername(request.username);
      if (existingByUsername) {
        throw new Error('Username already exists');
      }
    }

    if (request.email) {
      const existingByEmail = await this.findByEmail(request.email);
      if (existingByEmail) {
        throw new Error('Email already exists');
      }
    }

    const user: User = {
      id: this.generateId(),
      username: request.username,
      email: request.email,
      displayName: request.displayName,
      preferences: { ...this.getDefaultPreferences(), ...request.preferences },
      metadata: {
        createdAt: new Date(),
        loginCount: 0,
        appVersion: '1.0.0',
        platform: process.platform,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        locale: 'en-US'
      },
      isActive: true,
      membershipDuration: 0,
      totalPrompts: 0
    };

    return this.save(user);
  }

  /**
   * Map database row to User entity
   */
  protected mapRowToEntity(row: any): User {
    const preferences = this.deserializeValue(row.preferences, 'json') || this.getDefaultPreferences();
    const metadata = this.deserializeValue(row.metadata, 'json') || {};
    const subscription = row.subscription ? this.deserializeValue(row.subscription, 'json') : undefined;

    return {
      id: row.id,
      username: row.username,
      email: row.email,
      displayName: row.display_name,
      avatar: row.avatar,
      preferences,
      subscription,
      metadata: {
        createdAt: new Date(row.created_at),
        ...(row.last_login_at && { lastLoginAt: new Date(row.last_login_at) }),
        ...(row.last_active_at && { lastActiveAt: new Date(row.last_active_at) }),
        loginCount: row.login_count || 0,
        appVersion: metadata.appVersion || '1.0.0',
        platform: metadata.platform || process.platform,
        timezone: metadata.timezone || 'UTC',
        locale: metadata.locale || 'en-US'
      },
      isActive: this.deserializeValue(row.is_active, 'boolean'),
      membershipDuration: Math.floor((Date.now() - new Date(row.created_at).getTime()) / (1000 * 60 * 60 * 24)),
      totalPrompts: row.total_prompts || 0
    };
  }

  /**
   * Override save to handle user-specific fields
   */
  async save(entity: User): Promise<string> {
    const userEntity: UserEntity = {
      id: (entity as any).id || this.generateId(),
      preferences: JSON.stringify(entity.preferences),
      metadata: JSON.stringify(entity.metadata),
      isActive: entity.isActive,
      totalPrompts: entity.totalPrompts,
      createdAt: entity.metadata.createdAt,
      updatedAt: new Date(),
      ...(entity.username && { username: entity.username }),
      ...(entity.email && { email: entity.email }),
      ...(entity.displayName && { displayName: entity.displayName }),
      ...(entity.avatar && { avatar: entity.avatar }),
      ...(entity.subscription && { subscription: JSON.stringify(entity.subscription) })
    };

    const columns = Object.keys(userEntity).filter(key => userEntity[key as keyof UserEntity] !== undefined);
    const placeholders = columns.map(() => '?').join(', ');
    const values = columns.map(col => this.serializeValue(userEntity[col as keyof UserEntity]));

    const sql = `INSERT OR REPLACE INTO ${this.tableName} (${columns.map(col => this.camelToSnake(col)).join(', ')}) VALUES (${placeholders})`;

    await this.db.run(sql, values);
    return userEntity.id;
  }

  /**
   * Get default user preferences
   */
  private getDefaultPreferences(): UserPreferences {
    return {
      appearance: {
        theme: 'system',
        language: 'en',
        fontSize: 'medium',
        compactMode: false,
        animations: true
      },
      behavior: {
        autoSave: true,
        autoBackup: true,
        confirmDeletions: true,
        rememberWindowState: true,
        startMinimized: false,
        checkUpdates: true
      },
      privacy: {
        analytics: false,
        crashReports: true,
        usageStatistics: false,
        dataSharing: false,
        anonymizeData: true,
        retentionPeriod: 90
      },
      ai: {
        defaultProvider: 'openai',
        defaultModel: 'gpt-3.5-turbo',
        temperature: 0.7,
        maxTokens: 2048,
        autoEnhance: true,
        contextInjection: true,
        costWarnings: true
      },
      voice: {
        defaultLanguage: 'en-US',
        provider: 'browser',
        sensitivity: 0.5,
        noiseReduction: true,
        autoStop: true,
        maxDuration: 60,
        commands: true
      },
      hotkeys: {
        showFloatingWindow: 'Ctrl+Shift+P',
        captureScreen: 'Ctrl+Shift+S',
        startVoiceInput: 'Ctrl+Shift+V',
        quickSearch: 'Ctrl+Shift+F',
        enhanceClipboard: 'Ctrl+Shift+E',
        customHotkeys: {}
      },
      notifications: {
        enabled: true,
        sound: true,
        desktop: true,
        duration: 5000,
        position: 'top-right',
        types: ['success', 'error', 'warning', 'info']
      }
    };
  }
}
