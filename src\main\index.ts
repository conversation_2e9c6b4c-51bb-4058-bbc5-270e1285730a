/**
 * Main Entry Point
 * Personal Prompt Application
 */

import { Application, ApplicationConfig } from './Application';
import { LoggerFactory } from '../core/logger';

const logger = LoggerFactory.getInstance().getLogger('Main');

/**
 * Main application entry point
 */
async function main(): Promise<void> {
  try {
    logger.info('Starting Personal Prompt Application...');

    // Load configuration
    const config: Partial<ApplicationConfig> = {
      database: {
        path: process.env.DB_PATH || './data/personal-prompt.db',
        enableWAL: true,
        enableForeignKeys: true
      },
      logging: {
        level: (process.env.LOG_LEVEL as any) || 'info',
        enableFileLogging: true,
        logDirectory: process.env.LOG_DIR || './logs'
      },
      hotkeys: {
        enableGlobalHotkeys: process.env.ENABLE_HOTKEYS !== 'false',
        defaultHotkeys: {
          'show-window': process.env.HOTKEY_SHOW || 'Ctrl+Shift+P',
          'capture-screen': process.env.HOTKEY_CAPTURE || 'Ctrl+Shift+O',
          'voice-input': process.env.HOTKEY_VOICE || 'Ctrl+Shift+V',
          'show-library': process.env.HOTKEY_LIBRARY || 'Ctrl+Shift+L'
        }
      },
      ui: {
        theme: (process.env.UI_THEME as any) || 'auto',
        enableAnimations: process.env.ENABLE_ANIMATIONS !== 'false',
        windowOpacity: parseFloat(process.env.WINDOW_OPACITY || '0.95')
      },
      voice: {
        enableVoiceInput: process.env.ENABLE_VOICE !== 'false',
        defaultLanguage: process.env.VOICE_LANGUAGE || 'en-US',
        enableContinuousListening: process.env.CONTINUOUS_LISTENING === 'true'
      },
      context: {
        enableScreenCapture: process.env.ENABLE_SCREEN_CAPTURE !== 'false',
        enableClipboardMonitoring: process.env.ENABLE_CLIPBOARD === 'true',
        ocrLanguage: process.env.OCR_LANGUAGE || 'eng'
      },
      ai: {
        defaultProvider: process.env.AI_PROVIDER || 'openai',
        enableStreaming: process.env.ENABLE_STREAMING !== 'false',
        maxTokens: parseInt(process.env.MAX_TOKENS || '4000')
      }
    };

    // Create and initialize application
    const app = new Application(config);
    
    // Setup application event handlers
    setupApplicationEventHandlers(app);

    // Initialize and start the application
    await app.initialize();
    await app.start();

    logger.info('Personal Prompt Application is running');
    
    // Keep the process alive
    process.stdin.resume();

  } catch (error) {
    logger.error('Failed to start application', { error });
    process.exit(1);
  }
}

/**
 * Setup application event handlers
 */
function setupApplicationEventHandlers(app: Application): void {
  app.on('initialized', () => {
    logger.info('Application initialized');
  });

  app.on('started', () => {
    logger.info('Application started');
    console.log('\n🚀 Personal Prompt Application is ready!');
    console.log('📋 Use Ctrl+Shift+P to show the floating window');
    console.log('📸 Use Ctrl+Shift+O to capture screen');
    console.log('🎤 Use Ctrl+Shift+V for voice input');
    console.log('📚 Use Ctrl+Shift+L to show prompt library');
    console.log('❌ Use Ctrl+C to exit\n');
  });

  app.on('stopped', () => {
    logger.info('Application stopped');
  });

  app.on('shutdown', () => {
    logger.info('Application shutdown complete');
    process.exit(0);
  });

  app.on('error', (error) => {
    logger.error('Application error', { error });
  });

  app.on('config-updated', (config) => {
    logger.info('Configuration updated', { config });
  });
}

/**
 * Handle CLI arguments
 */
function handleCliArguments(): void {
  const args = process.argv.slice(2);
  
  for (const arg of args) {
    switch (arg) {
      case '--help':
      case '-h':
        showHelp();
        process.exit(0);
        break;
      case '--version':
      case '-v':
        showVersion();
        process.exit(0);
        break;
      case '--debug':
        process.env.LOG_LEVEL = 'debug';
        break;
      case '--no-hotkeys':
        process.env.ENABLE_HOTKEYS = 'false';
        break;
      case '--no-voice':
        process.env.ENABLE_VOICE = 'false';
        break;
      case '--dark':
        process.env.UI_THEME = 'dark';
        break;
      case '--light':
        process.env.UI_THEME = 'light';
        break;
    }
  }
}

/**
 * Show help information
 */
function showHelp(): void {
  console.log(`
Personal Prompt Application

USAGE:
  npm start [OPTIONS]

OPTIONS:
  -h, --help          Show this help message
  -v, --version       Show version information
  --debug             Enable debug logging
  --no-hotkeys        Disable global hotkeys
  --no-voice          Disable voice input
  --dark              Use dark theme
  --light             Use light theme

ENVIRONMENT VARIABLES:
  DB_PATH             Database file path (default: ./data/personal-prompt.db)
  LOG_LEVEL           Logging level (debug|info|warn|error)
  LOG_DIR             Log directory (default: ./logs)
  HOTKEY_SHOW         Show window hotkey (default: Ctrl+Shift+P)
  HOTKEY_CAPTURE      Screen capture hotkey (default: Ctrl+Shift+O)
  HOTKEY_VOICE        Voice input hotkey (default: Ctrl+Shift+V)
  HOTKEY_LIBRARY      Library hotkey (default: Ctrl+Shift+L)
  UI_THEME            UI theme (light|dark|auto)
  ENABLE_ANIMATIONS   Enable UI animations (true|false)
  WINDOW_OPACITY      Window opacity (0.0-1.0)
  VOICE_LANGUAGE      Voice recognition language (default: en-US)
  OCR_LANGUAGE        OCR language (default: eng)
  AI_PROVIDER         AI provider (default: openai)
  MAX_TOKENS          Maximum AI tokens (default: 4000)

EXAMPLES:
  npm start                    # Start with default settings
  npm start --debug            # Start with debug logging
  npm start --no-hotkeys       # Start without global hotkeys
  npm start --dark             # Start with dark theme
  
For more information, visit: https://github.com/user/personal-prompt
`);
}

/**
 * Show version information
 */
function showVersion(): void {
  const packageJson = require('../../package.json');
  console.log(`Personal Prompt Application v${packageJson.version}`);
}

/**
 * Setup error handling
 */
function setupErrorHandling(): void {
  process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    logger.error('Uncaught exception', { error });
    process.exit(1);
  });

  process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    logger.error('Unhandled rejection', { reason, promise });
    process.exit(1);
  });
}

// Handle CLI arguments
handleCliArguments();

// Setup error handling
setupErrorHandling();

// Start the application
if (require.main === module) {
  main().catch((error) => {
    console.error('Failed to start application:', error);
    process.exit(1);
  });
}

export { main, Application };
export * from './Application';
