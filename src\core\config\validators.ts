/**
 * Configuration validation utilities
 * Provides validation rules and validators for configuration settings
 */

import { ConfigValidator, ConfigSetting, ValidationResult, ValidationRule } from './types';

export class DefaultConfigValidator implements ConfigValidator {
  validate(value: any, setting: ConfigSetting): ValidationResult {
    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: []
    };

    // Check required fields
    if (setting.required && (value === undefined || value === null)) {
      result.valid = false;
      result.errors.push(`${setting.title} is required`);
      return result;
    }

    // Skip validation if value is undefined/null and not required
    if (value === undefined || value === null) {
      return result;
    }

    // Type validation
    const typeValidation = this.validateType(value, setting);
    if (!typeValidation.valid) {
      result.valid = false;
      result.errors.push(...typeValidation.errors);
    }

    // Enum validation
    if (setting.type === 'enum' && setting.options) {
      if (!setting.options.includes(value)) {
        result.valid = false;
        result.errors.push(`${setting.title} must be one of: ${setting.options.join(', ')}`);
      }
    }

    // Custom validation rules
    if (setting.validation) {
      for (const rule of setting.validation) {
        const ruleResult = this.validateRule(value, rule);
        if (!ruleResult.valid) {
          result.valid = false;
          result.errors.push(rule.message);
        }
      }
    }

    return result;
  }

  private validateType(value: any, setting: ConfigSetting): ValidationResult {
    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: []
    };

    switch (setting.type) {
      case 'string':
        if (typeof value !== 'string') {
          result.valid = false;
          result.errors.push(`${setting.title} must be a string`);
        }
        break;

      case 'number':
        if (typeof value !== 'number' || isNaN(value)) {
          result.valid = false;
          result.errors.push(`${setting.title} must be a number`);
        }
        break;

      case 'boolean':
        if (typeof value !== 'boolean') {
          result.valid = false;
          result.errors.push(`${setting.title} must be a boolean`);
        }
        break;

      case 'array':
        if (!Array.isArray(value)) {
          result.valid = false;
          result.errors.push(`${setting.title} must be an array`);
        }
        break;

      case 'object':
        if (typeof value !== 'object' || Array.isArray(value) || value === null) {
          result.valid = false;
          result.errors.push(`${setting.title} must be an object`);
        }
        break;

      case 'enum':
        // Enum validation is handled separately
        break;

      default:
        result.warnings.push(`Unknown type: ${setting.type}`);
    }

    return result;
  }

  private validateRule(value: any, rule: ValidationRule): ValidationResult {
    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: []
    };

    switch (rule.type) {
      case 'min':
        if (typeof value === 'number' && value < rule.value) {
          result.valid = false;
        } else if (typeof value === 'string' && value.length < rule.value) {
          result.valid = false;
        } else if (Array.isArray(value) && value.length < rule.value) {
          result.valid = false;
        }
        break;

      case 'max':
        if (typeof value === 'number' && value > rule.value) {
          result.valid = false;
        } else if (typeof value === 'string' && value.length > rule.value) {
          result.valid = false;
        } else if (Array.isArray(value) && value.length > rule.value) {
          result.valid = false;
        }
        break;

      case 'pattern':
        if (typeof value === 'string' && rule.value instanceof RegExp) {
          if (!rule.value.test(value)) {
            result.valid = false;
          }
        }
        break;

      case 'custom':
        if (rule.validator && typeof rule.validator === 'function') {
          try {
            result.valid = rule.validator(value);
          } catch (error) {
            result.valid = false;
            result.warnings.push(`Custom validator error: ${error}`);
          }
        }
        break;

      default:
        result.warnings.push(`Unknown validation rule type: ${rule.type}`);
    }

    return result;
  }
}

/**
 * Specialized validators for specific setting types
 */

export class HotkeyValidator implements ConfigValidator {
  validate(value: any, setting: ConfigSetting): ValidationResult {
    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: []
    };

    if (typeof value !== 'string') {
      result.valid = false;
      result.errors.push('Hotkey must be a string');
      return result;
    }

    // Basic hotkey format validation
    const hotkeyPattern = /^(Ctrl|Alt|Shift|Cmd)(\+(Ctrl|Alt|Shift|Cmd))*\+[A-Z0-9]+$/;
    if (!hotkeyPattern.test(value)) {
      result.valid = false;
      result.errors.push('Invalid hotkey format. Use format like "Ctrl+Shift+P"');
    }

    // Check for duplicate modifiers
    const parts = value.split('+');
    const modifiers = parts.slice(0, -1);
    const uniqueModifiers = new Set(modifiers);
    
    if (modifiers.length !== uniqueModifiers.size) {
      result.valid = false;
      result.errors.push('Duplicate modifiers in hotkey');
    }

    return result;
  }
}

export class PathValidator implements ConfigValidator {
  validate(value: any, setting: ConfigSetting): ValidationResult {
    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: []
    };

    if (typeof value !== 'string') {
      result.valid = false;
      result.errors.push('Path must be a string');
      return result;
    }

    // Basic path validation (platform-agnostic)
    const invalidChars = /[<>:"|?*]/;
    if (invalidChars.test(value)) {
      result.valid = false;
      result.errors.push('Path contains invalid characters');
    }

    return result;
  }
}

export class APIKeyValidator implements ConfigValidator {
  validate(value: any, setting: ConfigSetting): ValidationResult {
    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: []
    };

    if (typeof value !== 'string') {
      result.valid = false;
      result.errors.push('API key must be a string');
      return result;
    }

    // Basic API key format validation
    if (value.length > 0 && value.length < 10) {
      result.warnings.push('API key seems too short');
    }

    if (value.includes(' ')) {
      result.warnings.push('API key should not contain spaces');
    }

    return result;
  }
}

/**
 * Validator factory for creating appropriate validators
 */
export class ValidatorFactory {
  static createValidator(setting: ConfigSetting): ConfigValidator {
    // Use specialized validators for specific cases
    if (setting.title.toLowerCase().includes('hotkey')) {
      return new HotkeyValidator();
    }
    
    if (setting.title.toLowerCase().includes('path')) {
      return new PathValidator();
    }
    
    if (setting.title.toLowerCase().includes('api key') || setting.sensitive) {
      return new APIKeyValidator();
    }
    
    // Default validator for all other cases
    return new DefaultConfigValidator();
  }
}
