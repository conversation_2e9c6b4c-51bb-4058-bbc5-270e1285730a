/**
 * File system module exports
 * Cross-platform file system operations with security and error handling
 */

// Core classes
export { FileSystemManager } from './FileSystemManager';
export { FileWatcher, MultiFileWatcher, createFileWatcher } from './FileWatcher';
export { PathUtils } from './PathUtils';

// Import for local use
import { FileSystemManager } from './FileSystemManager';
import { PathUtils } from './PathUtils';

// Types and interfaces
export * from './types';

// Convenience functions
let globalFileSystemManager: FileSystemManager | null = null;

export const getFileSystemManager = (): FileSystemManager => {
  if (!globalFileSystemManager) {
    globalFileSystemManager = new FileSystemManager();
  }
  return globalFileSystemManager;
};

export const initializeFileSystem = (securityContext?: any): FileSystemManager => {
  globalFileSystemManager = new FileSystemManager(securityContext);
  return globalFileSystemManager;
};

// File operations
export const exists = async (path: string): Promise<boolean> => {
  return getFileSystemManager().exists(path);
};

export const readFile = async (path: string, options?: any): Promise<string | Buffer> => {
  return getFileSystemManager().readFile(path, options);
};

export const writeFile = async (path: string, data: string | Buffer, options?: any): Promise<void> => {
  return getFileSystemManager().writeFile(path, data, options);
};

export const deleteFile = async (path: string): Promise<void> => {
  return getFileSystemManager().deleteFile(path);
};

export const copyFile = async (src: string, dest: string, options?: any): Promise<void> => {
  return getFileSystemManager().copyFile(src, dest, options);
};

export const moveFile = async (src: string, dest: string, options?: any): Promise<void> => {
  return getFileSystemManager().moveFile(src, dest, options);
};

export const createDirectory = async (path: string, options?: any): Promise<void> => {
  return getFileSystemManager().createDirectory(path, options);
};

export const deleteDirectory = async (path: string, options?: any): Promise<void> => {
  return getFileSystemManager().deleteDirectory(path, options);
};

export const listDirectory = async (path: string, options?: any): Promise<any[]> => {
  return getFileSystemManager().listDirectory(path, options);
};

export const getMetadata = async (path: string): Promise<any> => {
  return getFileSystemManager().getMetadata(path);
};

export const getFileStats = async (path: string): Promise<any> => {
  return getFileSystemManager().getMetadata(path);
};

export const createTempFile = async (prefix?: string, suffix?: string): Promise<string> => {
  const tempPath = getTempPath(prefix);
  const fileName = `${tempPath}${suffix || '.tmp'}`;
  await writeFile(fileName, '');
  return fileName;
};

export const watch = async (path: string, options?: any): Promise<any> => {
  return getFileSystemManager().watch(path, options);
};

export const createBackup = async (path: string, options?: any): Promise<string> => {
  return getFileSystemManager().createBackup(path, options);
};

export const restoreBackup = async (backupPath: string, targetPath: string): Promise<void> => {
  return getFileSystemManager().restoreBackup(backupPath, targetPath);
};

// Path utilities
export const normalizePath = (path: string): string => {
  return PathUtils.normalize(path);
};

export const resolvePath = (...paths: string[]): string => {
  return PathUtils.resolve(...paths);
};

export const joinPath = (...paths: string[]): string => {
  return PathUtils.join(...paths);
};

export const validatePath = (path: string, context?: any): any => {
  return PathUtils.validatePath(path, context);
};

export const sanitizeFilename = (filename: string): string => {
  return PathUtils.sanitizeFilename(filename);
};

export const getTempPath = (prefix?: string): string => {
  return PathUtils.getTempPath(prefix);
};

export const getHomePath = (): string => {
  return PathUtils.getHomePath();
};

export const getAppDataPath = (appName: string): string => {
  return PathUtils.getAppDataPath(appName);
};
