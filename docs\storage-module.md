# Storage Module Specification

## 🎯 Purpose

The Storage Module provides persistent data storage capabilities for the PromptPilot Desktop application, managing prompts, settings, user data, and application state with SQLite database backend and optional cloud synchronization.

## 📋 Responsibilities

### Data Persistence
- SQLite database management
- CRUD operations for all data types
- Transaction management
- Data integrity and validation

### Schema Management
- Database schema versioning
- Migration scripts
- Index optimization
- Backup and restore

### Synchronization
- Local-first data storage
- Optional cloud sync capabilities
- Conflict resolution
- Offline support

## 🏗️ Architecture

### Core Storage Module
```typescript
class StorageModule {
  private db: Database;
  private migrationManager: MigrationManager;
  private syncManager: SyncManager;
  private encryptionService: EncryptionService;
  
  async initialize(): Promise<void>;
  async close(): Promise<void>;
  async save<T>(table: string, data: T): Promise<string>;
  async load<T>(table: string, id: string): Promise<T | null>;
  async loadAll<T>(table: string, filter?: QueryFilter): Promise<T[]>;
  async update<T>(table: string, id: string, updates: Partial<T>): Promise<void>;
  async delete(table: string, id: string): Promise<void>;
  async query<T>(sql: string, params?: any[]): Promise<T[]>;
}
```

### Database Configuration
```typescript
interface DatabaseConfig {
  path: string;
  encryption: {
    enabled: boolean;
    key?: string;
  };
  performance: {
    journalMode: 'DELETE' | 'TRUNCATE' | 'PERSIST' | 'MEMORY' | 'WAL';
    synchronous: 'OFF' | 'NORMAL' | 'FULL' | 'EXTRA';
    cacheSize: number;
    mmapSize: number;
  };
  backup: {
    enabled: boolean;
    interval: number; // minutes
    maxBackups: number;
  };
}
```

## 🗄️ Database Schema

### Prompts Table
```sql
CREATE TABLE prompts (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  description TEXT,
  category TEXT NOT NULL,
  tags TEXT, -- JSON array
  variables TEXT, -- JSON array
  metadata TEXT NOT NULL, -- JSON object
  version INTEGER DEFAULT 1,
  parent_id TEXT,
  is_template BOOLEAN DEFAULT FALSE,
  is_favorite BOOLEAN DEFAULT FALSE,
  usage_count INTEGER DEFAULT 0,
  last_used_at DATETIME,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (parent_id) REFERENCES prompts(id) ON DELETE SET NULL
);

CREATE INDEX idx_prompts_category ON prompts(category);
CREATE INDEX idx_prompts_tags ON prompts(tags);
CREATE INDEX idx_prompts_created_at ON prompts(created_at);
CREATE INDEX idx_prompts_last_used_at ON prompts(last_used_at);
CREATE INDEX idx_prompts_is_favorite ON prompts(is_favorite);
CREATE INDEX idx_prompts_is_template ON prompts(is_template);
CREATE UNIQUE INDEX idx_prompts_title_category ON prompts(title, category);
```

### Categories Table
```sql
CREATE TABLE categories (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  parent_id TEXT,
  color TEXT,
  icon TEXT,
  sort_order INTEGER DEFAULT 0,
  prompt_count INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE CASCADE
);

CREATE INDEX idx_categories_parent_id ON categories(parent_id);
CREATE INDEX idx_categories_sort_order ON categories(sort_order);
```

### Tags Table
```sql
CREATE TABLE tags (
  name TEXT PRIMARY KEY,
  color TEXT,
  description TEXT,
  usage_count INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_tags_usage_count ON tags(usage_count DESC);
```

### Settings Table
```sql
CREATE TABLE settings (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL,
  type TEXT NOT NULL, -- 'string', 'number', 'boolean', 'object'
  category TEXT DEFAULT 'general',
  description TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_settings_category ON settings(category);
```

### Usage Analytics Table
```sql
CREATE TABLE usage_analytics (
  id TEXT PRIMARY KEY,
  prompt_id TEXT NOT NULL,
  used_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  rating INTEGER, -- 1-5 scale
  success BOOLEAN,
  context_type TEXT, -- 'manual', 'voice', 'hotkey', etc.
  metadata TEXT, -- JSON object for additional data
  FOREIGN KEY (prompt_id) REFERENCES prompts(id) ON DELETE CASCADE
);

CREATE INDEX idx_usage_analytics_prompt_id ON usage_analytics(prompt_id);
CREATE INDEX idx_usage_analytics_used_at ON usage_analytics(used_at);
```

## 🔧 Database Operations

### Repository Pattern Implementation
```typescript
abstract class BaseRepository<T> {
  constructor(protected db: Database, protected tableName: string) {}
  
  async save(entity: T): Promise<string> {
    const id = this.generateId();
    const columns = Object.keys(entity as any);
    const placeholders = columns.map(() => '?').join(', ');
    const values = columns.map(col => (entity as any)[col]);
    
    const sql = `INSERT INTO ${this.tableName} (id, ${columns.join(', ')}) 
                 VALUES (?, ${placeholders})`;
    
    await this.db.run(sql, [id, ...values]);
    return id;
  }
  
  async findById(id: string): Promise<T | null> {
    const sql = `SELECT * FROM ${this.tableName} WHERE id = ?`;
    const row = await this.db.get(sql, [id]);
    return row ? this.mapRowToEntity(row) : null;
  }
  
  async findAll(filter?: QueryFilter): Promise<T[]> {
    let sql = `SELECT * FROM ${this.tableName}`;
    const params: any[] = [];
    
    if (filter) {
      const { whereClause, whereParams } = this.buildWhereClause(filter);
      sql += ` WHERE ${whereClause}`;
      params.push(...whereParams);
    }
    
    if (filter?.orderBy) {
      sql += ` ORDER BY ${filter.orderBy} ${filter.orderDirection || 'ASC'}`;
    }
    
    if (filter?.limit) {
      sql += ` LIMIT ${filter.limit}`;
      if (filter.offset) {
        sql += ` OFFSET ${filter.offset}`;
      }
    }
    
    const rows = await this.db.all(sql, params);
    return rows.map(row => this.mapRowToEntity(row));
  }
  
  async update(id: string, updates: Partial<T>): Promise<void> {
    const columns = Object.keys(updates);
    const setClause = columns.map(col => `${col} = ?`).join(', ');
    const values = columns.map(col => (updates as any)[col]);
    
    const sql = `UPDATE ${this.tableName} SET ${setClause}, updated_at = CURRENT_TIMESTAMP 
                 WHERE id = ?`;
    
    await this.db.run(sql, [...values, id]);
  }
  
  async delete(id: string): Promise<void> {
    const sql = `DELETE FROM ${this.tableName} WHERE id = ?`;
    await this.db.run(sql, [id]);
  }
  
  protected abstract mapRowToEntity(row: any): T;
  protected abstract generateId(): string;
  protected abstract buildWhereClause(filter: QueryFilter): { whereClause: string; whereParams: any[] };
}
```

### Prompt Repository
```typescript
class PromptRepository extends BaseRepository<Prompt> {
  constructor(db: Database) {
    super(db, 'prompts');
  }
  
  async findByCategory(categoryId: string): Promise<Prompt[]> {
    return this.findAll({ category: categoryId });
  }
  
  async findByTags(tags: string[]): Promise<Prompt[]> {
    const placeholders = tags.map(() => 'JSON_EXTRACT(tags, "$") LIKE ?').join(' OR ');
    const params = tags.map(tag => `%"${tag}"%`);
    
    const sql = `SELECT * FROM ${this.tableName} WHERE ${placeholders}`;
    const rows = await this.db.all(sql, params);
    return rows.map(row => this.mapRowToEntity(row));
  }
  
  async findFavorites(): Promise<Prompt[]> {
    return this.findAll({ is_favorite: true });
  }
  
  async findTemplates(): Promise<Prompt[]> {
    return this.findAll({ is_template: true });
  }
  
  async findRecentlyUsed(limit: number = 10): Promise<Prompt[]> {
    return this.findAll({
      orderBy: 'last_used_at',
      orderDirection: 'DESC',
      limit
    });
  }
  
  async searchByText(query: string): Promise<Prompt[]> {
    const sql = `
      SELECT *, 
             (CASE 
                WHEN title LIKE ? THEN 3
                WHEN content LIKE ? THEN 2
                WHEN description LIKE ? THEN 1
                ELSE 0
              END) as relevance_score
      FROM ${this.tableName}
      WHERE title LIKE ? OR content LIKE ? OR description LIKE ?
      ORDER BY relevance_score DESC, usage_count DESC
    `;
    
    const searchTerm = `%${query}%`;
    const params = [searchTerm, searchTerm, searchTerm, searchTerm, searchTerm, searchTerm];
    
    const rows = await this.db.all(sql, params);
    return rows.map(row => this.mapRowToEntity(row));
  }
  
  protected mapRowToEntity(row: any): Prompt {
    return {
      id: row.id,
      title: row.title,
      content: row.content,
      description: row.description,
      category: row.category,
      tags: JSON.parse(row.tags || '[]'),
      variables: JSON.parse(row.variables || '[]'),
      metadata: JSON.parse(row.metadata),
      version: row.version,
      parentId: row.parent_id,
      isTemplate: Boolean(row.is_template),
      isFavorite: Boolean(row.is_favorite),
      usage: {
        count: row.usage_count,
        lastUsed: row.last_used_at ? new Date(row.last_used_at) : new Date()
      }
    };
  }
  
  protected generateId(): string {
    return crypto.randomUUID();
  }
  
  protected buildWhereClause(filter: QueryFilter): { whereClause: string; whereParams: any[] } {
    const conditions: string[] = [];
    const params: any[] = [];
    
    Object.entries(filter).forEach(([key, value]) => {
      if (value !== undefined && key !== 'orderBy' && key !== 'orderDirection' && key !== 'limit' && key !== 'offset') {
        conditions.push(`${key} = ?`);
        params.push(value);
      }
    });
    
    return {
      whereClause: conditions.join(' AND '),
      whereParams: params
    };
  }
}
```

## 🔄 Migration Management

### Migration System
```typescript
interface Migration {
  version: number;
  description: string;
  up: (db: Database) => Promise<void>;
  down: (db: Database) => Promise<void>;
}

class MigrationManager {
  private migrations: Migration[] = [];
  
  constructor(private db: Database) {
    this.loadMigrations();
  }
  
  async getCurrentVersion(): Promise<number> {
    try {
      const result = await this.db.get('SELECT version FROM schema_version ORDER BY version DESC LIMIT 1');
      return result?.version || 0;
    } catch {
      return 0;
    }
  }
  
  async migrate(): Promise<void> {
    const currentVersion = await this.getCurrentVersion();
    const pendingMigrations = this.migrations.filter(m => m.version > currentVersion);
    
    if (pendingMigrations.length === 0) {
      return;
    }
    
    await this.db.exec('BEGIN TRANSACTION');
    
    try {
      for (const migration of pendingMigrations) {
        console.log(`Running migration ${migration.version}: ${migration.description}`);
        await migration.up(this.db);
        await this.recordMigration(migration.version, migration.description);
      }
      
      await this.db.exec('COMMIT');
    } catch (error) {
      await this.db.exec('ROLLBACK');
      throw new Error(`Migration failed: ${error.message}`);
    }
  }
  
  private async recordMigration(version: number, description: string): Promise<void> {
    await this.db.run(
      'INSERT INTO schema_version (version, description, applied_at) VALUES (?, ?, CURRENT_TIMESTAMP)',
      [version, description]
    );
  }
  
  private loadMigrations(): void {
    this.migrations = [
      {
        version: 1,
        description: 'Initial schema',
        up: async (db) => {
          await db.exec(`
            CREATE TABLE IF NOT EXISTS schema_version (
              version INTEGER PRIMARY KEY,
              description TEXT NOT NULL,
              applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
          `);
          
          // Create all initial tables
          await this.createInitialTables(db);
        },
        down: async (db) => {
          // Drop all tables
          await db.exec('DROP TABLE IF EXISTS prompts');
          await db.exec('DROP TABLE IF EXISTS categories');
          await db.exec('DROP TABLE IF EXISTS tags');
          await db.exec('DROP TABLE IF EXISTS settings');
          await db.exec('DROP TABLE IF EXISTS usage_analytics');
        }
      },
      
      {
        version: 2,
        description: 'Add full-text search',
        up: async (db) => {
          await db.exec(`
            CREATE VIRTUAL TABLE prompts_fts USING fts5(
              title, content, description, tags,
              content='prompts',
              content_rowid='rowid'
            );
          `);
          
          // Populate FTS table
          await db.exec(`
            INSERT INTO prompts_fts(rowid, title, content, description, tags)
            SELECT rowid, title, content, description, tags FROM prompts;
          `);
          
          // Create triggers to keep FTS in sync
          await db.exec(`
            CREATE TRIGGER prompts_fts_insert AFTER INSERT ON prompts BEGIN
              INSERT INTO prompts_fts(rowid, title, content, description, tags)
              VALUES (new.rowid, new.title, new.content, new.description, new.tags);
            END;
          `);
        },
        down: async (db) => {
          await db.exec('DROP TRIGGER IF EXISTS prompts_fts_insert');
          await db.exec('DROP TABLE IF EXISTS prompts_fts');
        }
      }
    ];
  }
  
  private async createInitialTables(db: Database): Promise<void> {
    // Implementation of initial table creation
    // (SQL statements from the schema section above)
  }
}
```

## 💾 Backup & Restore

### Backup Manager
```typescript
class BackupManager {
  constructor(private db: Database, private config: DatabaseConfig) {}
  
  async createBackup(filePath?: string): Promise<string> {
    const backupPath = filePath || this.generateBackupPath();
    
    await this.db.backup(backupPath);
    
    // Compress backup if enabled
    if (this.config.backup.compress) {
      return await this.compressBackup(backupPath);
    }
    
    return backupPath;
  }
  
  async restoreFromBackup(backupPath: string): Promise<void> {
    // Validate backup file
    await this.validateBackup(backupPath);
    
    // Close current database
    await this.db.close();
    
    // Restore from backup
    await fs.copyFile(backupPath, this.config.path);
    
    // Reopen database
    await this.db.open(this.config.path);
  }
  
  async scheduleBackups(): Promise<void> {
    if (!this.config.backup.enabled) return;
    
    setInterval(async () => {
      try {
        const backupPath = await this.createBackup();
        await this.cleanupOldBackups();
        console.log(`Backup created: ${backupPath}`);
      } catch (error) {
        console.error('Backup failed:', error);
      }
    }, this.config.backup.interval * 60 * 1000);
  }
  
  private generateBackupPath(): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    return path.join(os.tmpdir(), `promptpilot-backup-${timestamp}.db`);
  }
  
  private async cleanupOldBackups(): Promise<void> {
    // Implementation to remove old backups based on maxBackups setting
  }
}
```

## 🧪 Testing Strategy

### Unit Tests
```typescript
describe('StorageModule', () => {
  let storage: StorageModule;
  let testDb: Database;
  
  beforeEach(async () => {
    testDb = new Database(':memory:');
    storage = new StorageModule(testDb);
    await storage.initialize();
  });
  
  afterEach(async () => {
    await storage.close();
  });
  
  test('should save and retrieve prompt', async () => {
    const prompt: Prompt = {
      title: 'Test Prompt',
      content: 'Test content',
      category: 'test',
      tags: ['test'],
      metadata: {
        author: 'test',
        createdAt: new Date(),
        updatedAt: new Date(),
        source: 'user',
        language: 'en',
        estimatedTokens: 10
      },
      version: 1,
      isTemplate: false,
      isFavorite: false,
      usage: { count: 0, lastUsed: new Date() }
    };
    
    const id = await storage.save('prompts', prompt);
    const retrieved = await storage.load<Prompt>('prompts', id);
    
    expect(retrieved?.title).toBe(prompt.title);
    expect(retrieved?.content).toBe(prompt.content);
  });
});
```
