/**
 * Main error handling and management class
 * Centralized error handling with classification, recovery, and reporting
 */

import { EventEmitter } from 'events';
import {
  I<PERSON>rrorHandler,
  IErrorBoundary,
  AppError,
  ErrorDetails,
  ErrorContext,
  ErrorCategory,
  ErrorHandlerConfig,
  ErrorBoundaryState,
  RecoveryHandler,
  ErrorReporter,
  RecoveryOptions,
  ErrorAnalytics
} from './types';
import { ApplicationError, ErrorClassifier } from './ErrorTypes';
import { 
  ConsoleErrorReporter, 
  FileErrorReporter, 
  ErrorAnalyticsTracker, 
  ErrorNotificationManager 
} from './ErrorReporter';
import { LoggerFactory } from '../logger';

const logger = LoggerFactory.getInstance().getLogger('ErrorHandler');

/**
 * Recovery handler for retryable operations
 */
export class RetryRecoveryHandler implements RecoveryHandler {
  canRecover(error: AppError): boolean {
    return error.recoverable && 
           ['network', 'ai_provider', 'stt_provider', 'database'].includes(error.category);
  }

  async recover(error: AppError, options?: RecoveryOptions): Promise<boolean> {
    const maxRetries = options?.maxRetries || 3;
    const retryDelay = options?.retryDelay || 1000;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        logger.info(`Retry attempt ${attempt}/${maxRetries} for error ${error.id}`);
        
        // Wait before retry
        if (attempt > 1) {
          await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
        }

        // Execute fallback action if provided
        if (options?.fallbackAction) {
          await options.fallbackAction();
          return true;
        }

        // If no fallback action, consider it recovered
        return true;
      } catch (retryError) {
        logger.warn(`Retry attempt ${attempt} failed`, retryError);
        
        if (attempt === maxRetries) {
          logger.error(`All retry attempts failed for error ${error.id}`);
          return false;
        }
      }
    }

    return false;
  }
}

/**
 * Main error handler implementation
 */
export class ErrorHandler extends EventEmitter implements IErrorHandler {
  private config: ErrorHandlerConfig;
  private recoveryHandlers: Map<ErrorCategory, RecoveryHandler> = new Map();
  private reporter?: ErrorReporter;
  private analyticsTracker: ErrorAnalyticsTracker;
  private notificationManager: ErrorNotificationManager;
  private errorQueue: AppError[] = [];
  private processingQueue: boolean = false;

  constructor(config?: Partial<ErrorHandlerConfig>) {
    super();
    
    this.config = {
      enableReporting: true,
      enableRecovery: true,
      maxRetries: 3,
      retryDelay: 1000,
      enableUserNotification: true,
      enableAnalytics: true,
      logLevel: 'error',
      ...config
    };

    this.analyticsTracker = new ErrorAnalyticsTracker();
    this.notificationManager = new ErrorNotificationManager();

    // Set up default recovery handlers
    this.setupDefaultRecoveryHandlers();

    // Set up event listeners
    this.setupEventListeners();
  }

  /**
   * Handle an error
   */
  async handle(error: Error | AppError, context?: Partial<ErrorContext>): Promise<void> {
    try {
      // Convert to AppError if needed
      const appError = this.ensureAppError(error, context);

      // Log the error
      this.logError(appError);

      // Track analytics
      if (this.config.enableAnalytics) {
        this.analyticsTracker.track(appError);
      }

      // Add to processing queue
      this.errorQueue.push(appError);
      
      // Process queue if not already processing
      if (!this.processingQueue) {
        await this.processErrorQueue();
      }

      // Emit error event
      this.emit('error-handled', appError);
    } catch (handlingError) {
      logger.error('Error occurred while handling error', handlingError);
      console.error('Critical: Error handler failed', handlingError);
    }
  }

  /**
   * Create a new application error
   */
  createError(
    details: Partial<ErrorDetails>, 
    context?: Partial<ErrorContext>, 
    cause?: Error
  ): AppError {
    return new ApplicationError(details, context || {}, cause);
  }

  /**
   * Register a recovery handler for a specific error category
   */
  registerRecoveryHandler(category: ErrorCategory, handler: RecoveryHandler): void {
    this.recoveryHandlers.set(category, handler);
    logger.info(`Recovery handler registered for category: ${category}`);
  }

  /**
   * Set the error reporter
   */
  setReporter(reporter: ErrorReporter): void {
    this.reporter = reporter;
    if (this.config.reportingEndpoint) {
      reporter.configure({ endpoint: this.config.reportingEndpoint });
    }
    logger.info('Error reporter configured');
  }

  /**
   * Configure the error handler
   */
  configure(config: Partial<ErrorHandlerConfig>): void {
    this.config = { ...this.config, ...config };
    
    // Reconfigure components
    this.analyticsTracker.configure(config);
    this.notificationManager.configure(config);
    
    if (this.reporter) {
      this.reporter.configure(config);
    }

    logger.info('Error handler reconfigured', config);
  }

  /**
   * Get error analytics
   */
  async getAnalytics(timeRange?: { start: Date; end: Date }): Promise<ErrorAnalytics[]> {
    return this.analyticsTracker.getAnalytics(timeRange);
  }

  /**
   * Process the error queue
   */
  private async processErrorQueue(): Promise<void> {
    if (this.processingQueue || this.errorQueue.length === 0) {
      return;
    }

    this.processingQueue = true;

    try {
      while (this.errorQueue.length > 0) {
        const error = this.errorQueue.shift()!;
        await this.processError(error);
      }
    } finally {
      this.processingQueue = false;
    }
  }

  /**
   * Process a single error
   */
  private async processError(error: AppError): Promise<void> {
    try {
      // Attempt recovery if enabled
      if (this.config.enableRecovery) {
        const recovered = await this.attemptRecovery(error);
        if (recovered) {
          logger.info(`Error ${error.id} recovered successfully`);
          this.emit('error-recovered', error);
          return;
        }
      }

      // Report the error if enabled
      if (this.config.enableReporting && this.reporter) {
        await this.reporter.report(error);
      }

      // Show user notification if enabled
      if (this.config.enableUserNotification) {
        this.notificationManager.notify(error);
      }

      this.emit('error-processed', error);
    } catch (processingError) {
      logger.error('Error occurred while processing error', processingError);
    }
  }

  /**
   * Attempt to recover from an error
   */
  private async attemptRecovery(error: AppError): Promise<boolean> {
    const handler = this.recoveryHandlers.get(error.category);
    
    if (!handler || !handler.canRecover(error)) {
      return false;
    }

    try {
      const options: RecoveryOptions = {
        strategy: 'retry',
        maxRetries: this.config.maxRetries,
        retryDelay: this.config.retryDelay,
        autoRecover: true
      };

      return await handler.recover(error, options);
    } catch (recoveryError) {
      logger.error(`Recovery failed for error ${error.id}`, recoveryError);
      return false;
    }
  }

  /**
   * Ensure error is an AppError instance
   */
  private ensureAppError(error: Error | AppError, context?: Partial<ErrorContext>): AppError {
    if (error instanceof ApplicationError) {
      return error;
    }

    // Classify the error
    const { category, severity } = ErrorClassifier.classify(error);
    const recoverable = ErrorClassifier.isRecoverable(error);

    return new ApplicationError(
      {
        message: error.message,
        category,
        severity,
        recoverable,
        technicalMessage: error.stack
      },
      context || {},
      error
    );
  }

  /**
   * Log an error based on configuration
   */
  private logError(error: AppError): void {
    const logMessage = `Error ${error.id}: ${error.message}`;
    const logData = {
      id: error.id,
      code: error.code,
      category: error.category,
      severity: error.severity,
      context: error.context
    };

    switch (error.severity) {
      case 'critical':
        logger.error(logMessage, logData);
        break;
      case 'high':
        logger.error(logMessage, logData);
        break;
      case 'medium':
        logger.warn(logMessage, logData);
        break;
      case 'low':
        if (this.config.logLevel === 'debug') {
          logger.info(logMessage, logData);
        }
        break;
    }
  }

  /**
   * Set up default recovery handlers
   */
  private setupDefaultRecoveryHandlers(): void {
    const retryHandler = new RetryRecoveryHandler();
    
    // Register retry handler for recoverable categories
    this.registerRecoveryHandler('network', retryHandler);
    this.registerRecoveryHandler('ai_provider', retryHandler);
    this.registerRecoveryHandler('stt_provider', retryHandler);
    this.registerRecoveryHandler('database', retryHandler);
  }

  /**
   * Set up event listeners
   */
  private setupEventListeners(): void {
    // Listen for retry requests from notifications
    this.notificationManager.on('retry-requested', async (error: AppError) => {
      logger.info(`Retry requested for error ${error.id}`);
      await this.handle(error);
    });

    // Listen for help requests
    this.notificationManager.on('help-requested', (error: AppError) => {
      logger.info(`Help requested for error ${error.id}`);
      this.emit('help-requested', error);
    });

    // Note: Global process listeners are not set up in constructor to avoid memory leaks
    // They should be set up explicitly when needed using setupGlobalErrorHandling()
  }

  /**
   * Set up global process error listeners
   */
  setupGlobalListeners(): void {
    // Handle uncaught exceptions
    process.on('uncaughtException', (error: Error) => {
      logger.error('Uncaught exception', error);
      this.handle(error, { component: 'process', operation: 'uncaught-exception' });
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
      logger.error('Unhandled promise rejection', reason);
      const error = reason instanceof Error ? reason : new Error(String(reason));
      this.handle(error, { component: 'process', operation: 'unhandled-rejection' });
    });
  }
}

// Global error handler instance
let globalErrorHandler: ErrorHandler | null = null;

/**
 * Get the global error handler instance
 */
export const getErrorHandler = (): ErrorHandler => {
  if (!globalErrorHandler) {
    globalErrorHandler = new ErrorHandler();
    
    // Set up default console reporter for development
    globalErrorHandler.setReporter(new ConsoleErrorReporter());
  }
  return globalErrorHandler;
};

/**
 * Initialize the global error handler
 */
export const initializeErrorHandler = (config?: Partial<ErrorHandlerConfig>): ErrorHandler => {
  globalErrorHandler = new ErrorHandler(config);
  return globalErrorHandler;
};

/**
 * Error boundary for component-level error handling
 */
export class ErrorBoundary extends EventEmitter implements IErrorBoundary {
  private _state: ErrorBoundaryState;
  private errorHandler: ErrorHandler;
  private componentName: string;
  private options: any;

  constructor(componentName: string, errorHandler?: ErrorHandler, options?: any) {
    super();
    this.componentName = componentName;
    this.errorHandler = errorHandler || getErrorHandler();
    this.options = options || {};

    this._state = {
      hasError: false,
      retryCount: 0
    };
  }

  get state(): ErrorBoundaryState {
    return { ...this._state };
  }

  /**
   * Catch and handle an error
   */
  catch(error: Error, errorInfo?: any): void {
    const appError = this.errorHandler.createError(
      {
        message: error.message,
        technicalMessage: error.stack
      },
      {
        component: this.componentName,
        operation: 'component-error',
        metadata: { errorInfo }
      },
      error
    );

    this._state = {
      hasError: true,
      error: appError,
      errorInfo,
      retryCount: this._state.retryCount,
      lastRetry: this._state.lastRetry
    };

    // Handle the error
    this.errorHandler.handle(appError);

    this.emit('error-caught', { error: appError, errorInfo });
  }

  /**
   * Retry the failed operation
   */
  async retry(): Promise<void> {
    if (!this._state.hasError) {
      return;
    }

    this._state = {
      ...this._state,
      retryCount: this._state.retryCount + 1,
      lastRetry: new Date()
    };

    this.emit('retry-attempted', this._state);

    // Reset error state to allow retry
    this.reset();
  }

  /**
   * Reset the error boundary state
   */
  reset(): void {
    this._state = {
      hasError: false,
      retryCount: this._state.retryCount
    };

    this.emit('state-reset');
  }

  /**
   * Configure the error boundary
   */
  configure(options: any): void {
    this.options = { ...this.options, ...options };
  }
}
