/**
 * Main Floating Window implementation
 * Floating window management and UI capabilities
 */

import { EventEmitter } from 'events';
import {
  IFloatingWindow,
  FloatingWindowConfig,
  WindowShowOptions,
  WindowContent,
  WindowState,
  // WindowPosition,
  // WindowSize,
  FloatingWindowError,
  WindowOperationError,
  // WindowEvent,
  WindowEventData
} from './types';
import { WindowRenderer } from './WindowRenderer';
import { ThemeManager } from './ThemeManager';
import { AnimationManager } from './AnimationManager';
import { LoggerFactory } from '../../core/logger';
import { toError } from '../../core/utils/errorUtils';

const logger = LoggerFactory.getInstance().getLogger('FloatingWindow');

/**
 * Main Floating Window implementation
 */
export class FloatingWindow extends EventEmitter implements IFloatingWindow {
  private config: FloatingWindowConfig;
  private windowId: string;
  private state: WindowState;
  private renderer!: WindowRenderer;
  private themeManager!: ThemeManager;
  private animationManager!: AnimationManager;
  private isInitialized: boolean = false;
  private autoHideTimeout: NodeJS.Timeout | undefined;

  constructor(windowId: string, config: FloatingWindowConfig) {
    super();
    
    this.windowId = windowId;
    this.config = config;
    
    this.state = {
      isVisible: false,
      isMinimized: false,
      isMaximized: false,
      isFocused: false,
      position: {
        x: config.window.x || 100,
        y: config.window.y || 100
      },
      size: {
        width: config.window.width,
        height: config.window.height
      },
      opacity: config.window.opacity,
      zIndex: 1000
    };

    this.initializeComponents();
  }

  /**
   * Initialize components
   */
  private initializeComponents(): void {
    this.renderer = new WindowRenderer(this.windowId, this.config);
    this.themeManager = new ThemeManager(this.config.ui.theme);
    this.animationManager = new AnimationManager();
  }

  /**
   * Initialize the floating window
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      await this.renderer.initialize();
      await this.themeManager.initialize();
      
      this.setupEventHandlers();
      this.isInitialized = true;
      
      this.emit('window-created', {
        windowId: this.windowId,
        event: 'window-created',
        timestamp: new Date()
      } as WindowEventData);

      logger.info('Floating window initialized', { windowId: this.windowId });
    } catch (error) {
      logger.error('Failed to initialize floating window', { error, windowId: this.windowId });
      throw new FloatingWindowError('Failed to initialize floating window', toError(error));
    }
  }

  /**
   * Show the window
   */
  async show(options: WindowShowOptions = {}): Promise<void> {
    try {
      if (this.state.isVisible) {
        return;
      }

      // Apply show options
      if (options.position) {
        await this.setPosition(options.position.x, options.position.y);
      }

      if (options.size) {
        await this.setSize(options.size.width, options.size.height);
      }

      if (options.center) {
        await this.centerWindow();
      }

      // Show with animation
      if (options.animation && options.animation.type !== 'none') {
        await this.animationManager.animate('show', options.animation);
      }

      await this.renderer.show();
      
      this.state.isVisible = true;
      this.state.isMinimized = false;

      if (options.focus !== false) {
        await this.focus();
      }

      // Setup auto-hide if enabled
      if (this.config.behavior.autoHide) {
        this.setupAutoHide();
      }

      this.emit('window-shown', {
        windowId: this.windowId,
        event: 'window-shown',
        data: options,
        timestamp: new Date()
      } as WindowEventData);

      logger.info('Floating window shown', { windowId: this.windowId, options });
    } catch (error) {
      logger.error('Failed to show floating window', { error, windowId: this.windowId });
      throw new WindowOperationError('Failed to show floating window', toError(error));
    }
  }

  /**
   * Hide the window
   */
  async hide(): Promise<void> {
    try {
      if (!this.state.isVisible) {
        return;
      }

      // Hide with animation if enabled
      if (this.config.behavior.fadeInOut) {
        await this.animationManager.animate('hide', {
          type: 'fade',
          duration: 200,
          easing: 'ease-out'
        });
      }

      await this.renderer.hide();
      
      this.state.isVisible = false;
      this.state.isFocused = false;

      // Clear auto-hide timeout
      if (this.autoHideTimeout) {
        clearTimeout(this.autoHideTimeout);
        this.autoHideTimeout = undefined;
      }

      this.emit('window-hidden', {
        windowId: this.windowId,
        event: 'window-hidden',
        timestamp: new Date()
      } as WindowEventData);

      logger.info('Floating window hidden', { windowId: this.windowId });
    } catch (error) {
      logger.error('Failed to hide floating window', { error, windowId: this.windowId });
      throw new WindowOperationError('Failed to hide floating window', toError(error));
    }
  }

  /**
   * Toggle window visibility
   */
  async toggle(): Promise<void> {
    if (this.state.isVisible) {
      await this.hide();
    } else {
      await this.show();
    }
  }

  /**
   * Check if window is visible
   */
  isVisible(): boolean {
    return this.state.isVisible;
  }

  /**
   * Set window position
   */
  async setPosition(x: number, y: number): Promise<void> {
    try {
      await this.renderer.setPosition(x, y);
      
      this.state.position = { x, y };

      this.emit('window-moved', {
        windowId: this.windowId,
        event: 'window-moved',
        data: { x, y },
        timestamp: new Date()
      } as WindowEventData);

      logger.debug('Window position set', { windowId: this.windowId, x, y });
    } catch (error) {
      logger.error('Failed to set window position', { error, windowId: this.windowId });
      throw new WindowOperationError('Failed to set window position', toError(error));
    }
  }

  /**
   * Set window size
   */
  async setSize(width: number, height: number): Promise<void> {
    try {
      // Enforce size constraints
      const constrainedWidth = Math.max(
        this.config.window.minWidth,
        Math.min(width, this.config.window.maxWidth || Infinity)
      );
      
      const constrainedHeight = Math.max(
        this.config.window.minHeight,
        Math.min(height, this.config.window.maxHeight || Infinity)
      );

      await this.renderer.setSize(constrainedWidth, constrainedHeight);
      
      this.state.size = { width: constrainedWidth, height: constrainedHeight };

      this.emit('window-resized', {
        windowId: this.windowId,
        event: 'window-resized',
        data: { width: constrainedWidth, height: constrainedHeight },
        timestamp: new Date()
      } as WindowEventData);

      logger.debug('Window size set', { 
        windowId: this.windowId, 
        width: constrainedWidth, 
        height: constrainedHeight 
      });
    } catch (error) {
      logger.error('Failed to set window size', { error, windowId: this.windowId });
      throw new WindowOperationError('Failed to set window size', toError(error));
    }
  }

  /**
   * Set window content
   */
  async setContent(content: WindowContent): Promise<void> {
    try {
      await this.renderer.setContent(content);

      this.emit('content-changed', {
        windowId: this.windowId,
        event: 'content-changed',
        data: content,
        timestamp: new Date()
      } as WindowEventData);

      logger.debug('Window content set', { windowId: this.windowId, contentType: content.type });
    } catch (error) {
      logger.error('Failed to set window content', { error, windowId: this.windowId });
      throw new WindowOperationError('Failed to set window content', toError(error));
    }
  }

  /**
   * Focus the window
   */
  async focus(): Promise<void> {
    try {
      await this.renderer.focus();
      
      this.state.isFocused = true;

      this.emit('window-focused', {
        windowId: this.windowId,
        event: 'window-focused',
        timestamp: new Date()
      } as WindowEventData);

      logger.debug('Window focused', { windowId: this.windowId });
    } catch (error) {
      logger.error('Failed to focus window', { error, windowId: this.windowId });
      throw new WindowOperationError('Failed to focus window', toError(error));
    }
  }

  /**
   * Minimize the window
   */
  async minimize(): Promise<void> {
    try {
      await this.renderer.minimize();
      
      this.state.isMinimized = true;
      this.state.isFocused = false;

      this.emit('window-minimized', {
        windowId: this.windowId,
        event: 'window-minimized',
        timestamp: new Date()
      } as WindowEventData);

      logger.debug('Window minimized', { windowId: this.windowId });
    } catch (error) {
      logger.error('Failed to minimize window', { error, windowId: this.windowId });
      throw new WindowOperationError('Failed to minimize window', toError(error));
    }
  }

  /**
   * Maximize the window
   */
  async maximize(): Promise<void> {
    try {
      await this.renderer.maximize();
      
      this.state.isMaximized = true;

      this.emit('window-maximized', {
        windowId: this.windowId,
        event: 'window-maximized',
        timestamp: new Date()
      } as WindowEventData);

      logger.debug('Window maximized', { windowId: this.windowId });
    } catch (error) {
      logger.error('Failed to maximize window', { error, windowId: this.windowId });
      throw new WindowOperationError('Failed to maximize window', toError(error));
    }
  }

  /**
   * Restore the window
   */
  async restore(): Promise<void> {
    try {
      await this.renderer.restore();
      
      this.state.isMinimized = false;
      this.state.isMaximized = false;

      this.emit('window-restored', {
        windowId: this.windowId,
        event: 'window-restored',
        timestamp: new Date()
      } as WindowEventData);

      logger.debug('Window restored', { windowId: this.windowId });
    } catch (error) {
      logger.error('Failed to restore window', { error, windowId: this.windowId });
      throw new WindowOperationError('Failed to restore window', toError(error));
    }
  }

  /**
   * Close the window
   */
  async close(): Promise<void> {
    try {
      await this.hide();
      await this.renderer.destroy();
      
      this.emit('window-destroyed', {
        windowId: this.windowId,
        event: 'window-destroyed',
        timestamp: new Date()
      } as WindowEventData);

      this.removeAllListeners();
      
      logger.info('Floating window closed', { windowId: this.windowId });
    } catch (error) {
      logger.error('Failed to close window', { error, windowId: this.windowId });
      throw new WindowOperationError('Failed to close window', toError(error));
    }
  }

  /**
   * Get window state
   */
  getState(): WindowState {
    return { ...this.state };
  }

  /**
   * Get window ID
   */
  getId(): string {
    return this.windowId;
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<FloatingWindowConfig>): void {
    this.config = { ...this.config, ...config };
    
    if (config.ui?.theme) {
      this.themeManager.setTheme(config.ui.theme);
    }

    logger.debug('Window configuration updated', { windowId: this.windowId });
  }

  /**
   * Center window on screen
   */
  private async centerWindow(): Promise<void> {
    // In a real implementation, this would get screen dimensions
    const screenWidth = 1920;
    const screenHeight = 1080;
    
    const x = Math.floor((screenWidth - this.state.size.width) / 2);
    const y = Math.floor((screenHeight - this.state.size.height) / 2);
    
    await this.setPosition(x, y);
  }

  /**
   * Setup auto-hide functionality
   */
  private setupAutoHide(): void {
    if (this.autoHideTimeout) {
      clearTimeout(this.autoHideTimeout);
    }

    this.autoHideTimeout = setTimeout(() => {
      if (this.state.isVisible && !this.state.isFocused) {
        this.hide().catch(error => {
          logger.error('Auto-hide failed', { error, windowId: this.windowId });
        });
      }
    }, this.config.behavior.autoHideDelay);
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    // Handle renderer events
    this.renderer.on('blur', () => {
      this.state.isFocused = false;
      this.emit('window-blurred', {
        windowId: this.windowId,
        event: 'window-blurred',
        timestamp: new Date()
      } as WindowEventData);
    });

    this.renderer.on('focus', () => {
      this.state.isFocused = true;
      if (this.autoHideTimeout) {
        clearTimeout(this.autoHideTimeout);
        this.autoHideTimeout = undefined;
      }
    });
  }
}
