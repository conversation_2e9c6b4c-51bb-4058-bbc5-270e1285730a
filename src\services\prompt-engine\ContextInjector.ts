/**
 * Context Injector implementation
 * Handles context extraction and injection into prompts
 */

import {
  ContextSource,
  ContextData,
  ContextExtractionOptions,
  ContextExtractionError
} from './types';
import { getContextExtractor } from '../context-extractor';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('ContextInjector');

export interface ContextConfig {
  maxSources: number;
  maxContentLength: number;
  enableSensitiveFilter: boolean;
}

/**
 * Context Injector implementation
 */
export class ContextInjector {
  private config: ContextConfig;
  private contextExtractor: any;

  constructor(config: ContextConfig) {
    this.config = config;
    this.contextExtractor = getContextExtractor();
  }

  /**
   * Inject context into a prompt
   */
  async injectContext(prompt: string, contextSources: ContextSource[]): Promise<string> {
    try {
      // Limit number of sources
      const limitedSources = contextSources.slice(0, this.config.maxSources);
      
      const contextData: ContextData[] = [];

      for (const source of limitedSources) {
        try {
          const data = await this.extractContext(source);
          if (data) {
            contextData.push(data);
          }
        } catch (error) {
          logger.warn(`Failed to extract context from ${source}`, { error });
        }
      }

      if (contextData.length === 0) {
        return prompt;
      }

      return this.buildContextualPrompt(prompt, contextData);
    } catch (error) {
      logger.error('Failed to inject context', { error, contextSources });
      throw new ContextExtractionError('Failed to inject context', error);
    }
  }

  /**
   * Extract context from a specific source
   */
  private async extractContext(source: ContextSource): Promise<ContextData | null> {
    try {
      switch (source) {
        case ContextSource.CLIPBOARD:
          return await this.extractClipboardContext();
          
        case ContextSource.SCREEN_CAPTURE:
          return await this.extractScreenContext();
          
        case ContextSource.ACTIVE_WINDOW:
          return await this.extractActiveWindowContext();
          
        case ContextSource.SELECTED_TEXT:
          return await this.extractSelectedTextContext();
          
        case ContextSource.SYSTEM_INFO:
          return await this.extractSystemInfoContext();
          
        default:
          logger.warn('Unsupported context source', { source });
          return null;
      }
    } catch (error) {
      logger.error('Context extraction failed', { error, source });
      throw new ContextExtractionError(`Failed to extract context from ${source}`, error);
    }
  }

  /**
   * Extract clipboard context
   */
  private async extractClipboardContext(): Promise<ContextData | null> {
    try {
      const clipboardText = await this.contextExtractor.getClipboardText();
      
      if (!clipboardText || clipboardText.trim().length === 0) {
        return null;
      }

      const content = this.processContent(clipboardText);
      
      return {
        source: ContextSource.CLIPBOARD,
        content,
        metadata: {
          timestamp: new Date(),
          sourceInfo: { type: 'text' },
          size: content.length
        }
      };
    } catch (error) {
      logger.error('Failed to extract clipboard context', { error });
      return null;
    }
  }

  /**
   * Extract screen capture context
   */
  private async extractScreenContext(): Promise<ContextData | null> {
    try {
      const screenText = await this.contextExtractor.captureScreenText();
      
      if (!screenText || screenText.trim().length === 0) {
        return null;
      }

      const content = this.processContent(screenText);
      
      return {
        source: ContextSource.SCREEN_CAPTURE,
        content,
        metadata: {
          timestamp: new Date(),
          sourceInfo: { type: 'screen-ocr' },
          size: content.length
        }
      };
    } catch (error) {
      logger.error('Failed to extract screen context', { error });
      return null;
    }
  }

  /**
   * Extract active window context
   */
  private async extractActiveWindowContext(): Promise<ContextData | null> {
    try {
      const windowInfo = await this.contextExtractor.getActiveWindowInfo();
      
      if (!windowInfo) {
        return null;
      }

      const content = this.formatWindowInfo(windowInfo);
      
      return {
        source: ContextSource.ACTIVE_WINDOW,
        content,
        metadata: {
          timestamp: new Date(),
          sourceInfo: windowInfo,
          size: content.length
        }
      };
    } catch (error) {
      logger.error('Failed to extract active window context', { error });
      return null;
    }
  }

  /**
   * Extract selected text context
   */
  private async extractSelectedTextContext(): Promise<ContextData | null> {
    try {
      const selectedText = await this.contextExtractor.getSelectedText();
      
      if (!selectedText || selectedText.trim().length === 0) {
        return null;
      }

      const content = this.processContent(selectedText);
      
      return {
        source: ContextSource.SELECTED_TEXT,
        content,
        metadata: {
          timestamp: new Date(),
          sourceInfo: { type: 'selection' },
          size: content.length
        }
      };
    } catch (error) {
      logger.error('Failed to extract selected text context', { error });
      return null;
    }
  }

  /**
   * Extract system info context
   */
  private async extractSystemInfoContext(): Promise<ContextData | null> {
    try {
      const systemInfo = await this.contextExtractor.getSystemInfo();
      
      if (!systemInfo) {
        return null;
      }

      const content = this.formatSystemInfo(systemInfo);
      
      return {
        source: ContextSource.SYSTEM_INFO,
        content,
        metadata: {
          timestamp: new Date(),
          sourceInfo: systemInfo,
          size: content.length
        }
      };
    } catch (error) {
      logger.error('Failed to extract system info context', { error });
      return null;
    }
  }

  /**
   * Process and clean content
   */
  private processContent(content: string): string {
    let processed = content.trim();

    // Apply length limit
    if (processed.length > this.config.maxContentLength) {
      processed = processed.substring(0, this.config.maxContentLength) + '...';
    }

    // Apply sensitive content filter if enabled
    if (this.config.enableSensitiveFilter) {
      processed = this.filterSensitiveContent(processed);
    }

    return processed;
  }

  /**
   * Filter sensitive content
   */
  private filterSensitiveContent(content: string): string {
    // Simple sensitive content filtering
    const sensitivePatterns = [
      /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g, // Credit card numbers
      /\b\d{3}-\d{2}-\d{4}\b/g, // SSN
      /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, // Email addresses
      /\b(?:\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b/g // Phone numbers
    ];

    let filtered = content;
    for (const pattern of sensitivePatterns) {
      filtered = filtered.replace(pattern, '[REDACTED]');
    }

    return filtered;
  }

  /**
   * Format window information
   */
  private formatWindowInfo(windowInfo: any): string {
    const parts = [];
    
    if (windowInfo.title) {
      parts.push(`Window: ${windowInfo.title}`);
    }
    
    if (windowInfo.application) {
      parts.push(`Application: ${windowInfo.application}`);
    }
    
    if (windowInfo.url) {
      parts.push(`URL: ${windowInfo.url}`);
    }
    
    if (windowInfo.text) {
      parts.push(`Content: ${windowInfo.text}`);
    }

    return parts.join('\n');
  }

  /**
   * Format system information
   */
  private formatSystemInfo(systemInfo: any): string {
    const parts = [];
    
    if (systemInfo.platform) {
      parts.push(`Platform: ${systemInfo.platform}`);
    }
    
    if (systemInfo.version) {
      parts.push(`Version: ${systemInfo.version}`);
    }
    
    if (systemInfo.currentTime) {
      parts.push(`Current Time: ${systemInfo.currentTime}`);
    }
    
    if (systemInfo.timezone) {
      parts.push(`Timezone: ${systemInfo.timezone}`);
    }

    return parts.join('\n');
  }

  /**
   * Build contextual prompt with injected context
   */
  private buildContextualPrompt(prompt: string, contextData: ContextData[]): string {
    let contextualPrompt = prompt;

    if (contextData.length > 0) {
      contextualPrompt += '\n\n--- Context Information ---\n';

      contextData.forEach((data, index) => {
        const sourceLabel = this.getSourceLabel(data.source);
        contextualPrompt += `\n${index + 1}. ${sourceLabel}:\n${data.content}\n`;
      });

      contextualPrompt += '\n--- End Context ---\n';
      contextualPrompt += 'Please consider the above context when responding.';
    }

    return contextualPrompt;
  }

  /**
   * Get human-readable source label
   */
  private getSourceLabel(source: ContextSource): string {
    const labels = {
      [ContextSource.CLIPBOARD]: 'Clipboard Content',
      [ContextSource.SCREEN_CAPTURE]: 'Screen Capture',
      [ContextSource.ACTIVE_WINDOW]: 'Active Window',
      [ContextSource.SELECTED_TEXT]: 'Selected Text',
      [ContextSource.SYSTEM_INFO]: 'System Information',
      [ContextSource.FILE_CONTENT]: 'File Content',
      [ContextSource.CONVERSATION_HISTORY]: 'Conversation History',
      [ContextSource.BROWSER_TAB]: 'Browser Tab'
    };

    return labels[source] || source;
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<ContextConfig>): void {
    this.config = { ...this.config, ...config };
    logger.info('Context injector configuration updated');
  }
}
