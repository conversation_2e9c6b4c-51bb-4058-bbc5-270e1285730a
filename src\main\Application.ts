/**
 * Main Application Class
 * Orchestrates all modules and manages application lifecycle
 */

import { EventEmitter } from 'events';
import { LoggerFactory } from '../core/logger';
import { getConfigManager } from '../core/config';
import { getStorageManager } from '../storage/StorageManager';
import { initializePromptLibrary } from '../services/prompt-library';
import { initializePromptEngine } from '../services/prompt-engine';
// Voice module will be implemented later
// import { initializeVoiceModule } from '../services/voice-module';
import { initializeHotkeyManager } from '../services/hotkey-manager';
import { initializeContextExtractor } from '../services/context-extractor';
import { initializeWindowManager } from '../services/floating-window';

const logger = LoggerFactory.getInstance().getLogger('Application');

export interface ApplicationConfig {
  database: {
    path: string;
    enableWAL: boolean;
    enableForeignKeys: boolean;
  };
  logging: {
    level: 'debug' | 'info' | 'warn' | 'error';
    enableFileLogging: boolean;
    logDirectory: string;
  };
  hotkeys: {
    enableGlobalHotkeys: boolean;
    defaultHotkeys: Record<string, string>;
  };
  ui: {
    theme: 'light' | 'dark' | 'auto';
    enableAnimations: boolean;
    windowOpacity: number;
  };
  voice: {
    enableVoiceInput: boolean;
    defaultLanguage: string;
    enableContinuousListening: boolean;
  };
  context: {
    enableScreenCapture: boolean;
    enableClipboardMonitoring: boolean;
    ocrLanguage: string;
  };
  ai: {
    defaultProvider: string;
    enableStreaming: boolean;
    maxTokens: number;
  };
}

/**
 * Main Application implementation
 */
export class Application extends EventEmitter {
  private config: ApplicationConfig;
  private isInitialized: boolean = false;
  private isRunning: boolean = false;
  private modules: Map<string, any> = new Map();

  constructor(config?: Partial<ApplicationConfig>) {
    super();
    
    this.config = {
      database: {
        path: './data/personal-prompt.db',
        enableWAL: true,
        enableForeignKeys: true,
        ...config?.database
      },
      logging: {
        level: 'info',
        enableFileLogging: true,
        logDirectory: './logs',
        ...config?.logging
      },
      hotkeys: {
        enableGlobalHotkeys: true,
        defaultHotkeys: {
          'show-window': 'Ctrl+Shift+P',
          'capture-screen': 'Ctrl+Shift+O',
          'voice-input': 'Ctrl+Shift+V',
          'show-library': 'Ctrl+Shift+L'
        },
        ...config?.hotkeys
      },
      ui: {
        theme: 'auto',
        enableAnimations: true,
        windowOpacity: 0.95,
        ...config?.ui
      },
      voice: {
        enableVoiceInput: true,
        defaultLanguage: 'en-US',
        enableContinuousListening: false,
        ...config?.voice
      },
      context: {
        enableScreenCapture: true,
        enableClipboardMonitoring: false,
        ocrLanguage: 'eng',
        ...config?.context
      },
      ai: {
        defaultProvider: 'openai',
        enableStreaming: true,
        maxTokens: 4000,
        ...config?.ai
      }
    };
  }

  /**
   * Initialize the application
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      logger.info('Initializing Personal Prompt Application...');

      // Initialize core systems
      await this.initializeCore();

      // Initialize services
      await this.initializeServices();

      // Setup global event handlers
      this.setupGlobalEventHandlers();

      // Register default hotkeys
      await this.registerDefaultHotkeys();

      this.isInitialized = true;
      
      this.emit('initialized');
      logger.info('Application initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize application', { error });
      throw error;
    }
  }

  /**
   * Start the application
   */
  async start(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (this.isRunning) {
      return;
    }

    try {
      logger.info('Starting Personal Prompt Application...');

      // Start all modules
      await this.startModules();

      this.isRunning = true;
      
      this.emit('started');
      logger.info('Application started successfully');
    } catch (error) {
      logger.error('Failed to start application', { error });
      throw error;
    }
  }

  /**
   * Stop the application
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    try {
      logger.info('Stopping Personal Prompt Application...');

      // Stop all modules
      await this.stopModules();

      this.isRunning = false;
      
      this.emit('stopped');
      logger.info('Application stopped successfully');
    } catch (error) {
      logger.error('Failed to stop application', { error });
      throw error;
    }
  }

  /**
   * Restart the application
   */
  async restart(): Promise<void> {
    await this.stop();
    await this.start();
    
    this.emit('restarted');
    logger.info('Application restarted');
  }

  /**
   * Shutdown the application
   */
  async shutdown(): Promise<void> {
    try {
      logger.info('Shutting down Personal Prompt Application...');

      await this.stop();
      await this.cleanup();

      this.emit('shutdown');
      logger.info('Application shutdown complete');
    } catch (error) {
      logger.error('Error during application shutdown', { error });
      throw error;
    }
  }

  /**
   * Get application status
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isRunning: this.isRunning,
      modules: Array.from(this.modules.keys()),
      config: this.config,
      uptime: this.isRunning ? Date.now() - this.getStartTime() : 0
    };
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<ApplicationConfig>): void {
    this.config = { ...this.config, ...config };
    
    // Update module configurations
    this.updateModuleConfigurations();
    
    this.emit('config-updated', config);
    logger.info('Application configuration updated');
  }

  /**
   * Get module by name
   */
  getModule(name: string): any {
    return this.modules.get(name);
  }

  /**
   * Initialize core systems
   */
  private async initializeCore(): Promise<void> {
    // Initialize logger
    // LoggerFactory.getInstance().setLevel(this.config.logging.level); // Method not available
    
    // Initialize configuration manager
    const configManager = getConfigManager();
    // await configManager.initialize(); // Method not available

    // Initialize database
    const storageManager = getStorageManager();
    await storageManager.initialize();
    
    logger.info('Core systems initialized');
  }

  /**
   * Initialize services
   */
  private async initializeServices(): Promise<void> {
    try {
      // Initialize Prompt Library
      const promptLibrary = await initializePromptLibrary();
      this.modules.set('prompt-library', promptLibrary);

      // Initialize Prompt Engine
      const promptEngine = await initializePromptEngine({
        ai: {
          provider: this.config.ai.defaultProvider as any,
          defaultModel: 'gpt-3.5-turbo', // Default model
          timeout: 30000,
          retries: 3
        },
        cache: {
          ttl: 300000, // 5 minutes
          maxEntries: 1000,
          maxSize: 50 * 1024 * 1024 // 50MB
        },
        templates: {
          builtInEnabled: true,
          autoSave: true
        },
        context: {
          maxSources: 10,
          maxContentLength: 10000,
          enableSensitiveFilter: true
        },
        enhancement: {
          defaultStyle: 'professional',
          defaultLength: 'medium',
          defaultTone: 'neutral',
          maxVariations: 5
        }
      });
      this.modules.set('prompt-engine', promptEngine);

      // Initialize Voice Module (commented out until implemented)
      // const voiceModule = await initializeVoiceModule({
      //   enableVoiceInput: this.config.voice.enableVoiceInput,
      //   defaultLanguage: this.config.voice.defaultLanguage,
      //   enableContinuousListening: this.config.voice.enableContinuousListening
      // });
      // this.modules.set('voice-module', voiceModule);

      // Initialize Hotkey Manager
      const hotkeyManager = await initializeHotkeyManager({
        enableGlobalHotkeys: this.config.hotkeys.enableGlobalHotkeys
      });
      this.modules.set('hotkey-manager', hotkeyManager);

      // Initialize Context Extractor
      const contextExtractor = await initializeContextExtractor({
        screen: {
          defaultFormat: 'png' as const,
          defaultQuality: 90,
          tempDirectory: '/tmp',
          autoCleanup: true
        },
        clipboard: {
          monitoringEnabled: this.config.context.enableClipboardMonitoring,
          monitoringInterval: 1000,
          historySize: 100,
          enableHistory: true
        },
        ocr: {
          engine: 'tesseract' as const,
          defaultLanguage: this.config.context.ocrLanguage,
          confidence: 0.8,
          enableCache: true
        }
      });
      this.modules.set('context-extractor', contextExtractor);

      // Initialize Window Manager
      const windowManager = initializeWindowManager({
        ui: {
          theme: this.config.ui.theme,
          accentColor: '#007acc',
          fontSize: 14,
          fontFamily: 'system-ui',
          borderRadius: 8,
          showTitleBar: false,
          showControls: true
        },
        window: {
          width: 400,
          height: 300,
          minWidth: 300,
          minHeight: 200,
          opacity: this.config.ui.windowOpacity,
          alwaysOnTop: true,
          resizable: true,
          movable: true,
          minimizable: true,
          maximizable: false,
          closable: true,
          skipTaskbar: true,
          transparent: false
        },
        behavior: {
          autoHide: false,
          autoHideDelay: 3000,
          stayOnTop: true,
          followCursor: false,
          snapToEdges: false,
          fadeInOut: this.config.ui.enableAnimations,
          rememberPosition: true,
          rememberSize: true
        },
        hotkeys: {
          show: 'Ctrl+Shift+P',
          hide: 'Escape',
          toggle: 'Ctrl+Shift+T',
          focus: 'Ctrl+Shift+F'
        }
      });
      this.modules.set('window-manager', windowManager);

      logger.info('All services initialized');
    } catch (error) {
      logger.error('Failed to initialize services', { error });
      throw error;
    }
  }

  /**
   * Start all modules
   */
  private async startModules(): Promise<void> {
    for (const [name, module] of Array.from(this.modules.entries())) {
      try {
        if (module && typeof module.start === 'function') {
          await module.start();
          logger.debug('Module started', { name });
        }
      } catch (error) {
        logger.error('Failed to start module', { error, name });
      }
    }
  }

  /**
   * Stop all modules
   */
  private async stopModules(): Promise<void> {
    for (const [name, module] of Array.from(this.modules.entries())) {
      try {
        if (module && typeof module.stop === 'function') {
          await module.stop();
          logger.debug('Module stopped', { name });
        }
      } catch (error) {
        logger.error('Failed to stop module', { error, name });
      }
    }
  }

  /**
   * Setup global event handlers
   */
  private setupGlobalEventHandlers(): void {
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception', { error });
      this.emit('error', error);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled promise rejection', { reason, promise });
      this.emit('error', reason);
    });

    // Handle process signals
    process.on('SIGINT', () => {
      logger.info('Received SIGINT, shutting down gracefully');
      this.shutdown().catch(error => {
        logger.error('Error during graceful shutdown', { error });
        process.exit(1);
      });
    });

    process.on('SIGTERM', () => {
      logger.info('Received SIGTERM, shutting down gracefully');
      this.shutdown().catch(error => {
        logger.error('Error during graceful shutdown', { error });
        process.exit(1);
      });
    });
  }

  /**
   * Register default hotkeys
   */
  private async registerDefaultHotkeys(): Promise<void> {
    const hotkeyManager = this.modules.get('hotkey-manager');
    const windowManager = this.modules.get('window-manager');
    
    if (!hotkeyManager || !windowManager) {
      return;
    }

    try {
      // Register show window hotkey
      await hotkeyManager.registerAction('show-main-window', {
        execute: async () => {
          const windows = windowManager.getVisibleWindows();
          if (windows.length > 0) {
            await windowManager.hideAllWindows();
          } else {
            const window = await windowManager.createWindow();
            await window.show({ center: true });
          }
        },
        description: 'Show/hide main window'
      });

      // Register other default actions
      await hotkeyManager.registerAction('capture-screen-text', {
        execute: async () => {
          const contextExtractor = this.modules.get('context-extractor');
          if (contextExtractor) {
            const text = await contextExtractor.captureScreen({ includeOCR: true });
            logger.info('Screen captured', { textLength: text.extractedText?.length || 0 });
          }
        },
        description: 'Capture screen and extract text'
      });

      logger.info('Default hotkeys registered');
    } catch (error) {
      logger.error('Failed to register default hotkeys', { error });
    }
  }

  /**
   * Update module configurations
   */
  private updateModuleConfigurations(): void {
    // Update each module's configuration based on new app config
    for (const [name, module] of Array.from(this.modules.entries())) {
      try {
        if (module && typeof module.updateConfig === 'function') {
          const moduleConfig = this.getModuleConfig(name);
          module.updateConfig(moduleConfig);
        }
      } catch (error) {
        logger.error('Failed to update module configuration', { error, name });
      }
    }
  }

  /**
   * Get module-specific configuration
   */
  private getModuleConfig(moduleName: string): any {
    switch (moduleName) {
      case 'voice-module':
        return this.config.voice;
      case 'hotkey-manager':
        return this.config.hotkeys;
      case 'context-extractor':
        return this.config.context;
      case 'window-manager':
        return this.config.ui;
      case 'prompt-engine':
        return this.config.ai;
      default:
        return {};
    }
  }

  /**
   * Get application start time
   */
  private getStartTime(): number {
    // In a real implementation, this would track actual start time
    return Date.now() - 10000; // Placeholder
  }

  /**
   * Cleanup resources
   */
  private async cleanup(): Promise<void> {
    try {
      // Cleanup all modules
      for (const [name, module] of Array.from(this.modules.entries())) {
        try {
          if (module && typeof module.cleanup === 'function') {
            await module.cleanup();
          }
        } catch (error) {
          logger.error('Failed to cleanup module', { error, name });
        }
      }

      // Cleanup core systems
      const storageManager = getStorageManager();
      await storageManager.close();

      this.modules.clear();
      this.removeAllListeners();

      logger.info('Application cleanup completed');
    } catch (error) {
      logger.error('Error during application cleanup', { error });
    }
  }
}

// Global application instance
let globalApplication: Application | null = null;

export const getApplication = (): Application => {
  if (!globalApplication) {
    globalApplication = new Application();
  }
  return globalApplication;
};

export const initializeApplication = async (config?: Partial<ApplicationConfig>): Promise<Application> => {
  globalApplication = new Application(config);
  await globalApplication.initialize();
  return globalApplication;
};
