/**
 * AI Service module exports
 * Unified AI provider system with streaming and context management
 */

// Core classes
export { AIService, getAIService, initializeAIService } from './AIService';
export { BaseAIProvider } from './BaseAIProvider';

// Import for local use
import { getAIService, initializeAIService } from './AIService';

// Provider implementations
export { OpenAIProvider } from './providers/OpenAIProvider';

// Types and interfaces
export * from './types';

// Convenience functions
export const createAIService = async (config?: any): Promise<any> => {
  return initializeAIService(config);
};

export const getDefaultAIProvider = (): any => {
  return getAIService().getProvider(getAIService().getConfig().defaultProvider);
};

export const completePrompt = async (
  messages: any[],
  options?: any,
  provider?: any
): Promise<any> => {
  const request = {
    messages,
    options,
    context: getAIService().createContext()
  };
  
  return getAIService().complete(request, provider);
};

export const streamPrompt = async function* (
  messages: any[],
  options?: any,
  provider?: any
): AsyncIterable<any> {
  const request = {
    messages,
    options,
    context: getAIService().createContext()
  };
  
  yield* getAIService().stream(request, provider);
};

export const enhanceText = async (
  text: string,
  enhancement: string,
  options?: any
): Promise<string> => {
  const { AI_ENHANCEMENTS } = await import('./types');
  const enhancementConfig = AI_ENHANCEMENTS[enhancement];
  
  if (!enhancementConfig) {
    throw new Error(`Unknown enhancement: ${enhancement}`);
  }
  
  const messages = [
    { role: 'user', content: `${enhancementConfig.prompt}\n\n${text}` }
  ];
  
  const response = await completePrompt(messages, {
    ...enhancementConfig.options,
    ...options
  }, enhancementConfig.provider);
  
  return response.content;
};

export const estimateTokens = async (
  text: string,
  model?: string,
  provider?: any
): Promise<number> => {
  return getAIService().estimateTokens(text, model, provider);
};

export const estimateCost = async (
  tokens: number,
  model?: string,
  provider?: any
): Promise<number> => {
  return getAIService().estimateCost(tokens, model, provider);
};

export const getAvailableModels = async (): Promise<any[]> => {
  return getAIService().getAvailableModels();
};

export const getModel = async (modelId: string, provider?: any): Promise<any> => {
  return getAIService().getModel(modelId, provider);
};

// Context management helpers
export const createConversationContext = (options?: any): any => {
  return getAIService().createContext({
    conversationId: generateId(),
    history: [],
    maxHistoryLength: 20,
    ...options
  });
};

export const addToContext = (context: any, message: any): any => {
  const updatedHistory = [...(context.history || []), message];
  
  // Trim history if too long
  if (context.maxHistoryLength && updatedHistory.length > context.maxHistoryLength) {
    updatedHistory.splice(0, updatedHistory.length - context.maxHistoryLength);
  }
  
  return getAIService().updateContext(context, { history: updatedHistory });
};

export const clearContext = (context: any): any => {
  return getAIService().updateContext(context, { history: [] });
};

// Provider management helpers
export const registerAIProvider = (provider: any): void => {
  getAIService().registerProvider(provider);
};

export const getAIProvider = (name: any): any => {
  return getAIService().getProvider(name);
};

export const setDefaultAIProvider = (provider: any): void => {
  getAIService().setDefaultProvider(provider);
};

export const getAvailableProviders = (): any[] => {
  return getAIService().getAvailableProviders();
};

// Configuration helpers
export const configureAIService = (config: any): void => {
  getAIService().configure(config);
};

export const getAIServiceConfig = (): any => {
  return getAIService().getConfig();
};

// Utility functions
export const generateId = (): string => {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
};

export const formatMessages = (
  systemPrompt?: string,
  userMessage?: string,
  history?: any[]
): any[] => {
  const messages: any[] = [];
  
  if (systemPrompt) {
    messages.push({ role: 'system', content: systemPrompt });
  }
  
  if (history) {
    messages.push(...history);
  }
  
  if (userMessage) {
    messages.push({ role: 'user', content: userMessage });
  }
  
  return messages;
};

export const extractVariables = (text: string): string[] => {
  const variableRegex = /\{\{(\w+)\}\}/g;
  const variables: string[] = [];
  let match;
  
  while ((match = variableRegex.exec(text)) !== null) {
    if (!variables.includes(match[1])) {
      variables.push(match[1]);
    }
  }
  
  return variables;
};

export const replaceVariables = (
  text: string,
  variables: Record<string, string>
): string => {
  let result = text;
  
  for (const [key, value] of Object.entries(variables)) {
    const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
    result = result.replace(regex, value);
  }
  
  return result;
};

// Error handling helpers
export const isAIError = (error: any): boolean => {
  return error instanceof Error && error.name.includes('AI');
};

export const isRateLimitError = (error: any): boolean => {
  return error.name === 'AIRateLimitError';
};

export const isQuotaExceededError = (error: any): boolean => {
  return error.name === 'AIQuotaExceededError';
};

export const getRetryDelay = (error: any): number => {
  if (isRateLimitError(error) && error.retryAfter) {
    return error.retryAfter * 1000; // Convert to milliseconds
  }
  return 1000; // Default 1 second
};

// Streaming helpers
export const collectStreamResponse = async (
  stream: AsyncIterable<any>
): Promise<{ content: string; usage?: any; metadata?: any }> => {
  let content = '';
  let usage: any;
  let metadata: any;
  
  for await (const chunk of stream) {
    if (chunk.delta.content) {
      content += chunk.delta.content;
    }
    
    if (chunk.usage) {
      usage = chunk.usage;
    }
    
    if (chunk.metadata) {
      metadata = { ...metadata, ...chunk.metadata };
    }
  }
  
  return { content, usage, metadata };
};

// Model comparison helpers
export const compareModels = (model1: any, model2: any): number => {
  // Compare by context window first
  const contextDiff = model2.capabilities.contextWindow - model1.capabilities.contextWindow;
  if (contextDiff !== 0) return contextDiff;
  
  // Then by cost (lower is better)
  const cost1 = model1.capabilities.costPer1kTokens.input + model1.capabilities.costPer1kTokens.output;
  const cost2 = model2.capabilities.costPer1kTokens.input + model2.capabilities.costPer1kTokens.output;
  return cost1 - cost2;
};

export const getBestModelForTask = async (
  task: 'chat' | 'completion' | 'analysis' | 'creative',
  provider?: any
): Promise<any> => {
  const models = await getAvailableModels();
  const filteredModels = provider 
    ? models.filter(m => m.provider === provider)
    : models;
  
  // Sort by capabilities and cost
  const sortedModels = filteredModels.sort(compareModels);
  
  // Return best model for task
  switch (task) {
    case 'chat':
    case 'completion':
      return sortedModels.find(m => m.capabilities.supportsStreaming) || sortedModels[0];
    case 'analysis':
      return sortedModels.find(m => m.capabilities.contextWindow >= 16000) || sortedModels[0];
    case 'creative':
      return sortedModels.find(m => m.id.includes('gpt-4')) || sortedModels[0];
    default:
      return sortedModels[0];
  }
};

// Health check
export const checkAIServiceHealth = async (): Promise<{
  healthy: boolean;
  providers: any[];
  errors: string[];
}> => {
  const errors: string[] = [];
  const providerStatuses: any[] = [];
  
  try {
    const service = getAIService();
    const providers = service.getAvailableProviders();
    
    for (const providerName of providers) {
      const provider = service.getProvider(providerName);
      if (provider) {
        try {
          const isValid = await provider.validateConfig();
          providerStatuses.push({
            name: providerName,
            healthy: isValid,
            models: await provider.getModels().then(m => m.length).catch(() => 0)
          });
        } catch (error) {
          errors.push(`Provider ${providerName}: ${error instanceof Error ? error.message : String(error)}`);
          providerStatuses.push({
            name: providerName,
            healthy: false,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }
    }
    
    const healthy = providerStatuses.some(p => p.healthy) && errors.length === 0;
    
    return {
      healthy,
      providers: providerStatuses,
      errors
    };
  } catch (error) {
    errors.push(`Service error: ${error instanceof Error ? error.message : String(error)}`);
    return {
      healthy: false,
      providers: [],
      errors
    };
  }
};
