# IPC Communication Module Specification

## 🎯 Purpose

The IPC (Inter-Process Communication) module provides secure, type-safe communication between the Electron main process and renderer processes, enabling seamless data exchange and command execution.

## 📋 Responsibilities

### Message Routing
- Route messages between main and renderer processes
- Validate message formats and permissions
- Handle request-response patterns
- Manage event subscriptions

### Security
- Sanitize and validate all IPC messages
- Enforce security policies
- Prevent unauthorized access to system APIs
- Log security violations

### Performance
- Optimize message serialization
- Implement message queuing
- Handle high-frequency events
- Manage memory usage

## 🏗️ Architecture

### IPC Router
```typescript
class IPCRouter {
  private handlers: Map<string, IPCHandler>;
  private middleware: IPCMiddleware[];
  private eventEmitter: EventEmitter;
  
  registerHandler(channel: string, handler: <PERSON>CHandler): void;
  unregisterHandler(channel: string): void;
  addMiddleware(middleware: IPCMiddleware): void;
  routeMessage(channel: string, data: any): Promise<any>;
}
```

### Message Types
```typescript
enum IPCMessageType {
  // Window Management
  WINDOW_SHOW = 'window:show',
  WINDOW_HIDE = 'window:hide',
  WINDOW_MINIMIZE = 'window:minimize',
  WINDOW_CLOSE = 'window:close',
  
  // Prompt Operations
  PROMPT_SAVE = 'prompt:save',
  PROMPT_LOAD = 'prompt:load',
  PROMPT_DELETE = 'prompt:delete',
  PROMPT_ENHANCE = 'prompt:enhance',
  
  // Voice Operations
  VOICE_START_RECORDING = 'voice:start-recording',
  VOICE_STOP_RECORDING = 'voice:stop-recording',
  VOICE_TRANSCRIPT = 'voice:transcript',
  
  // Context Operations
  CONTEXT_CAPTURE_SCREEN = 'context:capture-screen',
  CONTEXT_GET_CLIPBOARD = 'context:get-clipboard',
  CONTEXT_EXTRACT_TEXT = 'context:extract-text',
  
  // System Operations
  SYSTEM_GET_INFO = 'system:get-info',
  SYSTEM_NOTIFICATION = 'system:notification',
  SYSTEM_HOTKEY_TRIGGERED = 'system:hotkey-triggered',
  
  // Settings Operations
  SETTINGS_GET = 'settings:get',
  SETTINGS_SET = 'settings:set',
  SETTINGS_RESET = 'settings:reset'
}
```

## 📡 Message Protocols

### Request-Response Protocol
```typescript
interface IPCRequest<T = any> {
  id: string;
  channel: string;
  data: T;
  timestamp: number;
  source: 'main' | 'renderer';
}

interface IPCResponse<T = any> {
  id: string;
  channel: string;
  success: boolean;
  data?: T;
  error?: IPCError;
  timestamp: number;
}

interface IPCError {
  code: string;
  message: string;
  details?: any;
}
```

### Event Protocol
```typescript
interface IPCEvent<T = any> {
  channel: string;
  data: T;
  timestamp: number;
  source: 'main' | 'renderer';
}
```

## 🔒 Security Implementation

### Message Validation
```typescript
interface MessageValidator {
  validateChannel(channel: string): boolean;
  validateData(data: any, schema: JSONSchema): boolean;
  sanitizeData(data: any): any;
  checkPermissions(channel: string, source: string): boolean;
}

class IPCValidator implements MessageValidator {
  private allowedChannels: Set<string>;
  private channelSchemas: Map<string, JSONSchema>;
  private permissions: Map<string, string[]>;
  
  validateChannel(channel: string): boolean {
    return this.allowedChannels.has(channel);
  }
  
  validateData(data: any, schema: JSONSchema): boolean {
    return ajv.validate(schema, data);
  }
  
  sanitizeData(data: any): any {
    // Remove potentially dangerous properties
    const sanitized = { ...data };
    delete sanitized.__proto__;
    delete sanitized.constructor;
    return sanitized;
  }
  
  checkPermissions(channel: string, source: string): boolean {
    const allowedSources = this.permissions.get(channel);
    return allowedSources?.includes(source) ?? false;
  }
}
```

### Preload Script
```typescript
// preload.ts
import { contextBridge, ipcRenderer } from 'electron';

// Expose safe IPC methods to renderer
contextBridge.exposeInMainWorld('electronAPI', {
  // Prompt operations
  savePrompt: (prompt: Prompt) => 
    ipcRenderer.invoke(IPCMessageType.PROMPT_SAVE, prompt),
  
  loadPrompts: () => 
    ipcRenderer.invoke(IPCMessageType.PROMPT_LOAD),
  
  enhancePrompt: (text: string) => 
    ipcRenderer.invoke(IPCMessageType.PROMPT_ENHANCE, text),
  
  // Voice operations
  startVoiceRecording: () => 
    ipcRenderer.invoke(IPCMessageType.VOICE_START_RECORDING),
  
  stopVoiceRecording: () => 
    ipcRenderer.invoke(IPCMessageType.VOICE_STOP_RECORDING),
  
  // Context operations
  captureScreen: () => 
    ipcRenderer.invoke(IPCMessageType.CONTEXT_CAPTURE_SCREEN),
  
  getClipboardText: () => 
    ipcRenderer.invoke(IPCMessageType.CONTEXT_GET_CLIPBOARD),
  
  // Event listeners
  onHotkeyTriggered: (callback: (hotkeyId: string) => void) => {
    const listener = (_: any, hotkeyId: string) => callback(hotkeyId);
    ipcRenderer.on(IPCMessageType.SYSTEM_HOTKEY_TRIGGERED, listener);
    return () => ipcRenderer.removeListener(
      IPCMessageType.SYSTEM_HOTKEY_TRIGGERED, 
      listener
    );
  },
  
  onVoiceTranscript: (callback: (transcript: string) => void) => {
    const listener = (_: any, transcript: string) => callback(transcript);
    ipcRenderer.on(IPCMessageType.VOICE_TRANSCRIPT, listener);
    return () => ipcRenderer.removeListener(
      IPCMessageType.VOICE_TRANSCRIPT, 
      listener
    );
  }
});
```

## 🔧 Handler Implementation

### Main Process Handlers
```typescript
class MainProcessHandlers {
  constructor(
    private promptService: PromptService,
    private voiceService: VoiceService,
    private contextService: ContextService
  ) {}
  
  registerHandlers(ipcRouter: IPCRouter): void {
    // Prompt handlers
    ipcRouter.registerHandler(
      IPCMessageType.PROMPT_SAVE,
      this.handleSavePrompt.bind(this)
    );
    
    ipcRouter.registerHandler(
      IPCMessageType.PROMPT_LOAD,
      this.handleLoadPrompts.bind(this)
    );
    
    ipcRouter.registerHandler(
      IPCMessageType.PROMPT_ENHANCE,
      this.handleEnhancePrompt.bind(this)
    );
    
    // Voice handlers
    ipcRouter.registerHandler(
      IPCMessageType.VOICE_START_RECORDING,
      this.handleStartVoiceRecording.bind(this)
    );
    
    ipcRouter.registerHandler(
      IPCMessageType.VOICE_STOP_RECORDING,
      this.handleStopVoiceRecording.bind(this)
    );
    
    // Context handlers
    ipcRouter.registerHandler(
      IPCMessageType.CONTEXT_CAPTURE_SCREEN,
      this.handleCaptureScreen.bind(this)
    );
  }
  
  private async handleSavePrompt(data: Prompt): Promise<void> {
    await this.promptService.save(data);
  }
  
  private async handleLoadPrompts(): Promise<Prompt[]> {
    return await this.promptService.loadAll();
  }
  
  private async handleEnhancePrompt(text: string): Promise<string> {
    return await this.promptService.enhance(text);
  }
  
  private async handleStartVoiceRecording(): Promise<void> {
    await this.voiceService.startRecording();
  }
  
  private async handleStopVoiceRecording(): Promise<string> {
    return await this.voiceService.stopRecording();
  }
  
  private async handleCaptureScreen(): Promise<string> {
    return await this.contextService.captureScreen();
  }
}
```

## 🔄 Event Management

### Event Emitter
```typescript
class IPCEventEmitter {
  private eventEmitter: EventEmitter;
  private subscribers: Map<string, Set<Function>>;
  
  emit(channel: string, data: any): void {
    // Emit to main process listeners
    this.eventEmitter.emit(channel, data);
    
    // Send to all renderer processes
    BrowserWindow.getAllWindows().forEach(window => {
      window.webContents.send(channel, data);
    });
  }
  
  on(channel: string, listener: Function): void {
    this.eventEmitter.on(channel, listener);
  }
  
  off(channel: string, listener: Function): void {
    this.eventEmitter.off(channel, listener);
  }
  
  once(channel: string, listener: Function): void {
    this.eventEmitter.once(channel, listener);
  }
}
```

### Event Broadcasting
```typescript
class EventBroadcaster {
  broadcastHotkeyTriggered(hotkeyId: string): void {
    this.emit(IPCMessageType.SYSTEM_HOTKEY_TRIGGERED, hotkeyId);
  }
  
  broadcastVoiceTranscript(transcript: string): void {
    this.emit(IPCMessageType.VOICE_TRANSCRIPT, transcript);
  }
  
  broadcastSystemNotification(notification: Notification): void {
    this.emit(IPCMessageType.SYSTEM_NOTIFICATION, notification);
  }
  
  private emit(channel: string, data: any): void {
    BrowserWindow.getAllWindows().forEach(window => {
      if (!window.isDestroyed()) {
        window.webContents.send(channel, data);
      }
    });
  }
}
```

## 📊 Performance Optimization

### Message Queuing
```typescript
class MessageQueue {
  private queue: IPCMessage[] = [];
  private processing = false;
  private maxQueueSize = 1000;
  
  enqueue(message: IPCMessage): void {
    if (this.queue.length >= this.maxQueueSize) {
      this.queue.shift(); // Remove oldest message
    }
    this.queue.push(message);
    this.processQueue();
  }
  
  private async processQueue(): Promise<void> {
    if (this.processing) return;
    
    this.processing = true;
    while (this.queue.length > 0) {
      const message = this.queue.shift()!;
      await this.processMessage(message);
    }
    this.processing = false;
  }
  
  private async processMessage(message: IPCMessage): Promise<void> {
    // Process individual message
  }
}
```

### Serialization Optimization
```typescript
class MessageSerializer {
  serialize(data: any): Buffer {
    // Use efficient serialization (e.g., MessagePack)
    return msgpack.encode(data);
  }
  
  deserialize(buffer: Buffer): any {
    return msgpack.decode(buffer);
  }
  
  compressLargeData(data: any): any {
    const serialized = JSON.stringify(data);
    if (serialized.length > 1024) {
      return {
        compressed: true,
        data: zlib.gzipSync(serialized)
      };
    }
    return { compressed: false, data };
  }
}
```

## 🧪 Testing Strategy

### Unit Tests
```typescript
describe('IPCRouter', () => {
  let router: IPCRouter;
  
  beforeEach(() => {
    router = new IPCRouter();
  });
  
  test('should register and call handler', async () => {
    const handler = jest.fn().mockResolvedValue('result');
    router.registerHandler('test-channel', handler);
    
    const result = await router.routeMessage('test-channel', { test: 'data' });
    
    expect(handler).toHaveBeenCalledWith({ test: 'data' });
    expect(result).toBe('result');
  });
  
  test('should validate messages', () => {
    const validator = new IPCValidator();
    expect(validator.validateChannel('invalid-channel')).toBe(false);
    expect(validator.validateChannel('prompt:save')).toBe(true);
  });
});
```

### Integration Tests
```typescript
describe('IPC Integration', () => {
  test('should handle prompt save flow', async () => {
    const prompt = { id: '1', title: 'Test', content: 'Test content' };
    
    // Mock the service
    const mockPromptService = {
      save: jest.fn().mockResolvedValue(undefined)
    };
    
    const handlers = new MainProcessHandlers(mockPromptService);
    const result = await handlers.handleSavePrompt(prompt);
    
    expect(mockPromptService.save).toHaveBeenCalledWith(prompt);
  });
});
```
