# Personal Prompt Application

A powerful AI-powered prompt enhancement tool with voice input, screen capture, and context extraction capabilities. This desktop application helps you create, manage, and enhance prompts with intelligent context gathering and multi-modal input support.

## Features

- 🤖 AI-powered prompt enhancement
- 📝 Comprehensive prompt library management
- 🎤 Voice-to-text input capabilities
- 📸 Screen capture and OCR integration
- ⌨️ Global hotkey support
- 🔒 Privacy-first, local-first data storage
- 🌐 Cross-platform compatibility (Windows, macOS, Linux)

## Development Setup

### Prerequisites

- Node.js 18.0.0 or higher
- npm or yarn package manager

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd promptpilot-desktop
```

2. Install dependencies:
```bash
npm install
```

3. Start development server:
```bash
npm run dev
```

### Available Scripts

- `npm run dev` - Start development mode with hot reload
- `npm run build` - Build the application for production
- `npm start` - Start the built application
- `npm run lint` - Run ESLint code analysis
- `npm run format` - Format code with Prettier
- `npm test` - Run test suite
- `npm run package` - Package the application for distribution

### Project Structure

```
src/
├── main/           # Electron main process
├── renderer/       # UI components (React)
├── shared/         # Shared utilities and IPC
├── core/           # Foundation layer (Logger, Config, Security)
├── services/       # Service layer modules
├── storage/        # Database and persistence
└── types/          # TypeScript definitions
```

## Architecture

The application follows a strict 6-layer dependency hierarchy:

1. **Foundation Layer**: Logger, Configuration, Security
2. **Core Utilities**: File System, Error Handler, Data Models
3. **Storage & Communication**: Storage Module, IPC Communication
4. **Service Layer**: Context Extractor, Voice Module, Prompt Library, AI API Bridge, STT Bridge
5. **Business Logic**: Prompt Engine, Hotkey Manager
6. **Application Layer**: Main Process, Renderer Process

## Security

- Context isolation enabled
- Node integration disabled in renderer
- Secure IPC communication
- Local data encryption for sensitive information

## License

MIT License - see LICENSE file for details
