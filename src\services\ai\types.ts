/**
 * AI Provider Service type definitions
 * Unified interface for different AI providers with streaming and context management
 */

// AI Provider types
export type AIProvider = 'openai' | 'anthropic' | 'google' | 'azure' | 'local' | 'custom';

// AI Model capabilities
export interface ModelCapabilities {
  readonly maxTokens: number;
  readonly supportsStreaming: boolean;
  readonly supportsImages: boolean;
  readonly supportsTools: boolean;
  readonly supportsFunctions: boolean;
  readonly contextWindow: number;
  readonly costPer1kTokens: {
    readonly input: number;
    readonly output: number;
  };
}

// AI Model information
export interface AIModel {
  readonly id: string;
  readonly name: string;
  readonly provider: AIProvider;
  readonly description?: string;
  readonly capabilities: ModelCapabilities;
  readonly isAvailable: boolean;
  readonly deprecated?: boolean;
}

// AI Request message
export interface AIMessage {
  readonly role: 'system' | 'user' | 'assistant' | 'tool';
  readonly content: string;
  readonly name?: string;
  readonly toolCallId?: string;
  readonly metadata?: Record<string, any>;
}

// AI Request options
export interface AIRequestOptions {
  readonly model?: string;
  readonly maxTokens?: number;
  readonly temperature?: number;
  readonly topP?: number;
  readonly frequencyPenalty?: number;
  readonly presencePenalty?: number;
  readonly stop?: string[];
  readonly stream?: boolean;
  readonly tools?: AITool[];
  readonly toolChoice?: 'auto' | 'none' | { type: 'function'; function: { name: string } };
  readonly responseFormat?: { type: 'text' | 'json_object' };
  readonly seed?: number;
  readonly user?: string;
  readonly metadata?: Record<string, any>;
}

// AI Tool definition
export interface AITool {
  readonly type: 'function';
  readonly function: {
    readonly name: string;
    readonly description: string;
    readonly parameters: Record<string, any>;
  };
}

// AI Request
export interface AIRequest {
  readonly messages: AIMessage[];
  readonly options?: AIRequestOptions;
  readonly context?: AIContext;
}

// AI Response
export interface AIResponse {
  readonly id: string;
  readonly model: string;
  readonly provider: AIProvider;
  readonly content: string;
  readonly finishReason: 'stop' | 'length' | 'tool_calls' | 'content_filter' | 'error';
  readonly usage: {
    readonly promptTokens: number;
    readonly completionTokens: number;
    readonly totalTokens: number;
    readonly cost?: number;
  };
  readonly toolCalls?: AIToolCall[];
  readonly metadata?: Record<string, any>;
  readonly timestamp: Date;
  readonly latency: number;
}

// AI Tool call
export interface AIToolCall {
  readonly id: string;
  readonly type: 'function';
  readonly function: {
    readonly name: string;
    readonly arguments: string;
  };
}

// AI Streaming response chunk
export interface AIStreamChunk {
  readonly id: string;
  readonly model: string;
  readonly provider: AIProvider;
  readonly delta: {
    readonly role?: string;
    readonly content?: string;
    readonly toolCalls?: Partial<AIToolCall>[];
  };
  readonly finishReason?: string;
  readonly usage?: {
    readonly promptTokens?: number;
    readonly completionTokens?: number;
    readonly totalTokens?: number;
  };
}

// AI Context for conversation management
export interface AIContext {
  readonly conversationId?: string;
  readonly sessionId?: string;
  readonly userId?: string;
  readonly promptId?: string;
  readonly variables?: Record<string, any>;
  readonly history?: AIMessage[];
  readonly maxHistoryLength?: number;
  readonly systemPrompt?: string;
  readonly metadata?: Record<string, any>;
}

// AI Provider configuration
export interface AIProviderConfig {
  readonly provider: AIProvider;
  readonly apiKey?: string;
  readonly baseUrl?: string;
  readonly organization?: string;
  readonly project?: string;
  readonly defaultModel?: string;
  readonly timeout?: number;
  readonly retries?: number;
  readonly rateLimits?: {
    readonly requestsPerMinute?: number;
    readonly tokensPerMinute?: number;
  };
  readonly headers?: Record<string, string>;
  readonly proxy?: string;
  readonly enabled: boolean;
}

// AI Provider interface
export interface IAIProvider {
  readonly name: AIProvider;
  readonly config: AIProviderConfig;
  
  // Model operations
  getModels(): Promise<AIModel[]>;
  getModel(modelId: string): Promise<AIModel | null>;
  
  // Request operations
  complete(request: AIRequest): Promise<AIResponse>;
  stream(request: AIRequest): AsyncIterable<AIStreamChunk>;
  
  // Utility operations
  estimateTokens(text: string, model?: string): Promise<number>;
  validateConfig(): Promise<boolean>;
  getUsage(): Promise<AIUsageStats>;
}

// AI Service interface
export interface IAIService {
  // Provider management
  registerProvider(provider: IAIProvider): void;
  getProvider(name: AIProvider): IAIProvider | null;
  getAvailableProviders(): AIProvider[];
  setDefaultProvider(provider: AIProvider): void;
  
  // Model operations
  getAvailableModels(): Promise<AIModel[]>;
  getModel(modelId: string, provider?: AIProvider): Promise<AIModel | null>;
  
  // Request operations
  complete(request: AIRequest, provider?: AIProvider): Promise<AIResponse>;
  stream(request: AIRequest, provider?: AIProvider): AsyncIterable<AIStreamChunk>;
  
  // Context management
  createContext(options?: Partial<AIContext>): AIContext;
  updateContext(context: AIContext, updates: Partial<AIContext>): AIContext;
  
  // Utility operations
  estimateTokens(text: string, model?: string, provider?: AIProvider): Promise<number>;
  estimateCost(tokens: number, model?: string, provider?: AIProvider): Promise<number>;
  
  // Configuration
  configure(config: Partial<AIServiceConfig>): void;
  getConfig(): AIServiceConfig;
}

// AI Service configuration
export interface AIServiceConfig {
  readonly defaultProvider: AIProvider;
  readonly fallbackProviders: AIProvider[];
  readonly enableCaching: boolean;
  readonly cacheTimeout: number;
  readonly enableRateLimiting: boolean;
  readonly enableUsageTracking: boolean;
  readonly enableErrorRetry: boolean;
  readonly maxRetries: number;
  readonly retryDelay: number;
  readonly timeout: number;
  readonly enableLogging: boolean;
  readonly logLevel: 'debug' | 'info' | 'warn' | 'error';
}

// AI Usage statistics
export interface AIUsageStats {
  readonly provider: AIProvider;
  readonly totalRequests: number;
  readonly totalTokens: number;
  readonly totalCost: number;
  readonly averageLatency: number;
  readonly errorRate: number;
  readonly lastUsed: Date;
  readonly dailyUsage: {
    readonly date: string;
    readonly requests: number;
    readonly tokens: number;
    readonly cost: number;
  }[];
}

// AI Error types
export class AIProviderError extends Error {
  constructor(
    message: string,
    public readonly provider: AIProvider,
    public readonly code?: string,
    public readonly statusCode?: number,
    public readonly details?: any
  ) {
    super(message);
    this.name = 'AIProviderError';
  }
}

export class AIRateLimitError extends AIProviderError {
  constructor(
    provider: AIProvider,
    public readonly retryAfter?: number,
    public readonly limit?: string
  ) {
    super(`Rate limit exceeded for ${provider}`, provider, 'RATE_LIMIT', 429);
    this.name = 'AIRateLimitError';
  }
}

export class AIQuotaExceededError extends AIProviderError {
  constructor(
    provider: AIProvider,
    public readonly quotaType: 'tokens' | 'requests' | 'cost'
  ) {
    super(`Quota exceeded for ${provider}: ${quotaType}`, provider, 'QUOTA_EXCEEDED', 402);
    this.name = 'AIQuotaExceededError';
  }
}

export class AIModelNotFoundError extends AIProviderError {
  constructor(
    provider: AIProvider,
    public readonly modelId: string
  ) {
    super(`Model ${modelId} not found for provider ${provider}`, provider, 'MODEL_NOT_FOUND', 404);
    this.name = 'AIModelNotFoundError';
  }
}

export class AIInvalidRequestError extends AIProviderError {
  constructor(
    provider: AIProvider,
    message: string,
    public readonly validationErrors?: string[]
  ) {
    super(message, provider, 'INVALID_REQUEST', 400);
    this.name = 'AIInvalidRequestError';
  }
}

// AI Enhancement types
export interface AIEnhancement {
  readonly type: 'grammar' | 'clarity' | 'tone' | 'length' | 'style' | 'translation' | 'summarization';
  readonly description: string;
  readonly prompt: string;
  readonly model?: string;
  readonly provider?: AIProvider;
  readonly options?: AIRequestOptions;
}

// Predefined AI enhancements
export const AI_ENHANCEMENTS: Record<string, AIEnhancement> = {
  improveGrammar: {
    type: 'grammar',
    description: 'Improve grammar and spelling',
    prompt: 'Please improve the grammar and spelling of the following text while maintaining its original meaning and tone:'
  },
  improveClarity: {
    type: 'clarity',
    description: 'Improve clarity and readability',
    prompt: 'Please rewrite the following text to make it clearer and more readable while preserving the original meaning:'
  },
  makeProfessional: {
    type: 'tone',
    description: 'Make tone more professional',
    prompt: 'Please rewrite the following text in a more professional tone:'
  },
  makeCasual: {
    type: 'tone',
    description: 'Make tone more casual',
    prompt: 'Please rewrite the following text in a more casual and friendly tone:'
  },
  makeShorten: {
    type: 'length',
    description: 'Make text shorter',
    prompt: 'Please shorten the following text while keeping the key points:'
  },
  makeExpand: {
    type: 'length',
    description: 'Make text longer',
    prompt: 'Please expand the following text with more details and examples:'
  },
  summarize: {
    type: 'summarization',
    description: 'Summarize text',
    prompt: 'Please provide a concise summary of the following text:'
  },
  translate: {
    type: 'translation',
    description: 'Translate text',
    prompt: 'Please translate the following text to {language}:'
  }
};
