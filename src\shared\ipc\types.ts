/**
 * IPC Communication Types and Interfaces
 * Defines all message types, protocols, and interfaces for IPC communication
 */

// Message Types Enum
export enum IPCMessageType {
  // Window Management
  WINDOW_SHOW = 'window:show',
  WINDOW_HIDE = 'window:hide',
  WINDOW_MINIMIZE = 'window:minimize',
  WINDOW_MAXIMIZE = 'window:maximize',
  WINDOW_CLOSE = 'window:close',
  WINDOW_TOGGLE_DEVTOOLS = 'window:toggle-devtools',
  
  // Prompt Operations
  PROMPT_SAVE = 'prompt:save',
  PROMPT_LOAD = 'prompt:load',
  PROMPT_LOAD_ALL = 'prompt:load-all',
  PROMPT_DELETE = 'prompt:delete',
  PROMPT_ENHANCE = 'prompt:enhance',
  PROMPT_SEARCH = 'prompt:search',
  PROMPT_DUPLICATE = 'prompt:duplicate',
  PROMPT_EXPORT = 'prompt:export',
  PROMPT_IMPORT = 'prompt:import',
  
  // Category Operations
  CATEGORY_SAVE = 'category:save',
  CATEGORY_LOAD_ALL = 'category:load-all',
  CATEGORY_DELETE = 'category:delete',
  CATEGORY_MOVE = 'category:move',
  
  // Voice Operations
  VOICE_START_RECORDING = 'voice:start-recording',
  VOICE_STOP_RECORDING = 'voice:stop-recording',
  VOICE_TRANSCRIPT = 'voice:transcript',
  VOICE_ERROR = 'voice:error',
  
  // Context Operations
  CONTEXT_CAPTURE_SCREEN = 'context:capture-screen',
  CONTEXT_GET_CLIPBOARD = 'context:get-clipboard',
  CONTEXT_EXTRACT_TEXT = 'context:extract-text',
  CONTEXT_GET_ACTIVE_WINDOW = 'context:get-active-window',
  
  // AI Operations
  AI_GENERATE = 'ai:generate',
  AI_ENHANCE = 'ai:enhance',
  AI_ANALYZE = 'ai:analyze',
  AI_PROVIDERS_LIST = 'ai:providers-list',
  AI_MODELS_LIST = 'ai:models-list',
  
  // System Operations
  SYSTEM_GET_INFO = 'system:get-info',
  SYSTEM_NOTIFICATION = 'system:notification',
  SYSTEM_HOTKEY_TRIGGERED = 'system:hotkey-triggered',
  SYSTEM_HOTKEY_REGISTER = 'system:hotkey-register',
  SYSTEM_HOTKEY_UNREGISTER = 'system:hotkey-unregister',
  
  // Settings Operations
  SETTINGS_GET = 'settings:get',
  SETTINGS_SET = 'settings:set',
  SETTINGS_RESET = 'settings:reset',
  SETTINGS_GET_ALL = 'settings:get-all',
  
  // User Operations
  USER_GET_CURRENT = 'user:get-current',
  USER_UPDATE_PREFERENCES = 'user:update-preferences',
  USER_GET_STATISTICS = 'user:get-statistics',
  
  // Session Operations
  SESSION_START = 'session:start',
  SESSION_END = 'session:end',
  SESSION_GET_CURRENT = 'session:get-current',
  
  // File Operations
  FILE_DIALOG_OPEN = 'file:dialog-open',
  FILE_DIALOG_SAVE = 'file:dialog-save',
  FILE_READ = 'file:read',
  FILE_WRITE = 'file:write',
  
  // Events
  EVENT_PROMPT_UPDATED = 'event:prompt-updated',
  EVENT_CATEGORY_UPDATED = 'event:category-updated',
  EVENT_SETTINGS_UPDATED = 'event:settings-updated',
  EVENT_USER_UPDATED = 'event:user-updated',
  EVENT_ERROR_OCCURRED = 'event:error-occurred'
}

// Request-Response Protocol
export interface IPCRequest<T = any> {
  id: string;
  channel: string;
  data: T;
  timestamp: number;
  source: 'main' | 'renderer';
}

export interface IPCResponse<T = any> {
  id: string;
  channel: string;
  success: boolean;
  data?: T;
  error?: IPCError;
  timestamp: number;
}

export interface IPCError {
  code: string;
  message: string;
  details?: any;
  stack?: string;
}

// Event Protocol
export interface IPCEvent<T = any> {
  channel: string;
  data: T;
  timestamp: number;
  source: 'main' | 'renderer';
}

// Handler Types
export type IPCHandler<TInput = any, TOutput = any> = (
  data: TInput,
  event?: Electron.IpcMainInvokeEvent
) => Promise<TOutput> | TOutput;

export type IPCEventHandler<T = any> = (data: T) => void;

// Middleware Types
export interface IPCMiddleware {
  name: string;
  execute: (
    channel: string,
    data: any,
    next: () => Promise<any>
  ) => Promise<any>;
}

// Validation Types
export interface MessageValidator {
  validateChannel(channel: string): boolean;
  validateData(data: any, schema?: any): boolean;
  sanitizeData(data: any): any;
  checkPermissions(channel: string, source: string): boolean;
}

// Security Types
export interface SecurityPolicy {
  allowedChannels: Set<string>;
  channelPermissions: Map<string, string[]>;
  rateLimits: Map<string, { maxRequests: number; windowMs: number }>;
  sanitizationRules: Map<string, (data: any) => any>;
}

// Message Queue Types
export interface QueuedMessage {
  id: string;
  channel: string;
  data: any;
  timestamp: number;
  priority: number;
  retries: number;
  maxRetries: number;
}

// Performance Types
export interface PerformanceMetrics {
  messageCount: number;
  averageResponseTime: number;
  errorRate: number;
  queueSize: number;
  lastResetTime: number;
}

// Configuration Types
export interface IPCConfig {
  enableLogging: boolean;
  enableMetrics: boolean;
  maxQueueSize: number;
  defaultTimeout: number;
  enableRateLimiting: boolean;
  enableSerialization: boolean;
  compressionThreshold: number;
}

// Serialization Types
export interface SerializedMessage {
  compressed: boolean;
  data: Buffer | any;
  originalSize?: number;
  compressedSize?: number;
}

// Event Emitter Types
export interface IPCEventEmitter {
  emit(channel: string, data: any): void;
  on(channel: string, listener: Function): void;
  off(channel: string, listener: Function): void;
  once(channel: string, listener: Function): void;
  removeAllListeners(channel?: string): void;
}

// Router Types
export interface IPCRouterConfig {
  enableValidation: boolean;
  enableMiddleware: boolean;
  enableMetrics: boolean;
  enableLogging: boolean;
  timeout: number;
}

// Channel Schema Types
export interface ChannelSchema {
  channel: string;
  inputSchema?: any;
  outputSchema?: any;
  permissions: string[];
  rateLimit?: {
    maxRequests: number;
    windowMs: number;
  };
}

// Error Codes
export enum IPCErrorCode {
  INVALID_CHANNEL = 'INVALID_CHANNEL',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  VALIDATION_FAILED = 'VALIDATION_FAILED',
  HANDLER_NOT_FOUND = 'HANDLER_NOT_FOUND',
  TIMEOUT = 'TIMEOUT',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  SERIALIZATION_ERROR = 'SERIALIZATION_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR'
}

// Utility Types
export type IPCChannelMap = {
  [K in IPCMessageType]: {
    input: any;
    output: any;
  };
};

// Type-safe IPC invoke function
export type TypedIPCInvoke = <T extends IPCMessageType>(
  channel: T,
  data: IPCChannelMap[T]['input']
) => Promise<IPCChannelMap[T]['output']>;

// Type-safe event listener
export type TypedIPCListener = <T extends IPCMessageType>(
  channel: T,
  listener: (data: IPCChannelMap[T]['output']) => void
) => () => void;
