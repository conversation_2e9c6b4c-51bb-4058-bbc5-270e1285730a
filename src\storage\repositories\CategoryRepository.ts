/**
 * Category repository implementation
 * Handles CRUD operations for categories with hierarchical support
 */

import { BaseRepository } from '../BaseRepository';
import { DatabaseConnection, CategoryEntity } from '../types';
import { Category } from '../../types/models/Category';

/**
 * Repository for category entities
 */
export class CategoryRepository extends BaseRepository<Category> {
  constructor(db: DatabaseConnection) {
    super(db, 'categories');
  }

  /**
   * Find root categories (no parent)
   */
  async findRootCategories(): Promise<Category[]> {
    const sql = `SELECT * FROM ${this.tableName} WHERE parent_id IS NULL ORDER BY sort_order ASC`;
    const rows = await this.query(sql);
    return rows.map(row => this.mapRowToEntity(row));
  }

  /**
   * Find child categories
   */
  async findChildren(parentId: string): Promise<Category[]> {
    return this.findAll({
      parentId,
      orderBy: 'sort_order',
      orderDirection: 'ASC'
    });
  }

  /**
   * Find category hierarchy (tree structure)
   */
  async findHierarchy(): Promise<Category[]> {
    const sql = `
      WITH RECURSIVE category_tree AS (
        -- Base case: root categories
        SELECT *, 0 as level
        FROM ${this.tableName}
        WHERE parent_id IS NULL
        
        UNION ALL
        
        -- Recursive case: child categories
        SELECT c.*, ct.level + 1
        FROM ${this.tableName} c
        INNER JOIN category_tree ct ON c.parent_id = ct.id
      )
      SELECT * FROM category_tree ORDER BY level, sort_order
    `;

    const rows = await this.query(sql);
    return rows.map(row => this.mapRowToEntity(row));
  }

  /**
   * Find system categories
   */
  async findSystemCategories(): Promise<Category[]> {
    return this.findAll({
      isSystem: true,
      orderBy: 'sort_order',
      orderDirection: 'ASC'
    });
  }

  /**
   * Find user-created categories
   */
  async findUserCategories(): Promise<Category[]> {
    return this.findAll({
      isSystem: false,
      orderBy: 'sort_order',
      orderDirection: 'ASC'
    });
  }

  /**
   * Update sort order for categories
   */
  async updateSortOrder(categoryIds: string[]): Promise<void> {
    await this.db.transaction(async () => {
      for (let i = 0; i < categoryIds.length; i++) {
        await this.update(categoryIds[i], { sortOrder: i });
      }
    });
  }

  /**
   * Move category to new parent
   */
  async moveToParent(categoryId: string, newParentId?: string): Promise<void> {
    // Validate that we're not creating a circular reference
    if (newParentId) {
      const isCircular = await this.wouldCreateCircularReference(categoryId, newParentId);
      if (isCircular) {
        throw new Error('Cannot move category: would create circular reference');
      }
    }

    const updateData: any = {};
    if (newParentId !== undefined) {
      updateData.parentId = newParentId;
    }
    await this.update(categoryId, updateData);
  }

  /**
   * Get category path (breadcrumb)
   */
  async getCategoryPath(categoryId: string): Promise<Category[]> {
    const sql = `
      WITH RECURSIVE category_path AS (
        -- Base case: target category
        SELECT *, 0 as level
        FROM ${this.tableName}
        WHERE id = ?
        
        UNION ALL
        
        -- Recursive case: parent categories
        SELECT c.*, cp.level + 1
        FROM ${this.tableName} c
        INNER JOIN category_path cp ON c.id = cp.parent_id
      )
      SELECT * FROM category_path ORDER BY level DESC
    `;

    const rows = await this.query(sql, [categoryId]);
    return rows.map(row => this.mapRowToEntity(row));
  }

  /**
   * Update prompt count for category
   */
  async updatePromptCount(categoryId: string, delta: number): Promise<void> {
    const sql = `
      UPDATE ${this.tableName} 
      SET prompt_count = MAX(0, prompt_count + ?),
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    await this.db.run(sql, [delta, categoryId]);
  }

  /**
   * Recalculate prompt counts for all categories
   */
  async recalculatePromptCounts(): Promise<void> {
    const sql = `
      UPDATE ${this.tableName}
      SET prompt_count = (
        SELECT COUNT(*)
        FROM prompts
        WHERE prompts.category_id = ${this.tableName}.id
        AND prompts.is_archived = FALSE
      ),
      updated_at = CURRENT_TIMESTAMP
    `;

    await this.db.exec(sql);
  }

  /**
   * Delete category and handle children
   */
  async deleteWithChildren(categoryId: string, moveChildrenTo?: string): Promise<void> {
    await this.db.transaction(async () => {
      // Handle child categories
      const children = await this.findChildren(categoryId);
      
      if (moveChildrenTo) {
        // Move children to new parent
        for (const child of children) {
          await this.update(child.id, { parentId: moveChildrenTo });
        }
      } else {
        // Delete children recursively
        for (const child of children) {
          await this.deleteWithChildren(child.id);
        }
      }

      // Move prompts to default category or specified category
      const defaultCategoryId = moveChildrenTo || 'general';
      await this.db.run(
        'UPDATE prompts SET category_id = ? WHERE category_id = ?',
        [defaultCategoryId, categoryId]
      );

      // Delete the category
      await this.delete(categoryId);
    });
  }

  /**
   * Get category statistics
   */
  async getStatistics(): Promise<{
    total: number;
    system: number;
    user: number;
    withPrompts: number;
    maxDepth: number;
  }> {
    const [total, system, user, withPrompts] = await Promise.all([
      this.count(),
      this.count({ isSystem: true }),
      this.count({ isSystem: false }),
      this.query<{ count: number }>('SELECT COUNT(*) as count FROM categories WHERE prompt_count > 0')
        .then(rows => rows[0]?.count || 0)
    ]);

    // Calculate max depth
    const maxDepthResult = await this.queryOne<{ max_depth: number }>(`
      WITH RECURSIVE category_depth AS (
        SELECT id, 0 as depth
        FROM ${this.tableName}
        WHERE parent_id IS NULL
        
        UNION ALL
        
        SELECT c.id, cd.depth + 1
        FROM ${this.tableName} c
        INNER JOIN category_depth cd ON c.parent_id = cd.id
      )
      SELECT MAX(depth) as max_depth FROM category_depth
    `);

    return {
      total,
      system,
      user,
      withPrompts,
      maxDepth: maxDepthResult?.max_depth || 0
    };
  }

  /**
   * Check if moving a category would create circular reference
   */
  private async wouldCreateCircularReference(categoryId: string, newParentId: string): Promise<boolean> {
    const sql = `
      WITH RECURSIVE category_ancestors AS (
        -- Base case: new parent
        SELECT id, parent_id
        FROM ${this.tableName}
        WHERE id = ?
        
        UNION ALL
        
        -- Recursive case: ancestors
        SELECT c.id, c.parent_id
        FROM ${this.tableName} c
        INNER JOIN category_ancestors ca ON c.id = ca.parent_id
      )
      SELECT COUNT(*) as count
      FROM category_ancestors
      WHERE id = ?
    `;

    const result = await this.queryOne<{ count: number }>(sql, [newParentId, categoryId]);
    return (result?.count || 0) > 0;
  }

  /**
   * Map database row to Category entity
   */
  protected mapRowToEntity(row: any): Category {
    return {
      id: row.id,
      name: row.name,
      description: row.description,
      parentId: row.parent_id || undefined,
      color: row.color || undefined,
      icon: row.icon || undefined,
      sortOrder: row.sort_order || 0,
      promptCount: row.prompt_count || 0,
      isSystem: this.deserializeValue(row.is_system, 'boolean'),
      metadata: {
        createdAt: new Date(row.created_at),
        updatedAt: new Date(row.updated_at),
        totalUsage: 0,
        customFields: {}
      },
      path: [], // Will be calculated when needed
      depth: 0, // Will be calculated when needed
      hasChildren: false // Will be calculated when needed
    };
  }

  /**
   * Override save to handle category-specific fields
   */
  async save(entity: Category): Promise<string> {
    const categoryEntity: CategoryEntity = {
      id: (entity as any).id || this.generateId(),
      name: entity.name,
      description: entity.description,
      sortOrder: entity.sortOrder || 0,
      promptCount: entity.promptCount || 0,
      isSystem: entity.isSystem || false,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...(entity.parentId && { parentId: entity.parentId }),
      ...(entity.color && { color: entity.color }),
      ...(entity.icon && { icon: entity.icon })
    };

    const columns = Object.keys(categoryEntity).filter(key => categoryEntity[key as keyof CategoryEntity] !== undefined);
    const placeholders = columns.map(() => '?').join(', ');
    const values = columns.map(col => this.serializeValue(categoryEntity[col as keyof CategoryEntity]));

    const sql = `INSERT INTO ${this.tableName} (${columns.map(col => this.camelToSnake(col)).join(', ')}) VALUES (${placeholders})`;

    await this.db.run(sql, values);
    return categoryEntity.id;
  }
}
