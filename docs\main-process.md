# Main Process Module Specification

## 🎯 Purpose

The Main Process module serves as the central coordinator for the Electron application, managing windows, system-level operations, and orchestrating communication between all other modules.

## 📋 Responsibilities

### Core Functions
- Application lifecycle management
- Window creation and management
- System tray integration
- Global hotkey registration
- IPC message routing
- Security policy enforcement

### System Integration
- OS-level API access
- File system operations
- Clipboard monitoring
- Screen capture coordination

## 🏗️ Architecture

### Class Structure
```typescript
class MainProcess {
  private windowManager: WindowManager;
  private hotkeyManager: HotkeyManager;
  private ipcRouter: IPCRouter;
  private systemTray: SystemTray;
  private configManager: ConfigurationManager;
  
  async initialize(): Promise<void>;
  async shutdown(): Promise<void>;
  handleAppReady(): void;
  handleWindowAllClosed(): void;
}
```

### Window Manager
```typescript
interface WindowManager {
  createMainWindow(): BrowserWindow;
  createFloatingWindow(): BrowserWindow;
  showWindow(windowId: string): void;
  hideWindow(windowId: string): void;
  closeWindow(windowId: string): void;
  getActiveWindow(): BrowserWindow | null;
}
```

### System Tray
```typescript
interface SystemTray {
  create(): void;
  updateIcon(iconPath: string): void;
  updateTooltip(text: string): void;
  showContextMenu(): void;
  destroy(): void;
}
```

## 🔧 Configuration

### Application Settings
```typescript
interface MainProcessConfig {
  windows: {
    main: WindowConfig;
    floating: WindowConfig;
  };
  systemTray: {
    enabled: boolean;
    iconPath: string;
    tooltip: string;
  };
  security: {
    nodeIntegration: boolean;
    contextIsolation: boolean;
    enableRemoteModule: boolean;
  };
  development: {
    devTools: boolean;
    hotReload: boolean;
  };
}
```

### Window Configuration
```typescript
interface WindowConfig {
  width: number;
  height: number;
  minWidth?: number;
  minHeight?: number;
  resizable: boolean;
  frame: boolean;
  transparent: boolean;
  alwaysOnTop: boolean;
  skipTaskbar: boolean;
  webPreferences: {
    nodeIntegration: boolean;
    contextIsolation: boolean;
    preload: string;
  };
}
```

## 📡 IPC Communication

### Message Types
```typescript
enum IPCMessageType {
  WINDOW_CONTROL = 'window-control',
  HOTKEY_TRIGGERED = 'hotkey-triggered',
  SYSTEM_NOTIFICATION = 'system-notification',
  APP_STATE_CHANGE = 'app-state-change',
  SERVICE_REQUEST = 'service-request',
  SERVICE_RESPONSE = 'service-response'
}
```

### Message Handlers
```typescript
interface IPCHandlers {
  'show-main-window': () => void;
  'hide-main-window': () => void;
  'show-floating-window': () => void;
  'minimize-to-tray': () => void;
  'quit-application': () => void;
  'get-app-version': () => string;
  'get-system-info': () => SystemInfo;
}
```

## ⌨️ Hotkey Management

### Global Hotkeys
```typescript
interface GlobalHotkeys {
  'show-floating-ui': string; // Default: 'Ctrl+Shift+P'
  'screen-capture': string;   // Default: 'Ctrl+Shift+O'
  'voice-input': string;      // Default: 'Ctrl+Shift+V'
  'quick-prompt': string;     // Default: 'Ctrl+Shift+Q'
}
```

### Hotkey Registration
```typescript
class HotkeyManager {
  registerHotkey(accelerator: string, callback: () => void): boolean;
  unregisterHotkey(accelerator: string): boolean;
  updateHotkey(oldAccelerator: string, newAccelerator: string): boolean;
  isHotkeyRegistered(accelerator: string): boolean;
  getRegisteredHotkeys(): string[];
}
```

## 🔒 Security Implementation

### Content Security Policy
```typescript
const CSP_POLICY = {
  'default-src': ["'self'"],
  'script-src': ["'self'", "'unsafe-inline'"],
  'style-src': ["'self'", "'unsafe-inline'"],
  'img-src': ["'self'", "data:", "https:"],
  'connect-src': ["'self'", "https://api.openai.com"]
};
```

### Preload Script Security
```typescript
// preload.ts
import { contextBridge, ipcRenderer } from 'electron';

contextBridge.exposeInMainWorld('electronAPI', {
  // Safe IPC methods only
  showMainWindow: () => ipcRenderer.invoke('show-main-window'),
  hideMainWindow: () => ipcRenderer.invoke('hide-main-window'),
  onHotkeyTriggered: (callback: Function) => 
    ipcRenderer.on('hotkey-triggered', callback)
});
```

## 🚀 Initialization Sequence

### Startup Flow
```typescript
async function initializeApplication(): Promise<void> {
  // 1. Load configuration
  await configManager.load();
  
  // 2. Initialize core services
  await logger.initialize();
  await storage.initialize();
  
  // 3. Setup IPC routing
  ipcRouter.registerHandlers();
  
  // 4. Register global hotkeys
  await hotkeyManager.registerAll();
  
  // 5. Create system tray
  systemTray.create();
  
  // 6. Create main window (hidden initially)
  windowManager.createMainWindow();
  
  // 7. Setup auto-updater
  await autoUpdater.initialize();
}
```

### Shutdown Flow
```typescript
async function shutdownApplication(): Promise<void> {
  // 1. Save application state
  await stateManager.save();
  
  // 2. Unregister hotkeys
  hotkeyManager.unregisterAll();
  
  // 3. Close all windows
  windowManager.closeAll();
  
  // 4. Cleanup system tray
  systemTray.destroy();
  
  // 5. Shutdown services
  await serviceManager.shutdownAll();
  
  // 6. Close database connections
  await storage.close();
}
```

## 📊 Performance Monitoring

### Metrics Collection
```typescript
interface PerformanceMetrics {
  startupTime: number;
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage: NodeJS.CpuUsage;
  windowCreationTime: number;
  ipcMessageLatency: number;
}
```

### Resource Management
```typescript
class ResourceManager {
  monitorMemoryUsage(): void;
  optimizeGarbageCollection(): void;
  trackWindowPerformance(): void;
  reportPerformanceMetrics(): PerformanceMetrics;
}
```

## 🧪 Testing Strategy

### Unit Tests
```typescript
describe('MainProcess', () => {
  test('should initialize all services', async () => {
    const mainProcess = new MainProcess();
    await mainProcess.initialize();
    expect(mainProcess.isInitialized()).toBe(true);
  });
  
  test('should handle window creation', () => {
    const window = windowManager.createMainWindow();
    expect(window).toBeInstanceOf(BrowserWindow);
  });
});
```

### Integration Tests
```typescript
describe('IPC Communication', () => {
  test('should route messages correctly', async () => {
    const response = await ipcRenderer.invoke('get-app-version');
    expect(response).toMatch(/^\d+\.\d+\.\d+$/);
  });
});
```

## 🔧 Error Handling

### Error Types
```typescript
enum MainProcessError {
  WINDOW_CREATION_FAILED = 'WINDOW_CREATION_FAILED',
  HOTKEY_REGISTRATION_FAILED = 'HOTKEY_REGISTRATION_FAILED',
  IPC_HANDLER_ERROR = 'IPC_HANDLER_ERROR',
  SYSTEM_TRAY_ERROR = 'SYSTEM_TRAY_ERROR'
}
```

### Error Recovery
```typescript
class ErrorRecovery {
  handleWindowCreationError(error: Error): void;
  handleHotkeyConflict(accelerator: string): void;
  handleIPCError(channel: string, error: Error): void;
  reportCriticalError(error: Error): void;
}
```
