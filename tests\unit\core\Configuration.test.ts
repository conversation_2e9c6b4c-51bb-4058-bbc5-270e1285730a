/**
 * Configuration module unit tests
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import { ConfigurationManager } from '../../../src/core/config/ConfigurationManager';
import { FileConfigurationStore } from '../../../src/core/config/ConfigurationStore';
import { DefaultConfigValidator, HotkeyValidator } from '../../../src/core/config/validators';
import { getDefaultConfig } from '../../../src/core/config/ConfigurationSchema';

// Mock file system operations
jest.mock('fs/promises');
const mockFs = fs as jest.Mocked<typeof fs>;

describe('ConfigurationManager', () => {
  let configManager: ConfigurationManager;
  let tempConfigPath: string;

  beforeEach(() => {
    tempConfigPath = path.join(__dirname, 'test-config.json');
    configManager = new ConfigurationManager({
      configPath: tempConfigPath,
      autoSave: false,
      watchChanges: false
    });

    // Reset mocks
    jest.clearAllMocks();
    
    // Mock file operations
    mockFs.readFile.mockResolvedValue(JSON.stringify(getDefaultConfig()));
    mockFs.writeFile.mockResolvedValue();
    mockFs.mkdir.mockResolvedValue(undefined);
    mockFs.access.mockResolvedValue();
    mockFs.copyFile.mockResolvedValue();
    mockFs.readdir.mockResolvedValue([]);
  });

  afterEach(async () => {
    try {
      await configManager.save();
    } catch {
      // Ignore errors during cleanup
    }
  });

  describe('Basic Operations', () => {
    test('should load configuration successfully', async () => {
      await configManager.load();
      
      expect(mockFs.readFile).toHaveBeenCalledWith(tempConfigPath, 'utf-8');
    });

    test('should get configuration values', async () => {
      await configManager.load();
      
      const theme = configManager.get<string>('general.theme');
      expect(theme).toBe('system');
      
      const language = configManager.get<string>('general.language');
      expect(language).toBe('en');
    });

    test('should set configuration values', async () => {
      await configManager.load();
      
      await configManager.set('general.theme', 'dark');
      const theme = configManager.get<string>('general.theme');
      
      expect(theme).toBe('dark');
    });

    test('should return default values for missing keys', async () => {
      await configManager.load();
      
      const nonExistent = configManager.get<string>('nonexistent.key');
      expect(nonExistent).toBeUndefined();
    });
  });

  describe('Validation', () => {
    test('should validate configuration values', async () => {
      await configManager.load();
      
      const validResult = configManager.validate('general.theme', 'dark');
      expect(validResult.valid).toBe(true);
      
      const invalidResult = configManager.validate('general.theme', 'invalid');
      expect(invalidResult.valid).toBe(false);
      expect(invalidResult.errors.length).toBeGreaterThan(0);
    });

    test('should prevent setting invalid values when validation is enabled', async () => {
      const validatingManager = new ConfigurationManager({
        configPath: tempConfigPath,
        validateOnSet: true,
        autoSave: false,
        watchChanges: false
      });
      
      await validatingManager.load();
      
      await expect(
        validatingManager.set('general.theme', 'invalid')
      ).rejects.toThrow('Validation failed');
    });
  });

  describe('Change Notifications', () => {
    test('should notify listeners on configuration changes', async () => {
      await configManager.load();
      
      const listener = jest.fn();
      const unsubscribe = configManager.onChange('general.theme', listener);
      
      await configManager.set('general.theme', 'dark');
      
      expect(listener).toHaveBeenCalledWith(
        expect.objectContaining({
          key: 'general.theme',
          oldValue: 'system',
          newValue: 'dark'
        })
      );
      
      unsubscribe();
    });

    test('should emit events on configuration changes', async () => {
      await configManager.load();
      
      const changeListener = jest.fn();
      configManager.on('changed', changeListener);
      
      await configManager.set('general.language', 'es');
      
      expect(changeListener).toHaveBeenCalledWith(
        expect.objectContaining({
          key: 'general.language',
          newValue: 'es'
        })
      );
    });
  });

  describe('Reset Functionality', () => {
    test('should reset specific configuration key to default', async () => {
      await configManager.load();
      
      // Change a value
      await configManager.set('general.theme', 'dark');
      expect(configManager.get('general.theme')).toBe('dark');
      
      // Reset to default
      await configManager.reset('general.theme');
      expect(configManager.get('general.theme')).toBe('system');
    });

    test('should reset all configuration to defaults', async () => {
      await configManager.load();
      
      // Change multiple values
      await configManager.set('general.theme', 'dark');
      await configManager.set('general.language', 'es');
      
      // Reset all
      await configManager.reset();
      
      expect(configManager.get('general.theme')).toBe('system');
      expect(configManager.get('general.language')).toBe('en');
    });
  });
});

describe('FileConfigurationStore', () => {
  let store: FileConfigurationStore;
  let tempPath: string;

  beforeEach(() => {
    tempPath = path.join(__dirname, 'test-store.json');
    store = new FileConfigurationStore(tempPath);
  });

  test('should load configuration from file', async () => {
    const testConfig = { test: 'value' };
    mockFs.readFile.mockResolvedValue(JSON.stringify(testConfig));
    mockFs.access.mockResolvedValue();
    
    const config = await store.load();
    
    expect(config).toEqual(testConfig);
  });

  test('should save configuration to file', async () => {
    const testConfig = { test: 'value' };
    
    await store.save(testConfig);
    
    expect(mockFs.writeFile).toHaveBeenCalledWith(
      tempPath,
      JSON.stringify(testConfig, null, 2),
      'utf-8'
    );
  });

  test('should get nested configuration values', async () => {
    await store.load();
    await store.set('section.key', 'value');
    
    const value = store.get('section.key');
    expect(value).toBe('value');
  });

  test('should set nested configuration values', async () => {
    await store.load();
    
    await store.set('deep.nested.key', 'value');
    const value = store.get('deep.nested.key');
    
    expect(value).toBe('value');
  });
});

describe('Validators', () => {
  describe('DefaultConfigValidator', () => {
    let validator: DefaultConfigValidator;

    beforeEach(() => {
      validator = new DefaultConfigValidator();
    });

    test('should validate string types', () => {
      const setting = {
        type: 'string' as const,
        title: 'Test String',
        description: 'Test',
        defaultValue: 'default',
        required: true,
        category: 'basic' as const
      };

      const validResult = validator.validate('test', setting);
      expect(validResult.valid).toBe(true);

      const invalidResult = validator.validate(123, setting);
      expect(invalidResult.valid).toBe(false);
    });

    test('should validate enum types', () => {
      const setting = {
        type: 'enum' as const,
        title: 'Test Enum',
        description: 'Test',
        defaultValue: 'option1',
        required: true,
        options: ['option1', 'option2', 'option3'],
        category: 'basic' as const
      };

      const validResult = validator.validate('option2', setting);
      expect(validResult.valid).toBe(true);

      const invalidResult = validator.validate('invalid', setting);
      expect(invalidResult.valid).toBe(false);
    });

    test('should validate required fields', () => {
      const setting = {
        type: 'string' as const,
        title: 'Required Field',
        description: 'Test',
        defaultValue: 'default',
        required: true,
        category: 'basic' as const
      };

      const invalidResult = validator.validate(null, setting);
      expect(invalidResult.valid).toBe(false);
      expect(invalidResult.errors[0]).toContain('required');
    });
  });

  describe('HotkeyValidator', () => {
    let validator: HotkeyValidator;

    beforeEach(() => {
      validator = new HotkeyValidator();
    });

    test('should validate correct hotkey format', () => {
      const setting = {
        type: 'string' as const,
        title: 'Hotkey',
        description: 'Test',
        defaultValue: 'Ctrl+Shift+P',
        required: true,
        category: 'basic' as const
      };

      const validResult = validator.validate('Ctrl+Shift+P', setting);
      expect(validResult.valid).toBe(true);

      const validResult2 = validator.validate('Alt+F4', setting);
      expect(validResult2.valid).toBe(true);
    });

    test('should reject invalid hotkey format', () => {
      const setting = {
        type: 'string' as const,
        title: 'Hotkey',
        description: 'Test',
        defaultValue: 'Ctrl+Shift+P',
        required: true,
        category: 'basic' as const
      };

      const invalidResult = validator.validate('invalid', setting);
      expect(invalidResult.valid).toBe(false);

      const invalidResult2 = validator.validate('Ctrl+', setting);
      expect(invalidResult2.valid).toBe(false);
    });
  });
});
