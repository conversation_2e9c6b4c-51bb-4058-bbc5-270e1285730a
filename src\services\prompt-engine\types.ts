/**
 * Prompt Engine module type definitions
 * Core prompt enhancement, processing, and transformation capabilities
 */

// Core interfaces
export interface IPromptEngine {
  enhance(prompt: string, options?: EnhancementOptions): Promise<string>;
  processTemplate(template: PromptTemplate, variables: Record<string, any>): Promise<string>;
  injectContext(prompt: string, contextSources: ContextSource[]): Promise<string>;
  generateVariations(prompt: string, count: number): Promise<string[]>;
  summarize(text: string, options?: SummarizationOptions): Promise<string>;
  translate(text: string, targetLanguage: string): Promise<string>;
}

// Enhancement options
export interface EnhancementOptions {
  style?: 'professional' | 'casual' | 'technical' | 'creative';
  length?: 'concise' | 'detailed' | 'comprehensive';
  tone?: 'formal' | 'friendly' | 'assertive' | 'questioning';
  context?: ContextSource[];
  temperature?: number;
  maxTokens?: number;
  model?: string;
  preserveStructure?: boolean;
  addExamples?: boolean;
  includeInstructions?: boolean;
}

// Prompt template interfaces
export interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  template: string;
  variables: TemplateVariable[];
  category: string;
  tags: string[];
  metadata: TemplateMetadata;
  version?: number;
  parentId?: string;
}

export interface TemplateVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  required: boolean;
  defaultValue?: any;
  description: string;
  validation?: ValidationRule[];
  placeholder?: string;
  options?: string[]; // For enum-like variables
}

export interface ValidationRule {
  type: 'minLength' | 'maxLength' | 'pattern' | 'range' | 'custom';
  value: any;
  message: string;
}

export interface TemplateMetadata {
  author: string;
  version: string;
  createdAt: Date;
  updatedAt: Date;
  usage?: number;
  rating?: number;
  tags?: string[];
}

// Template processing
export interface TemplateValidationResult {
  valid: boolean;
  error?: string;
  warnings?: string[];
  missingVariables?: string[];
}

export interface TemplateProcessingResult {
  result: string;
  usedVariables: string[];
  warnings?: string[];
}

// Context integration
export enum ContextSource {
  CLIPBOARD = 'clipboard',
  SCREEN_CAPTURE = 'screen-capture',
  ACTIVE_WINDOW = 'active-window',
  FILE_CONTENT = 'file-content',
  SELECTED_TEXT = 'selected-text',
  CONVERSATION_HISTORY = 'conversation-history',
  BROWSER_TAB = 'browser-tab',
  SYSTEM_INFO = 'system-info'
}

export interface ContextData {
  source: ContextSource;
  content: string;
  metadata: {
    timestamp: Date;
    sourceInfo?: any;
    confidence?: number;
    size?: number;
  };
}

export interface ContextExtractionOptions {
  maxLength?: number;
  includeMetadata?: boolean;
  filterSensitive?: boolean;
  format?: 'plain' | 'markdown' | 'structured';
}

// AI service interfaces
export interface AIOptions {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stop?: string[];
  stream?: boolean;
}

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
  name?: string;
  timestamp?: Date;
}

export interface AIResponse {
  content: string;
  model: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  finishReason: string;
}

export interface IAIService {
  enhance(prompt: string, options: AIOptions): Promise<string>;
  complete(messages: ChatMessage[], options?: AIOptions): Promise<AIResponse>;
  generateVariations(prompt: string, count: number, options?: AIOptions): Promise<string[]>;
  summarize(text: string, options?: AIOptions): Promise<string>;
  translate(text: string, targetLanguage: string, options?: AIOptions): Promise<string>;
  isAvailable(): Promise<boolean>;
}

// Summarization options
export interface SummarizationOptions {
  length?: 'short' | 'medium' | 'long';
  style?: 'bullet-points' | 'paragraph' | 'key-points';
  audience?: string;
  focus?: string[];
  preserveStructure?: boolean;
}

// Caching
export interface CacheEntry {
  value: string;
  timestamp: number;
  hits: number;
  size: number;
}

export interface CacheOptions {
  maxSize: number;
  ttl: number; // Time to live in milliseconds
  maxEntries: number;
}

// Configuration
export interface PromptEngineConfig {
  ai: {
    provider: 'openai' | 'anthropic' | 'local';
    apiKey?: string;
    baseURL?: string;
    defaultModel: string;
    timeout: number;
    retries: number;
  };
  cache: CacheOptions;
  templates: {
    builtInEnabled: boolean;
    customPath?: string;
    autoSave: boolean;
  };
  context: {
    maxSources: number;
    maxContentLength: number;
    enableSensitiveFilter: boolean;
  };
  enhancement: {
    defaultStyle: string;
    defaultLength: string;
    defaultTone: string;
    maxVariations: number;
  };
}

// Template helpers
export interface TemplateHelper {
  name: string;
  description: string;
  execute: (...args: any[]) => any;
}

// Built-in helpers
export interface BuiltInHelpers {
  formatDate: TemplateHelper;
  uppercase: TemplateHelper;
  lowercase: TemplateHelper;
  capitalize: TemplateHelper;
  ifEquals: TemplateHelper;
  join: TemplateHelper;
  truncate: TemplateHelper;
  replace: TemplateHelper;
  split: TemplateHelper;
  length: TemplateHelper;
}

// Conversation management
export interface ConversationContext {
  id: string;
  messages: ChatMessage[];
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    title?: string;
    tags?: string[];
  };
}

export interface ConversationOptions {
  maxMessages?: number;
  includeSystemMessages?: boolean;
  summarizeOldMessages?: boolean;
}

// Performance metrics
export interface PromptEngineMetrics {
  totalEnhancements: number;
  totalTemplateProcessing: number;
  totalContextInjections: number;
  averageResponseTime: number;
  cacheHitRate: number;
  errorRate: number;
  lastResetTime: Date;
}

// Error types
export class PromptEngineError extends Error {
  constructor(message: string, public readonly cause?: Error) {
    super(message);
    this.name = 'PromptEngineError';
  }
}

export class TemplateProcessingError extends PromptEngineError {
  constructor(message: string, cause?: Error) {
    super(message, cause);
    this.name = 'TemplateProcessingError';
  }
}

export class AIServiceError extends PromptEngineError {
  constructor(message: string, cause?: Error) {
    super(message, cause);
    this.name = 'AIServiceError';
  }
}

export class ContextExtractionError extends PromptEngineError {
  constructor(message: string, cause?: Error) {
    super(message, cause);
    this.name = 'ContextExtractionError';
  }
}

// Event types
export type PromptEngineEvent = 
  | 'enhancement-start'
  | 'enhancement-complete'
  | 'template-processed'
  | 'context-injected'
  | 'variation-generated'
  | 'error'
  | 'cache-hit'
  | 'cache-miss';

// Batch processing
export interface BatchProcessingOptions {
  concurrency?: number;
  retryFailures?: boolean;
  progressCallback?: (completed: number, total: number) => void;
}

export interface BatchProcessingResult<T> {
  successful: T[];
  failed: Array<{ input: any; error: Error }>;
  totalProcessed: number;
  processingTime: number;
}

// Plugin system
export interface PromptEnginePlugin {
  name: string;
  version: string;
  description: string;
  initialize(engine: IPromptEngine): Promise<void>;
  cleanup(): Promise<void>;
}

// Export utilities
export interface ExportOptions {
  format: 'json' | 'yaml' | 'markdown';
  includeMetadata: boolean;
  includeTemplates: boolean;
  includeConversations: boolean;
}

export interface ImportOptions {
  mergeStrategy: 'skip' | 'overwrite' | 'merge';
  validateTemplates: boolean;
  preserveIds: boolean;
}

// Analytics
export interface PromptAnalytics {
  promptId?: string;
  originalLength: number;
  enhancedLength: number;
  improvementScore?: number;
  processingTime: number;
  tokensUsed: number;
  model: string;
  timestamp: Date;
}
