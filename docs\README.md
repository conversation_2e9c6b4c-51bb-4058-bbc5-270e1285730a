# PromptPilot Desktop - Documentation

This directory contains comprehensive technical documentation for the PromptPilot Desktop application.

## 📁 Documentation Structure

### 🏗️ Architecture Overview
- [`system-architecture.md`](./system-architecture.md) - Overall system design and component relationships
- [`module-dependency-map.md`](./module-dependency-map.md) - Inter-module communication patterns

### 🔧 Core Application Modules
- [`main-process.md`](./main-process.md) - Electron main process specification
- [`renderer-process.md`](./renderer-process.md) - UI component architecture
- [`ipc-communication.md`](./ipc-communication.md) - Inter-process communication

### 🚀 Feature Modules
- [`prompt-engine.md`](./prompt-engine.md) - Prompt enhancement and processing
- [`context-extractor.md`](./context-extractor.md) - Screen capture and OCR
- [`voice-module.md`](./voice-module.md) - Speech-to-text functionality
- [`prompt-library.md`](./prompt-library.md) - Prompt storage and management
- [`storage-module.md`](./storage-module.md) - Database and persistence
- [`hotkey-manager.md`](./hotkey-manager.md) - Global hotkey management

### 🌐 API & Integration Modules
- [`ai-api-bridge.md`](./ai-api-bridge.md) - AI service integration
- [`stt-bridge.md`](./stt-bridge.md) - Speech-to-text service bridge

### ⚙️ Utility & Support Modules
- [`configuration.md`](./configuration.md) - Settings and configuration
- [`logger.md`](./logger.md) - Logging system
- [`security.md`](./security.md) - Security and encryption
- [`filesystem.md`](./filesystem.md) - File system operations

### 📊 Data & Interface Specifications
- [`database-schema.md`](./database-schema.md) - Database structure
- [`api-interfaces.md`](./api-interfaces.md) - API contracts
- [`data-models.md`](./data-models.md) - Data structures and models

### 🧪 Testing & Quality
- [`testing-strategy.md`](./testing-strategy.md) - Testing approach
- [`error-handling.md`](./error-handling.md) - Error management

### 📦 Deployment & Build
- [`build-config.md`](./build-config.md) - Build configuration
- [`installation-update.md`](./installation-update.md) - Installation and updates

### 🔌 Extension & Plugin
- [`plugin-architecture.md`](./plugin-architecture.md) - Plugin system design

## 🚀 Quick Start

1. Start with [`system-architecture.md`](./system-architecture.md) for overall understanding
2. Review [`module-dependency-map.md`](./module-dependency-map.md) for component relationships
3. Dive into specific module documentation as needed

## 📝 Document Conventions

- All code examples use TypeScript/JavaScript
- API endpoints include OpenAI-compatible placeholders
- Configuration examples use JSON format
- Database schemas use SQLite syntax
