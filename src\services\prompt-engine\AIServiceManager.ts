/**
 * AI Service Manager implementation
 * Manages AI service providers with OpenAI-compatible interface
 */

import {
  IAIService,
  AIOptions,
  ChatMessage,
  AIResponse,
  AIServiceError
} from './types';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('AIServiceManager');

export interface AIServiceConfig {
  provider: 'openai' | 'anthropic' | 'local';
  apiKey?: string;
  baseURL?: string;
  defaultModel: string;
  timeout: number;
  retries: number;
}

/**
 * AI Service Manager with OpenAI-compatible implementation
 */
export class AIServiceManager implements IAIService {
  private config: AIServiceConfig;

  constructor(config: AIServiceConfig) {
    this.config = config;
  }

  /**
   * Initialize the AI service
   */
  async initialize(): Promise<void> {
    try {
      const isAvailable = await this.isAvailable();
      if (!isAvailable) {
        throw new AIServiceError('AI service is not available');
      }
      
      logger.info('AI service initialized', { provider: this.config.provider });
    } catch (error) {
      logger.error('Failed to initialize AI service', { error });
      throw new AIServiceError('Failed to initialize AI service', error);
    }
  }

  /**
   * Enhance a prompt using AI
   */
  async enhance(prompt: string, options: AIOptions & { style?: string; length?: string; tone?: string }): Promise<string> {
    try {
      const systemPrompt = this.buildEnhancementSystemPrompt(options);
      
      const messages: ChatMessage[] = [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: `Please enhance this prompt: ${prompt}`
        }
      ];

      const response = await this.complete(messages, options);
      return response.content;
    } catch (error) {
      logger.error('Failed to enhance prompt', { error, prompt: prompt.substring(0, 100) });
      throw new AIServiceError('Failed to enhance prompt', error);
    }
  }

  /**
   * Complete a chat conversation
   */
  async complete(messages: ChatMessage[], options: AIOptions = {}): Promise<AIResponse> {
    try {
      const requestOptions = {
        model: options.model || this.config.defaultModel,
        temperature: options.temperature || 0.7,
        max_tokens: options.maxTokens || 1000,
        top_p: options.topP,
        frequency_penalty: options.frequencyPenalty,
        presence_penalty: options.presencePenalty,
        stop: options.stop
      };

      const response = await this.makeRequest('/chat/completions', {
        ...requestOptions,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content,
          name: msg.name
        }))
      });

      return {
        content: response.choices[0].message.content,
        model: response.model,
        usage: {
          promptTokens: response.usage?.prompt_tokens || 0,
          completionTokens: response.usage?.completion_tokens || 0,
          totalTokens: response.usage?.total_tokens || 0
        },
        finishReason: response.choices[0].finish_reason
      };
    } catch (error) {
      logger.error('Failed to complete chat', { error, messageCount: messages.length });
      throw new AIServiceError('Failed to complete chat', error);
    }
  }

  /**
   * Generate variations of a prompt
   */
  async generateVariations(prompt: string, count: number, options: AIOptions = {}): Promise<string[]> {
    try {
      const systemPrompt = `You are a helpful assistant that generates creative variations of prompts. 
Generate ${count} different variations of the given prompt while maintaining the core intent. 
Each variation should be unique and offer a different perspective or approach.
Return only the variations, one per line, without numbering or additional text.`;

      const messages: ChatMessage[] = [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: prompt
        }
      ];

      const response = await this.complete(messages, {
        ...options,
        temperature: 0.8 // Higher temperature for more variety
      });

      // Parse variations from response
      const variations = response.content
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0)
        .slice(0, count);

      return variations;
    } catch (error) {
      logger.error('Failed to generate variations', { error, prompt: prompt.substring(0, 100) });
      throw new AIServiceError('Failed to generate variations', error);
    }
  }

  /**
   * Summarize text
   */
  async summarize(text: string, options: AIOptions & { length?: string; style?: string; audience?: string } = {}): Promise<string> {
    try {
      let systemPrompt = 'You are a helpful assistant that creates concise and accurate summaries.';
      
      if (options.length) {
        systemPrompt += ` Create a ${options.length} summary.`;
      }
      
      if (options.style) {
        systemPrompt += ` Use ${options.style} format.`;
      }
      
      if (options.audience) {
        systemPrompt += ` Target audience: ${options.audience}.`;
      }

      const messages: ChatMessage[] = [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: `Please summarize the following text:\n\n${text}`
        }
      ];

      const response = await this.complete(messages, options);
      return response.content;
    } catch (error) {
      logger.error('Failed to summarize text', { error, textLength: text.length });
      throw new AIServiceError('Failed to summarize text', error);
    }
  }

  /**
   * Translate text
   */
  async translate(text: string, targetLanguage: string, options: AIOptions = {}): Promise<string> {
    try {
      const systemPrompt = `You are a professional translator. Translate the given text to ${targetLanguage}. 
Maintain the original meaning, tone, and style as much as possible. 
Return only the translated text without any additional commentary.`;

      const messages: ChatMessage[] = [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: text
        }
      ];

      const response = await this.complete(messages, options);
      return response.content;
    } catch (error) {
      logger.error('Failed to translate text', { error, targetLanguage });
      throw new AIServiceError('Failed to translate text', error);
    }
  }

  /**
   * Check if AI service is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      // For placeholder implementation, always return true
      // In real implementation, this would test the API connection
      return true;
    } catch (error) {
      logger.error('AI service availability check failed', { error });
      return false;
    }
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<AIServiceConfig>): void {
    this.config = { ...this.config, ...config };
    logger.info('AI service configuration updated');
  }

  /**
   * Build system prompt for enhancement
   */
  private buildEnhancementSystemPrompt(options: { style?: string; length?: string; tone?: string }): string {
    let systemPrompt = 'You are an expert prompt engineer. Your task is to enhance and improve prompts to make them more effective, clear, and specific.';

    if (options.style) {
      systemPrompt += ` Use a ${options.style} style.`;
    }

    if (options.tone) {
      systemPrompt += ` Maintain a ${options.tone} tone.`;
    }

    if (options.length) {
      systemPrompt += ` Make the enhanced prompt ${options.length}.`;
    }

    systemPrompt += ' Focus on clarity, specificity, and effectiveness. Return only the enhanced prompt without additional commentary.';

    return systemPrompt;
  }

  /**
   * Make HTTP request to AI service
   */
  private async makeRequest(endpoint: string, data: any): Promise<any> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.config.retries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

        // Placeholder implementation - to be supplemented with actual API calls
        const baseURL = this.config.baseURL || 'https://api.openai.com/v1';
        
        const response = await fetch(`${baseURL}${endpoint}`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.config.apiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(data),
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < this.config.retries) {
          const delay = Math.pow(2, attempt - 1) * 1000; // Exponential backoff
          logger.warn(`AI service request failed, retrying in ${delay}ms`, { 
            attempt, 
            error: error.message,
            endpoint 
          });
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw new AIServiceError(`AI service request failed after ${this.config.retries} attempts`, lastError!);
  }
}
