/**
 * IPC Handlers
 * Main process handlers for all IPC operations
 */

import { BrowserWindow, dialog, clipboard, screen } from 'electron';
import { IPCRouter } from './IPCRouter';
import { IPCMessageType } from './types';
import { logger } from '../../core/logger';

// Import types for handlers
import type { Prompt } from '../../types/models/Prompt';
import type { Category } from '../../types/models/Category';
import type { User } from '../../types/models/User';
import type { Setting } from '../../storage/repositories/SettingsRepository';

/**
 * Main process IPC handlers
 */
export class IPCHandlers {
  private router: IPCRouter;
  private mainWindow: BrowserWindow | null = null;

  // Service dependencies (will be injected)
  private promptService: any = null;
  private categoryService: any = null;
  private userService: any = null;
  private settingsService: any = null;
  private voiceService: any = null;
  private aiService: any = null;

  constructor(router: <PERSON>CRouter) {
    this.router = router;
  }

  /**
   * Set main window reference
   */
  setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window;
  }

  /**
   * Inject service dependencies
   */
  injectServices(services: {
    promptService?: any;
    categoryService?: any;
    userService?: any;
    settingsService?: any;
    voiceService?: any;
    aiService?: any;
  }): void {
    Object.assign(this, services);
  }

  /**
   * Register all IPC handlers
   */
  registerAllHandlers(): void {
    this.registerWindowHandlers();
    this.registerPromptHandlers();
    this.registerCategoryHandlers();
    this.registerUserHandlers();
    this.registerSettingsHandlers();
    this.registerVoiceHandlers();
    this.registerContextHandlers();
    this.registerAIHandlers();
    this.registerSystemHandlers();
    this.registerFileHandlers();

    logger.info('All IPC handlers registered');
  }

  /**
   * Window management handlers
   */
  private registerWindowHandlers(): void {
    this.router.registerHandler(IPCMessageType.WINDOW_SHOW, this.handleWindowShow.bind(this));
    this.router.registerHandler(IPCMessageType.WINDOW_HIDE, this.handleWindowHide.bind(this));
    this.router.registerHandler(IPCMessageType.WINDOW_MINIMIZE, this.handleWindowMinimize.bind(this));
    this.router.registerHandler(IPCMessageType.WINDOW_MAXIMIZE, this.handleWindowMaximize.bind(this));
    this.router.registerHandler(IPCMessageType.WINDOW_CLOSE, this.handleWindowClose.bind(this));
    this.router.registerHandler(IPCMessageType.WINDOW_TOGGLE_DEVTOOLS, this.handleToggleDevTools.bind(this));
  }

  /**
   * Prompt operation handlers
   */
  private registerPromptHandlers(): void {
    this.router.registerHandler(IPCMessageType.PROMPT_SAVE, this.handlePromptSave.bind(this));
    this.router.registerHandler(IPCMessageType.PROMPT_LOAD, this.handlePromptLoad.bind(this));
    this.router.registerHandler(IPCMessageType.PROMPT_LOAD_ALL, this.handlePromptLoadAll.bind(this));
    this.router.registerHandler(IPCMessageType.PROMPT_DELETE, this.handlePromptDelete.bind(this));
    this.router.registerHandler(IPCMessageType.PROMPT_ENHANCE, this.handlePromptEnhance.bind(this));
    this.router.registerHandler(IPCMessageType.PROMPT_SEARCH, this.handlePromptSearch.bind(this));
    this.router.registerHandler(IPCMessageType.PROMPT_DUPLICATE, this.handlePromptDuplicate.bind(this));
    this.router.registerHandler(IPCMessageType.PROMPT_EXPORT, this.handlePromptExport.bind(this));
    this.router.registerHandler(IPCMessageType.PROMPT_IMPORT, this.handlePromptImport.bind(this));
  }

  /**
   * Category operation handlers
   */
  private registerCategoryHandlers(): void {
    this.router.registerHandler(IPCMessageType.CATEGORY_SAVE, this.handleCategorySave.bind(this));
    this.router.registerHandler(IPCMessageType.CATEGORY_LOAD_ALL, this.handleCategoryLoadAll.bind(this));
    this.router.registerHandler(IPCMessageType.CATEGORY_DELETE, this.handleCategoryDelete.bind(this));
    this.router.registerHandler(IPCMessageType.CATEGORY_MOVE, this.handleCategoryMove.bind(this));
  }

  /**
   * User operation handlers
   */
  private registerUserHandlers(): void {
    this.router.registerHandler(IPCMessageType.USER_GET_CURRENT, this.handleUserGetCurrent.bind(this));
    this.router.registerHandler(IPCMessageType.USER_UPDATE_PREFERENCES, this.handleUserUpdatePreferences.bind(this));
    this.router.registerHandler(IPCMessageType.USER_GET_STATISTICS, this.handleUserGetStatistics.bind(this));
  }

  /**
   * Settings operation handlers
   */
  private registerSettingsHandlers(): void {
    this.router.registerHandler(IPCMessageType.SETTINGS_GET, this.handleSettingsGet.bind(this));
    this.router.registerHandler(IPCMessageType.SETTINGS_SET, this.handleSettingsSet.bind(this));
    this.router.registerHandler(IPCMessageType.SETTINGS_RESET, this.handleSettingsReset.bind(this));
    this.router.registerHandler(IPCMessageType.SETTINGS_GET_ALL, this.handleSettingsGetAll.bind(this));
  }

  /**
   * Voice operation handlers
   */
  private registerVoiceHandlers(): void {
    this.router.registerHandler(IPCMessageType.VOICE_START_RECORDING, this.handleVoiceStartRecording.bind(this));
    this.router.registerHandler(IPCMessageType.VOICE_STOP_RECORDING, this.handleVoiceStopRecording.bind(this));
  }

  /**
   * Context operation handlers
   */
  private registerContextHandlers(): void {
    this.router.registerHandler(IPCMessageType.CONTEXT_CAPTURE_SCREEN, this.handleContextCaptureScreen.bind(this));
    this.router.registerHandler(IPCMessageType.CONTEXT_GET_CLIPBOARD, this.handleContextGetClipboard.bind(this));
    this.router.registerHandler(IPCMessageType.CONTEXT_EXTRACT_TEXT, this.handleContextExtractText.bind(this));
    this.router.registerHandler(IPCMessageType.CONTEXT_GET_ACTIVE_WINDOW, this.handleContextGetActiveWindow.bind(this));
  }

  /**
   * AI operation handlers
   */
  private registerAIHandlers(): void {
    this.router.registerHandler(IPCMessageType.AI_GENERATE, this.handleAIGenerate.bind(this));
    this.router.registerHandler(IPCMessageType.AI_ENHANCE, this.handleAIEnhance.bind(this));
    this.router.registerHandler(IPCMessageType.AI_ANALYZE, this.handleAIAnalyze.bind(this));
    this.router.registerHandler(IPCMessageType.AI_PROVIDERS_LIST, this.handleAIProvidersList.bind(this));
    this.router.registerHandler(IPCMessageType.AI_MODELS_LIST, this.handleAIModelsList.bind(this));
  }

  /**
   * System operation handlers
   */
  private registerSystemHandlers(): void {
    this.router.registerHandler(IPCMessageType.SYSTEM_GET_INFO, this.handleSystemGetInfo.bind(this));
    this.router.registerHandler(IPCMessageType.SYSTEM_NOTIFICATION, this.handleSystemNotification.bind(this));
    this.router.registerHandler(IPCMessageType.SYSTEM_HOTKEY_REGISTER, this.handleSystemHotkeyRegister.bind(this));
    this.router.registerHandler(IPCMessageType.SYSTEM_HOTKEY_UNREGISTER, this.handleSystemHotkeyUnregister.bind(this));
  }

  /**
   * File operation handlers
   */
  private registerFileHandlers(): void {
    this.router.registerHandler(IPCMessageType.FILE_DIALOG_OPEN, this.handleFileDialogOpen.bind(this));
    this.router.registerHandler(IPCMessageType.FILE_DIALOG_SAVE, this.handleFileDialogSave.bind(this));
    this.router.registerHandler(IPCMessageType.FILE_READ, this.handleFileRead.bind(this));
    this.router.registerHandler(IPCMessageType.FILE_WRITE, this.handleFileWrite.bind(this));
  }

  // Window Handlers
  private async handleWindowShow(): Promise<void> {
    this.mainWindow?.show();
  }

  private async handleWindowHide(): Promise<void> {
    this.mainWindow?.hide();
  }

  private async handleWindowMinimize(): Promise<void> {
    this.mainWindow?.minimize();
  }

  private async handleWindowMaximize(): Promise<void> {
    if (this.mainWindow?.isMaximized()) {
      this.mainWindow.unmaximize();
    } else {
      this.mainWindow?.maximize();
    }
  }

  private async handleWindowClose(): Promise<void> {
    this.mainWindow?.close();
  }

  private async handleToggleDevTools(): Promise<void> {
    if (this.mainWindow?.webContents.isDevToolsOpened()) {
      this.mainWindow.webContents.closeDevTools();
    } else {
      this.mainWindow?.webContents.openDevTools();
    }
  }

  // Prompt Handlers
  private async handlePromptSave(prompt: Prompt): Promise<string> {
    if (!this.promptService) {
      throw new Error('Prompt service not available');
    }
    return await this.promptService.save(prompt);
  }

  private async handlePromptLoad(id: string): Promise<Prompt | null> {
    if (!this.promptService) {
      throw new Error('Prompt service not available');
    }
    return await this.promptService.findById(id);
  }

  private async handlePromptLoadAll(): Promise<Prompt[]> {
    if (!this.promptService) {
      throw new Error('Prompt service not available');
    }
    return await this.promptService.findAll();
  }

  private async handlePromptDelete(id: string): Promise<void> {
    if (!this.promptService) {
      throw new Error('Prompt service not available');
    }
    await this.promptService.delete(id);
  }

  private async handlePromptEnhance(text: string): Promise<string> {
    if (!this.promptService) {
      throw new Error('Prompt service not available');
    }
    return await this.promptService.enhance(text);
  }

  private async handlePromptSearch(query: string): Promise<Prompt[]> {
    if (!this.promptService) {
      throw new Error('Prompt service not available');
    }
    return await this.promptService.search(query);
  }

  private async handlePromptDuplicate(id: string): Promise<string> {
    if (!this.promptService) {
      throw new Error('Prompt service not available');
    }
    return await this.promptService.duplicate(id);
  }

  private async handlePromptExport(ids: string[]): Promise<string> {
    if (!this.promptService) {
      throw new Error('Prompt service not available');
    }
    return await this.promptService.export(ids);
  }

  private async handlePromptImport(data: string): Promise<string[]> {
    if (!this.promptService) {
      throw new Error('Prompt service not available');
    }
    return await this.promptService.import(data);
  }

  // Context Handlers (Basic implementations)
  private async handleContextCaptureScreen(): Promise<string> {
    try {
      const sources = await screen.getAllDisplays();
      const primaryDisplay = sources.find(display => display.bounds.x === 0 && display.bounds.y === 0) || sources[0];
      
      if (!primaryDisplay) {
        throw new Error('No display found');
      }

      // This is a placeholder - actual screen capture would require additional setup
      return `Screen capture from display ${primaryDisplay.id}`;
    } catch (error) {
      logger.error('Screen capture failed', { error });
      throw error;
    }
  }

  private async handleContextGetClipboard(): Promise<string> {
    return clipboard.readText();
  }

  private async handleContextExtractText(data: any): Promise<string> {
    // Placeholder for text extraction
    return `Extracted text from: ${JSON.stringify(data)}`;
  }

  private async handleContextGetActiveWindow(): Promise<any> {
    // Placeholder for active window detection
    return { title: 'Active Window', processName: 'unknown' };
  }

  // System Handlers
  private async handleSystemGetInfo(): Promise<any> {
    return {
      platform: process.platform,
      arch: process.arch,
      version: process.version,
      electronVersion: process.versions.electron,
      nodeVersion: process.versions.node
    };
  }

  private async handleSystemNotification(notification: any): Promise<void> {
    // Placeholder for system notifications
    logger.info('System notification', notification);
  }

  private async handleSystemHotkeyRegister(hotkey: any): Promise<void> {
    // Placeholder for hotkey registration
    logger.info('Hotkey registered', hotkey);
  }

  private async handleSystemHotkeyUnregister(hotkey: any): Promise<void> {
    // Placeholder for hotkey unregistration
    logger.info('Hotkey unregistered', hotkey);
  }

  // File Handlers
  private async handleFileDialogOpen(options: any): Promise<string[] | null> {
    const result = await dialog.showOpenDialog(this.mainWindow!, options);
    return result.canceled ? null : result.filePaths;
  }

  private async handleFileDialogSave(options: any): Promise<string | null> {
    const result = await dialog.showSaveDialog(this.mainWindow!, options);
    return result.canceled ? null : result.filePath!;
  }

  private async handleFileRead(path: string): Promise<string> {
    // Placeholder - would use fs.readFile
    return `File content from: ${path}`;
  }

  private async handleFileWrite(data: { path: string; content: string }): Promise<void> {
    // Placeholder - would use fs.writeFile
    logger.info('File written', { path: data.path, contentLength: data.content.length });
  }

  // Category Handlers
  private async handleCategorySave(category: Category): Promise<string> {
    if (!this.categoryService) {
      throw new Error('Category service not available');
    }
    return await this.categoryService.save(category);
  }

  private async handleCategoryLoadAll(): Promise<Category[]> {
    if (!this.categoryService) {
      throw new Error('Category service not available');
    }
    return await this.categoryService.findAll();
  }

  private async handleCategoryDelete(id: string): Promise<void> {
    if (!this.categoryService) {
      throw new Error('Category service not available');
    }
    await this.categoryService.delete(id);
  }

  private async handleCategoryMove(data: { id: string; newParentId: string | null }): Promise<void> {
    if (!this.categoryService) {
      throw new Error('Category service not available');
    }
    await this.categoryService.move(data.id, data.newParentId);
  }

  // User Handlers
  private async handleUserGetCurrent(): Promise<User | null> {
    if (!this.userService) {
      throw new Error('User service not available');
    }
    return await this.userService.getCurrentUser();
  }

  private async handleUserUpdatePreferences(preferences: any): Promise<void> {
    if (!this.userService) {
      throw new Error('User service not available');
    }
    await this.userService.updatePreferences(preferences);
  }

  private async handleUserGetStatistics(): Promise<any> {
    if (!this.userService) {
      throw new Error('User service not available');
    }
    return await this.userService.getStatistics();
  }

  // Settings Handlers
  private async handleSettingsGet(key: string): Promise<any> {
    if (!this.settingsService) {
      throw new Error('Settings service not available');
    }
    return await this.settingsService.get(key);
  }

  private async handleSettingsSet(key: string, value: any): Promise<void> {
    if (!this.settingsService) {
      throw new Error('Settings service not available');
    }
    await this.settingsService.set(key, value);
  }

  private async handleSettingsReset(key?: string): Promise<void> {
    if (!this.settingsService) {
      throw new Error('Settings service not available');
    }
    await this.settingsService.reset(key);
  }

  private async handleSettingsGetAll(): Promise<Setting[]> {
    if (!this.settingsService) {
      throw new Error('Settings service not available');
    }
    return await this.settingsService.getAll();
  }

  // Voice Handlers
  private async handleVoiceStartRecording(): Promise<void> {
    if (!this.voiceService) {
      throw new Error('Voice service not available');
    }
    await this.voiceService.startRecording();
  }

  private async handleVoiceStopRecording(): Promise<string> {
    if (!this.voiceService) {
      throw new Error('Voice service not available');
    }
    return await this.voiceService.stopRecording();
  }

  // AI Handlers
  private async handleAIGenerate(request: any): Promise<string> {
    if (!this.aiService) {
      throw new Error('AI service not available');
    }
    return await this.aiService.generate(request);
  }

  private async handleAIEnhance(text: string): Promise<string> {
    if (!this.aiService) {
      throw new Error('AI service not available');
    }
    return await this.aiService.enhance(text);
  }

  private async handleAIAnalyze(data: any): Promise<any> {
    if (!this.aiService) {
      throw new Error('AI service not available');
    }
    return await this.aiService.analyze(data);
  }

  private async handleAIProvidersList(): Promise<string[]> {
    if (!this.aiService) {
      throw new Error('AI service not available');
    }
    return await this.aiService.getProviders();
  }

  private async handleAIModelsList(provider?: string): Promise<string[]> {
    if (!this.aiService) {
      throw new Error('AI service not available');
    }
    return await this.aiService.getModels(provider);
  }

  /**
   * Broadcast event to all renderer processes
   */
  broadcastEvent(channel: string, data: any): void {
    BrowserWindow.getAllWindows().forEach(window => {
      if (!window.isDestroyed()) {
        window.webContents.send(channel, data);
      }
    });
  }

  /**
   * Get router instance
   */
  getRouter(): IPCRouter {
    return this.router;
  }
}
