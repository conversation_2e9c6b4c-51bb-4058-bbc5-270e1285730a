{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "removeComments": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "jsx": "react-jsx", "baseUrl": "./src", "paths": {"@/*": ["*"], "@/core/*": ["core/*"], "@/services/*": ["services/*"], "@/storage/*": ["storage/*"], "@/types/*": ["types/*"], "@/shared/*": ["shared/*"], "@/main/*": ["main/*"], "@/renderer/*": ["renderer/*"]}}, "include": ["src/**/*", "tests/**/*"], "exclude": ["node_modules", "dist", "release", "coverage"], "ts-node": {"esm": true}}