/**
 * Test file to validate the PromptLibrary service implementation
 */

import {
  createTestPrompt,
  createTestCategory,
  createMockPromptLibrary,
  expectValidPrompt,
  expectValidCategory
} from './setup';

describe('PromptLibrary Service Implementation', () => {
  let mockPromptLibrary: ReturnType<typeof createMockPromptLibrary>;

  beforeEach(() => {
    mockPromptLibrary = createMockPromptLibrary();
  });

  describe('Category Management', () => {
    it('should create category and return Category object', async () => {
      const categoryData = createTestCategory();
      const result = await mockPromptLibrary.createCategory(categoryData);
      
      expectValidCategory(result);
      expect(result.name).toBe('Test Category');
      expect(result.description).toBe('Test category description');
      expect(result.metadata).toBeDefined();
      expect(mockPromptLibrary.createCategory).toHaveBeenCalledWith(categoryData);
    });

    it('should update category and return updated Category object', async () => {
      const categoryId = 'test-category-id';
      const updates = { name: 'Updated Category', description: 'Updated description' };
      
      const result = await mockPromptLibrary.updateCategory(categoryId, updates);
      
      expectValidCategory(result);
      expect(result.id).toBe(categoryId);
      expect(mockPromptLibrary.updateCategory).toHaveBeenCalledWith(categoryId, updates);
    });

    it('should delete category and return boolean', async () => {
      const categoryId = 'test-category-id';
      const movePromptsTo = 'target-category-id';
      
      const result = await mockPromptLibrary.deleteCategory(categoryId, movePromptsTo);
      
      expect(typeof result).toBe('boolean');
      expect(result).toBe(true);
      expect(mockPromptLibrary.deleteCategory).toHaveBeenCalledWith(categoryId, movePromptsTo);
    });

    it('should get all categories', async () => {
      const result = await mockPromptLibrary.getAllCategories();
      
      expect(Array.isArray(result)).toBe(true);
      expect(mockPromptLibrary.getAllCategories).toHaveBeenCalled();
    });
  });

  describe('Prompt Management', () => {
    it('should create prompt and return Prompt object', async () => {
      const promptData = createTestPrompt();
      const result = await mockPromptLibrary.createPrompt(promptData);
      
      expectValidPrompt(result);
      expect(result.title).toBe('Test Prompt');
      expect(result.content).toBe('Test content with {{variable}}');
      expect(result.metadata).toBeDefined();
      expect(result.usage).toBeDefined();
      expect(mockPromptLibrary.createPrompt).toHaveBeenCalledWith(promptData);
    });

    it('should update prompt and return updated Prompt object', async () => {
      const promptId = 'test-prompt-id';
      const updates = { title: 'Updated Prompt', content: 'Updated content' };
      
      const result = await mockPromptLibrary.updatePrompt(promptId, updates);
      
      expectValidPrompt(result);
      expect(result.id).toBe(promptId);
      expect(mockPromptLibrary.updatePrompt).toHaveBeenCalledWith(promptId, updates);
    });

    it('should delete prompt and return boolean', async () => {
      const promptId = 'test-prompt-id';
      
      const result = await mockPromptLibrary.deletePrompt(promptId);
      
      expect(typeof result).toBe('boolean');
      expect(result).toBe(true);
      expect(mockPromptLibrary.deletePrompt).toHaveBeenCalledWith(promptId);
    });

    it('should get all prompts', async () => {
      const result = await mockPromptLibrary.getAllPrompts();
      
      expect(Array.isArray(result)).toBe(true);
      expect(mockPromptLibrary.getAllPrompts).toHaveBeenCalled();
    });

    it('should search prompts', async () => {
      const query = 'test query';
      const result = await mockPromptLibrary.searchPrompts(query);
      
      expect(Array.isArray(result)).toBe(true);
      expect(mockPromptLibrary.searchPrompts).toHaveBeenCalledWith(query);
    });

    it('should get prompts by category', async () => {
      const category = 'test-category';
      const result = await mockPromptLibrary.getPromptsByCategory(category);
      
      expect(Array.isArray(result)).toBe(true);
      expect(mockPromptLibrary.getPromptsByCategory).toHaveBeenCalledWith(category);
    });

    it('should get prompts by tags', async () => {
      const tags = ['tag1', 'tag2'];
      const result = await mockPromptLibrary.getPromptsByTags(tags);
      
      expect(Array.isArray(result)).toBe(true);
      expect(mockPromptLibrary.getPromptsByTags).toHaveBeenCalledWith(tags);
    });
  });

  describe('Import/Export', () => {
    it('should export prompts with options', async () => {
      const options = {
        format: 'json' as const,
        includeMetadata: true,
        includeVersions: false
      };
      
      const result = await mockPromptLibrary.exportPrompts(options);
      
      expect(typeof result).toBe('object');
      expect(result.prompts).toBeDefined();
      expect(result.version).toBeDefined();
      expect(result.exportedAt).toBeDefined();
      expect(mockPromptLibrary.exportPrompts).toHaveBeenCalledWith(options);
    });

    it('should import prompts', async () => {
      const data = { prompts: [], version: '1.0.0' };
      
      const result = await mockPromptLibrary.importPrompts(data);
      
      expect(typeof result).toBe('object');
      expect(result.imported).toBeDefined();
      expect(result.errors).toBeDefined();
      expect(mockPromptLibrary.importPrompts).toHaveBeenCalledWith(data);
    });
  });

  describe('Template Processing', () => {
    it('should process template with variables', async () => {
      const promptId = 'test-prompt-id';
      const variables = { variable: 'test value' };
      
      const result = await mockPromptLibrary.processTemplate(promptId, variables);
      
      expect(typeof result).toBe('string');
      expect(mockPromptLibrary.processTemplate).toHaveBeenCalledWith(promptId, variables);
    });
  });

  describe('Service Lifecycle', () => {
    it('should initialize service', async () => {
      await mockPromptLibrary.initialize();
      expect(mockPromptLibrary.initialize).toHaveBeenCalled();
    });

    it('should cleanup service', async () => {
      await mockPromptLibrary.cleanup();
      expect(mockPromptLibrary.cleanup).toHaveBeenCalled();
    });
  });
});
