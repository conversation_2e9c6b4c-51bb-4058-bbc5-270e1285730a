/**
 * Storage module exports
 * SQLite-based storage system with repositories and migrations
 */

import type { DatabaseConfig } from './types';
import { getStorageManager, initializeStorage } from './StorageManager';
import { MigrationManager } from './MigrationManager';

// Core classes
export { SQLiteStorageManager, getStorageManager, initializeStorage } from './StorageManager';
export { SQLiteDatabaseConnection, createDatabaseConnection } from './DatabaseConnection';
export { MigrationManager } from './MigrationManager';
export { BaseRepository } from './BaseRepository';

// Repository classes
export { PromptRepository } from './repositories/PromptRepository';
export { CategoryRepository } from './repositories/CategoryRepository';
export { SettingsRepository, type Setting, type SettingValue } from './repositories/SettingsRepository';
export { UserRepository } from './repositories/UserRepository';
export { SystemRepository } from './repositories/SystemRepository';
export { SessionRepository } from './repositories/SessionRepository';

// Types and interfaces
export * from './types';

// Convenience functions
export const createStorage = async (config?: Partial<DatabaseConfig>) => {
  return initializeStorage(config);
};

export const getDatabase = () => {
  return getStorageManager().getConnection();
};

export const getPromptRepository = () => {
  return getStorageManager().prompts;
};

export const getCategoryRepository = () => {
  return getStorageManager().categories;
};

export const getSettingsRepository = () => {
  return getStorageManager().settings;
};

// Database utilities
export const backupDatabase = async (destination?: string): Promise<string> => {
  return getStorageManager().backup(destination);
};

export const restoreDatabase = async (source: string): Promise<void> => {
  return getStorageManager().restore(source);
};

export const vacuumDatabase = async (): Promise<void> => {
  return getStorageManager().vacuum();
};

export const getDatabaseStats = async (): Promise<any> => {
  return getStorageManager().getStats();
};

// Transaction helper
export const withTransaction = async <T>(fn: (tx: any) => Promise<T>): Promise<T> => {
  return getStorageManager().transaction(fn);
};

// Settings helpers
export const getSetting = async <T = any>(key: string): Promise<T | null> => {
  return getStorageManager().settings.getValue<T>(key);
};

export const setSetting = async (key: string, value: any, category?: string, description?: string): Promise<void> => {
  return getStorageManager().settings.setValue(key, value, category, description);
};

export const getSettingsByCategory = async (category: string): Promise<any[]> => {
  return getStorageManager().settings.getByCategory(category);
};

export const resetSettings = async (category?: string): Promise<void> => {
  return getStorageManager().settings.resetToDefaults(category);
};

// Prompt helpers
export const savePrompt = async (prompt: any): Promise<string> => {
  return getStorageManager().prompts.save(prompt);
};

export const getPrompt = async (id: string): Promise<any> => {
  return getStorageManager().prompts.findById(id);
};

export const getAllPrompts = async (filter?: any): Promise<any[]> => {
  return getStorageManager().prompts.findAll(filter);
};

export const searchPrompts = async (query: string): Promise<any[]> => {
  return getStorageManager().prompts.searchByText(query);
};

export const getFavoritePrompts = async (): Promise<any[]> => {
  return getStorageManager().prompts.findFavorites();
};

export const getRecentPrompts = async (limit?: number): Promise<any[]> => {
  return getStorageManager().prompts.findRecentlyUsed(limit);
};

export const updatePromptUsage = async (id: string): Promise<void> => {
  return getStorageManager().prompts.updateUsage(id);
};

export const togglePromptFavorite = async (id: string): Promise<boolean> => {
  return getStorageManager().prompts.toggleFavorite(id);
};

export const duplicatePrompt = async (id: string, newTitle?: string): Promise<string> => {
  return getStorageManager().prompts.duplicate(id, newTitle);
};

// Category helpers
export const saveCategory = async (category: any): Promise<string> => {
  return getStorageManager().categories.save(category);
};

export const getCategory = async (id: string): Promise<any> => {
  return getStorageManager().categories.findById(id);
};

export const getAllCategories = async (): Promise<any[]> => {
  return getStorageManager().categories.findAll();
};

export const getRootCategories = async (): Promise<any[]> => {
  return getStorageManager().categories.findRootCategories();
};

export const getCategoryHierarchy = async (): Promise<any[]> => {
  return getStorageManager().categories.findHierarchy();
};

export const getCategoryChildren = async (parentId: string): Promise<any[]> => {
  return getStorageManager().categories.findChildren(parentId);
};

export const updateCategorySortOrder = async (categoryIds: string[]): Promise<void> => {
  return getStorageManager().categories.updateSortOrder(categoryIds);
};

// Default database configuration
export const getDefaultDatabaseConfig = (): any => {
  return {
    encryption: {
      enabled: false
    },
    performance: {
      journalMode: 'WAL',
      synchronous: 'NORMAL',
      cacheSize: 10000,
      mmapSize: 268435456 // 256MB
    },
    backup: {
      enabled: true,
      interval: 60, // 1 hour
      maxBackups: 10
    }
  };
};

// Migration utilities
export const getCurrentSchemaVersion = async (): Promise<number> => {
  const manager = getStorageManager();
  const migrationManager = new MigrationManager(manager.getConnection());
  return migrationManager.getCurrentVersion();
};

export const runMigrations = async (): Promise<void> => {
  const manager = getStorageManager();
  const migrationManager = new MigrationManager(manager.getConnection());
  return migrationManager.migrate();
};

export const validateMigrations = async (): Promise<boolean> => {
  const manager = getStorageManager();
  const migrationManager = new MigrationManager(manager.getConnection());
  return migrationManager.validateMigrations();
};

// Health check utilities
export const checkDatabaseHealth = async (): Promise<{
  connected: boolean;
  schemaVersion: number;
  stats: any;
  errors: string[];
}> => {
  const errors: string[] = [];
  let connected = false;
  let schemaVersion = 0;
  let stats = null;

  try {
    const manager = getStorageManager();
    connected = true;

    schemaVersion = await getCurrentSchemaVersion();
    stats = await getDatabaseStats();

    // Validate migrations
    const migrationsValid = await validateMigrations();
    if (!migrationsValid) {
      errors.push('Migration validation failed');
    }

    // Check if we can perform basic operations
    await manager.settings.getCategories();
    await manager.categories.count();
    await manager.prompts.count();

  } catch (error) {
    errors.push(`Database health check failed: ${error instanceof Error ? error.message : String(error)}`);
  }

  return {
    connected,
    schemaVersion,
    stats,
    errors
  };
};
