/**
 * Main file system operations class
 * Cross-platform file system manager with security, atomic operations, and error handling
 */

import * as fs from 'fs/promises';
import * as fsSync from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import { promisify } from 'util';
import {
  IFileSystemManager,
  FileMetadata,
  FileReadOptions,
  FileWriteOptions,
  DirectoryOptions,
  CopyOptions,
  MoveOptions,
  WatchOptions,
  ListOptions,
  BackupOptions,
  SecurityContext,
  PathValidationResult,
  FileOperationResult,
  FileOperation,
  FileSystemStats,
  AtomicContext,
  FileLock,
  FileType,
  FileWatcher as IFileWatcher
} from './types';
import { PathUtils } from './PathUtils';
import { FileWatcher } from './FileWatcher';
import { LoggerFactory } from '../logger';

const logger = LoggerFactory.getInstance().getLogger('FileSystemManager');

export class FileSystemManager implements IFileSystemManager {
  private locks: Map<string, FileLock> = new Map();
  private defaultSecurityContext?: SecurityContext;

  constructor(securityContext?: SecurityContext) {
    this.defaultSecurityContext = securityContext;
  }

  /**
   * Check if a file or directory exists
   */
  async exists(path: string): Promise<boolean> {
    try {
      const validation = this.validatePath(path);
      if (!validation.valid) {
        throw new Error(`Invalid path: ${validation.errors.join(', ')}`);
      }

      await fs.access(validation.normalized);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Read file contents
   */
  async readFile(path: string, options: FileReadOptions = {}): Promise<string | Buffer> {
    const operation: FileOperation = 'read';
    
    try {
      const validation = this.validatePath(path);
      if (!validation.valid) {
        throw new Error(`Invalid path: ${validation.errors.join(', ')}`);
      }

      const normalizedPath = validation.normalized;
      
      // Check if file exists and is readable
      const stats = await fs.stat(normalizedPath);
      if (!stats.isFile()) {
        throw new Error('Path is not a file');
      }

      // Read file with options
      const encoding = options.encoding || 'utf8';
      const result = await fs.readFile(normalizedPath, { 
        encoding: encoding === 'binary' ? undefined : encoding as BufferEncoding,
        flag: options.flag,
        signal: options.signal
      });

      logger.debug('File read successfully', { path: normalizedPath, size: stats.size });
      return result;
    } catch (error) {
      logger.error('Failed to read file', { path, error });
      throw this.createFileSystemError(error, operation, path);
    }
  }

  /**
   * Write file contents
   */
  async writeFile(path: string, data: string | Buffer, options: FileWriteOptions = {}): Promise<void> {
    const operation: FileOperation = 'write';
    
    try {
      const validation = this.validatePath(path);
      if (!validation.valid) {
        throw new Error(`Invalid path: ${validation.errors.join(', ')}`);
      }

      const normalizedPath = validation.normalized;

      // Create backup if requested
      if (options.backup && await this.exists(normalizedPath)) {
        await this.createBackup(normalizedPath);
      }

      // Atomic write if requested
      if (options.atomic) {
        await this.atomicWrite(normalizedPath, data, options);
      } else {
        await this.directWrite(normalizedPath, data, options);
      }

      logger.debug('File written successfully', { 
        path: normalizedPath, 
        size: Buffer.isBuffer(data) ? data.length : Buffer.byteLength(data),
        atomic: options.atomic 
      });
    } catch (error) {
      logger.error('Failed to write file', { path, error });
      throw this.createFileSystemError(error, operation, path);
    }
  }

  /**
   * Delete a file
   */
  async deleteFile(path: string): Promise<void> {
    const operation: FileOperation = 'delete';
    
    try {
      const validation = this.validatePath(path);
      if (!validation.valid) {
        throw new Error(`Invalid path: ${validation.errors.join(', ')}`);
      }

      const normalizedPath = validation.normalized;
      
      // Check if file exists
      const stats = await fs.stat(normalizedPath);
      if (!stats.isFile()) {
        throw new Error('Path is not a file');
      }

      await fs.unlink(normalizedPath);
      logger.debug('File deleted successfully', { path: normalizedPath });
    } catch (error) {
      logger.error('Failed to delete file', { path, error });
      throw this.createFileSystemError(error, operation, path);
    }
  }

  /**
   * Copy a file
   */
  async copyFile(src: string, dest: string, options: CopyOptions = {}): Promise<void> {
    const operation: FileOperation = 'copy';
    
    try {
      const srcValidation = this.validatePath(src);
      const destValidation = this.validatePath(dest);
      
      if (!srcValidation.valid) {
        throw new Error(`Invalid source path: ${srcValidation.errors.join(', ')}`);
      }
      if (!destValidation.valid) {
        throw new Error(`Invalid destination path: ${destValidation.errors.join(', ')}`);
      }

      const normalizedSrc = srcValidation.normalized;
      const normalizedDest = destValidation.normalized;

      // Check if source exists
      const srcStats = await fs.stat(normalizedSrc);
      if (!srcStats.isFile()) {
        throw new Error('Source is not a file');
      }

      // Check if destination exists and handle overwrite
      if (await this.exists(normalizedDest) && !options.overwrite) {
        throw new Error('Destination file already exists');
      }

      // Apply filter if provided
      if (options.filter && !options.filter(normalizedSrc, normalizedDest)) {
        return;
      }

      // Ensure destination directory exists
      await this.createDirectory(PathUtils.dirname(normalizedDest), { recursive: true });

      // Copy file
      await fs.copyFile(normalizedSrc, normalizedDest);

      // Preserve timestamps if requested
      if (options.preserveTimestamps) {
        await fs.utimes(normalizedDest, srcStats.atime, srcStats.mtime);
      }

      logger.debug('File copied successfully', { src: normalizedSrc, dest: normalizedDest });
    } catch (error) {
      logger.error('Failed to copy file', { src, dest, error });
      throw this.createFileSystemError(error, operation, src);
    }
  }

  /**
   * Move a file
   */
  async moveFile(src: string, dest: string, options: MoveOptions = {}): Promise<void> {
    const operation: FileOperation = 'move';
    
    try {
      const srcValidation = this.validatePath(src);
      const destValidation = this.validatePath(dest);
      
      if (!srcValidation.valid) {
        throw new Error(`Invalid source path: ${srcValidation.errors.join(', ')}`);
      }
      if (!destValidation.valid) {
        throw new Error(`Invalid destination path: ${destValidation.errors.join(', ')}`);
      }

      const normalizedSrc = srcValidation.normalized;
      const normalizedDest = destValidation.normalized;

      // Check if destination exists and handle overwrite
      if (await this.exists(normalizedDest) && !options.overwrite) {
        throw new Error('Destination file already exists');
      }

      // Ensure destination directory exists
      await this.createDirectory(PathUtils.dirname(normalizedDest), { recursive: true });

      // Try rename first (atomic on same filesystem)
      try {
        await fs.rename(normalizedSrc, normalizedDest);
      } catch (error) {
        // If rename fails, fall back to copy + delete
        await this.copyFile(normalizedSrc, normalizedDest, { overwrite: options.overwrite });
        await this.deleteFile(normalizedSrc);
      }

      logger.debug('File moved successfully', { src: normalizedSrc, dest: normalizedDest });
    } catch (error) {
      logger.error('Failed to move file', { src, dest, error });
      throw this.createFileSystemError(error, operation, src);
    }
  }

  /**
   * Create a directory
   */
  async createDirectory(path: string, options: DirectoryOptions = {}): Promise<void> {
    const operation: FileOperation = 'create';
    
    try {
      const validation = this.validatePath(path);
      if (!validation.valid) {
        throw new Error(`Invalid path: ${validation.errors.join(', ')}`);
      }

      const normalizedPath = validation.normalized;
      
      await fs.mkdir(normalizedPath, {
        recursive: options.recursive,
        mode: options.mode
      });

      logger.debug('Directory created successfully', { path: normalizedPath, recursive: options.recursive });
    } catch (error) {
      // Ignore error if directory already exists
      if ((error as any).code === 'EEXIST') {
        return;
      }
      
      logger.error('Failed to create directory', { path, error });
      throw this.createFileSystemError(error, operation, path);
    }
  }

  /**
   * Delete a directory
   */
  async deleteDirectory(path: string, options: { recursive?: boolean } = {}): Promise<void> {
    const operation: FileOperation = 'delete';
    
    try {
      const validation = this.validatePath(path);
      if (!validation.valid) {
        throw new Error(`Invalid path: ${validation.errors.join(', ')}`);
      }

      const normalizedPath = validation.normalized;
      
      await fs.rmdir(normalizedPath, { recursive: options.recursive });
      logger.debug('Directory deleted successfully', { path: normalizedPath, recursive: options.recursive });
    } catch (error) {
      logger.error('Failed to delete directory', { path, error });
      throw this.createFileSystemError(error, operation, path);
    }
  }

  /**
   * List directory contents
   */
  async listDirectory(path: string, options: ListOptions = {}): Promise<FileMetadata[]> {
    try {
      const validation = this.validatePath(path);
      if (!validation.valid) {
        throw new Error(`Invalid path: ${validation.errors.join(', ')}`);
      }

      const normalizedPath = validation.normalized;
      const entries: FileMetadata[] = [];

      await this.listDirectoryRecursive(normalizedPath, entries, options, 0);

      // Apply sorting
      if (options.sort) {
        this.sortFileMetadata(entries, options.sort, options.order || 'asc');
      }

      // Apply limit
      if (options.limit && options.limit > 0) {
        return entries.slice(0, options.limit);
      }

      return entries;
    } catch (error) {
      logger.error('Failed to list directory', { path, error });
      throw this.createFileSystemError(error, 'read', path);
    }
  }

  /**
   * Get file metadata
   */
  async getMetadata(path: string): Promise<FileMetadata> {
    try {
      const validation = this.validatePath(path);
      if (!validation.valid) {
        throw new Error(`Invalid path: ${validation.errors.join(', ')}`);
      }

      const normalizedPath = validation.normalized;
      const stats = await fs.stat(normalizedPath);
      
      return this.createFileMetadata(normalizedPath, stats);
    } catch (error) {
      logger.error('Failed to get file metadata', { path, error });
      throw this.createFileSystemError(error, 'read', path);
    }
  }

  /**
   * Set file permissions
   */
  async setPermissions(path: string, mode: number): Promise<void> {
    try {
      const validation = this.validatePath(path);
      if (!validation.valid) {
        throw new Error(`Invalid path: ${validation.errors.join(', ')}`);
      }

      const normalizedPath = validation.normalized;
      await fs.chmod(normalizedPath, mode);
      
      logger.debug('File permissions set successfully', { path: normalizedPath, mode });
    } catch (error) {
      logger.error('Failed to set file permissions', { path, error });
      throw this.createFileSystemError(error, 'write', path);
    }
  }

  /**
   * Watch a file or directory for changes
   */
  async watch(path: string, options: WatchOptions = {}): Promise<IFileWatcher> {
    const validation = this.validatePath(path);
    if (!validation.valid) {
      throw new Error(`Invalid path: ${validation.errors.join(', ')}`);
    }

    const watcher = new FileWatcher(validation.normalized, options);
    await watcher.start();
    
    return watcher;
  }

  /**
   * Validate a path for security and correctness
   */
  validatePath(path: string, context?: SecurityContext): PathValidationResult {
    const effectiveContext = context || this.defaultSecurityContext;
    return PathUtils.validatePath(path, effectiveContext);
  }

  /**
   * Create a backup of a file
   */
  async createBackup(path: string, options: BackupOptions = {}): Promise<string> {
    try {
      const validation = this.validatePath(path);
      if (!validation.valid) {
        throw new Error(`Invalid path: ${validation.errors.join(', ')}`);
      }

      const normalizedPath = validation.normalized;
      
      if (!await this.exists(normalizedPath)) {
        throw new Error('File does not exist');
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = `${normalizedPath}.backup.${timestamp}`;
      
      await this.copyFile(normalizedPath, backupPath, { preserveTimestamps: true });
      
      // Clean up old backups
      if (options.maxBackups && options.maxBackups > 0) {
        await this.cleanupOldBackups(normalizedPath, options.maxBackups);
      }

      logger.debug('Backup created successfully', { original: normalizedPath, backup: backupPath });
      return backupPath;
    } catch (error) {
      logger.error('Failed to create backup', { path, error });
      throw this.createFileSystemError(error, 'copy', path);
    }
  }

  /**
   * Restore a file from backup
   */
  async restoreBackup(backupPath: string, targetPath: string): Promise<void> {
    try {
      await this.copyFile(backupPath, targetPath, { overwrite: true, preserveTimestamps: true });
      logger.debug('Backup restored successfully', { backup: backupPath, target: targetPath });
    } catch (error) {
      logger.error('Failed to restore backup', { backupPath, targetPath, error });
      throw this.createFileSystemError(error, 'copy', backupPath);
    }
  }

  /**
   * Get file system statistics
   */
  async getStats(path?: string): Promise<FileSystemStats> {
    // This is a simplified implementation
    // In a real implementation, you would use platform-specific APIs
    return {
      totalSpace: 0,
      freeSpace: 0,
      usedSpace: 0,
      availableSpace: 0
    };
  }

  /**
   * Atomic write operation
   */
  private async atomicWrite(targetPath: string, data: string | Buffer, options: FileWriteOptions): Promise<void> {
    const tempPath = PathUtils.getTempPath('atomic_write');

    try {
      // Write to temporary file first
      await this.directWrite(tempPath, data, options);

      // Atomic move to target
      await fs.rename(tempPath, targetPath);
    } catch (error) {
      // Clean up temp file on error
      try {
        await fs.unlink(tempPath);
      } catch {
        // Ignore cleanup errors
      }
      throw error;
    }
  }

  /**
   * Direct write operation
   */
  private async directWrite(filePath: string, data: string | Buffer, options: FileWriteOptions): Promise<void> {
    // Ensure directory exists
    await this.createDirectory(PathUtils.dirname(filePath), { recursive: true });

    const encoding = options.encoding || 'utf8';
    await fs.writeFile(filePath, data, {
      encoding: encoding === 'binary' ? undefined : encoding as BufferEncoding,
      mode: options.mode,
      flag: options.flag,
      signal: options.signal
    });
  }

  /**
   * Recursively list directory contents
   */
  private async listDirectoryRecursive(
    dirPath: string,
    entries: FileMetadata[],
    options: ListOptions,
    currentDepth: number
  ): Promise<void> {
    try {
      const dirEntries = await fs.readdir(dirPath, { withFileTypes: true });

      for (const entry of dirEntries) {
        const fullPath = path.join(dirPath, entry.name);

        // Skip hidden files if not requested
        if (!options.includeHidden && entry.name.startsWith('.')) {
          continue;
        }

        try {
          const stats = await fs.stat(fullPath);
          const metadata = this.createFileMetadata(fullPath, stats);

          // Apply filter
          if (options.filter && !options.filter(metadata)) {
            continue;
          }

          entries.push(metadata);

          // Recurse into subdirectories
          if (options.recursive && entry.isDirectory()) {
            await this.listDirectoryRecursive(fullPath, entries, options, currentDepth + 1);
          }
        } catch (error) {
          logger.warn('Failed to get metadata for entry', { path: fullPath, error });
        }
      }
    } catch (error) {
      logger.error('Failed to read directory', { path: dirPath, error });
    }
  }

  /**
   * Create file metadata from stats
   */
  private createFileMetadata(filePath: string, stats: fsSync.Stats): FileMetadata {
    const parsedPath = path.parse(filePath);

    let fileType: FileType = 'unknown';
    if (stats.isFile()) fileType = 'file';
    else if (stats.isDirectory()) fileType = 'directory';
    else if (stats.isSymbolicLink()) fileType = 'symlink';

    return {
      path: filePath,
      name: parsedPath.name,
      extension: parsedPath.ext,
      size: stats.size,
      type: fileType,
      permissions: {
        readable: true, // Simplified
        writable: true,
        executable: true,
        mode: stats.mode
      },
      createdAt: stats.birthtime,
      modifiedAt: stats.mtime,
      accessedAt: stats.atime,
      isHidden: parsedPath.name.startsWith('.')
    };
  }

  /**
   * Sort file metadata array
   */
  private sortFileMetadata(entries: FileMetadata[], sortBy: string, order: 'asc' | 'desc'): void {
    entries.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'size':
          comparison = a.size - b.size;
          break;
        case 'modified':
          comparison = a.modifiedAt.getTime() - b.modifiedAt.getTime();
          break;
        case 'created':
          comparison = a.createdAt.getTime() - b.createdAt.getTime();
          break;
        default:
          return 0;
      }

      return order === 'desc' ? -comparison : comparison;
    });
  }

  /**
   * Clean up old backup files
   */
  private async cleanupOldBackups(originalPath: string, maxBackups: number): Promise<void> {
    try {
      const dir = PathUtils.dirname(originalPath);
      const basename = PathUtils.basename(originalPath);
      const entries = await fs.readdir(dir);

      const backupFiles = entries
        .filter(file => file.startsWith(`${basename}.backup.`))
        .map(file => path.join(dir, file));

      if (backupFiles.length <= maxBackups) {
        return;
      }

      // Sort by modification time (newest first)
      const backupStats = await Promise.all(
        backupFiles.map(async file => ({
          path: file,
          mtime: (await fs.stat(file)).mtime
        }))
      );

      backupStats.sort((a, b) => b.mtime.getTime() - a.mtime.getTime());

      // Delete excess backups
      const filesToDelete = backupStats.slice(maxBackups);
      await Promise.all(filesToDelete.map(file => fs.unlink(file.path)));

      logger.debug('Old backups cleaned up', {
        deleted: filesToDelete.length,
        remaining: maxBackups
      });
    } catch (error) {
      logger.warn('Failed to cleanup old backups', { path: originalPath, error });
    }
  }

  /**
   * Create a file system error
   */
  private createFileSystemError(error: unknown, operation: FileOperation, path?: string): Error {
    const originalError = error as any;
    const fsError = new Error(originalError.message || 'File system operation failed') as any;

    fsError.code = originalError.code || 'UNKNOWN';
    fsError.path = path;
    fsError.operation = operation;
    fsError.syscall = originalError.syscall;
    fsError.errno = originalError.errno;

    return fsError;
  }
}
