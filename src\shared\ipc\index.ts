/**
 * IPC Module Index
 * Exports all IPC-related components and types
 */

// Core IPC components
export { IPCRouter } from './IPCRouter';
export { IPCHandlers } from './IPCHandlers';
export { IPCValidator } from './IPCValidator';
export { IPCEventEmitter, EventBroadcaster } from './IPCEventEmitter';

// Types and interfaces
export * from './types';

// Re-export commonly used types for convenience
export type {
  IPCMessageType,
  IPCRequest,
  IPCResponse,
  IPCError,
  IPCEvent,
  IPCHandler,
  IPCEventHandler,
  IPCMiddleware,
  MessageValidator,
  SecurityPolicy,
  PerformanceMetrics,
  IPCConfig,
  TypedIPCInvoke,
  TypedIPCListener
} from './types';
