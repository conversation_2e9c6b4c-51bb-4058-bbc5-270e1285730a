/**
 * Jest test setup file
 * This file is run before each test suite
 */

// Mock Electron APIs for testing
const mockElectron = {
  app: {
    getVersion: jest.fn(() => '1.0.0'),
    getPath: jest.fn(() => '/mock/path'),
    whenReady: jest.fn(() => Promise.resolve()),
    on: jest.fn(),
    quit: jest.fn(),
  },
  BrowserWindow: jest.fn(() => ({
    loadFile: jest.fn(),
    loadURL: jest.fn(),
    on: jest.fn(),
    once: jest.fn(),
    show: jest.fn(),
    hide: jest.fn(),
    close: jest.fn(),
    minimize: jest.fn(),
    maximize: jest.fn(),
    isMaximized: jest.fn(() => false),
    unmaximize: jest.fn(),
    webContents: {
      openDevTools: jest.fn(),
      on: jest.fn(),
    },
  })),
  ipcMain: {
    handle: jest.fn(),
    on: jest.fn(),
    removeAllListeners: jest.fn(),
  },
  ipcRenderer: {
    invoke: jest.fn(),
    send: jest.fn(),
    on: jest.fn(),
    off: jest.fn(),
    removeAllListeners: jest.fn(),
  },
  contextBridge: {
    exposeInMainWorld: jest.fn(),
  },
};

// Mock electron module
jest.mock('electron', () => mockElectron);

// Mock better-sqlite3 for database tests
jest.mock('better-sqlite3', () => {
  return jest.fn(() => ({
    prepare: jest.fn(() => ({
      run: jest.fn(),
      get: jest.fn(),
      all: jest.fn(),
    })),
    exec: jest.fn(),
    close: jest.fn(),
    transaction: jest.fn(),
  }));
});

// Global test utilities
(global as any).mockElectron = mockElectron;

// Mock DOM APIs for Node.js environment
global.document = {
  createElement: jest.fn(() => ({
    style: {},
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    appendChild: jest.fn(),
    removeChild: jest.fn(),
    querySelector: jest.fn(),
    querySelectorAll: jest.fn(),
    innerHTML: '',
    focus: jest.fn(),
    blur: jest.fn()
  })),
  body: {
    appendChild: jest.fn(),
    removeChild: jest.fn(),
    style: {}
  },
  documentElement: {
    style: {
      setProperty: jest.fn(),
      getProperty: jest.fn()
    }
  }
} as any;

global.window = {
  matchMedia: jest.fn(() => ({
    matches: false,
    addListener: jest.fn(),
    removeListener: jest.fn()
  }))
} as any;

// Suppress console.log in tests unless explicitly needed
const originalConsoleLog = console.log;
console.log = (...args: any[]) => {
  if (process.env.VERBOSE_TESTS === 'true') {
    originalConsoleLog(...args);
  }
};

// Setup global test timeout
jest.setTimeout(30000);

// Import types for test helpers
import type {
  Prompt,
  Category,
  PromptMetadata,
  PromptUsage,
  PromptVariable
} from '../src/services/prompt-library/types';
import type {
  Prompt as MainPrompt,
  PromptMetadata as MainPromptMetadata,
  PromptUsage as MainPromptUsage
} from '../src/types/models/Prompt';
import type { Category as MainCategory } from '../src/types/models/Category';
import type { User } from '../src/types/models/User';
import type { ApplicationConfig } from '../src/main/Application';

/**
 * Centralized Test Helper Utilities
 * Provides consistent factory functions and mock utilities for all test files
 */

// =============================================================================
// CORE TYPE FACTORY FUNCTIONS
// =============================================================================

/**
 * Creates a complete test PromptMetadata object with all required properties
 */
export const createTestPromptMetadata = (overrides: Partial<PromptMetadata> = {}): PromptMetadata => ({
  author: 'test-user',
  source: 'user' as const,
  language: 'en',
  estimatedTokens: 100,
  complexity: 'simple' as const,
  keywords: ['test', 'example'],
  relatedPrompts: [],
  customFields: {},
  ...overrides
});

/**
 * Creates a complete test PromptUsage object with all required properties
 */
export const createTestPromptUsage = (overrides: Partial<PromptUsage> = {}): PromptUsage => ({
  count: 0,
  lastUsed: new Date(),
  averageRating: 4.5,
  successRate: 0.95,
  totalTokens: 1000,
  totalCost: 0.01,
  averageExecutionTime: 150,
  contexts: [
    {
      type: 'manual' as const,
      timestamp: new Date(),
      metadata: { source: 'test' }
    }
  ],
  ...overrides
});

/**
 * Creates a complete test PromptVariable object with all required properties
 */
export const createTestPromptVariable = (overrides: Partial<PromptVariable> = {}): PromptVariable => ({
  name: 'testVariable',
  type: 'string' as const,
  required: false,
  defaultValue: 'default',
  description: 'Test variable description',
  validation: [],
  ...overrides
});

/**
 * Creates a complete test Prompt object for prompt-library tests
 */
export const createTestPrompt = (overrides: Partial<Prompt> = {}): Prompt => {
  const basePrompt: Prompt = {
    id: 'test-prompt-id',
    title: 'Test Prompt',
    content: 'Test content with {{variable}}',
    description: 'Test prompt description',
    category: 'test-category',
    tags: ['test', 'example'],
    variables: [createTestPromptVariable()],
    metadata: createTestPromptMetadata(),
    version: 1,
    isTemplate: false,
    isFavorite: false,
    isArchived: false,
    usage: createTestPromptUsage(),
    createdAt: new Date(),
    updatedAt: new Date()
  };

  return { ...basePrompt, ...overrides };
};

/**
 * Creates a complete test Category object with all required properties
 */
export const createTestCategory = (overrides: Partial<Category> = {}): Category => {
  const baseCategory: Category = {
    id: 'test-category-id',
    name: 'Test Category',
    description: 'Test category description',
    color: '#FF0000',
    icon: 'folder',
    sortOrder: 0,
    metadata: {
      createdAt: new Date(),
      promptCount: 0
    }
  };

  return { ...baseCategory, ...overrides };
};

// =============================================================================
// MAIN TYPES FACTORY FUNCTIONS (for storage manager tests)
// =============================================================================

/**
 * Creates a complete test PromptMetadata object for main types
 */
export const createTestMainPromptMetadata = (overrides: Partial<MainPromptMetadata> = {}): MainPromptMetadata => ({
  author: 'test-user',
  source: 'user' as const,
  language: 'en',
  aiModel: 'gpt-4',
  estimatedTokens: 100,
  complexity: 'simple' as const,
  keywords: ['test', 'example'],
  relatedPrompts: [],
  customFields: {},
  ...overrides
});

/**
 * Creates a complete test PromptUsage object for main types
 */
export const createTestMainPromptUsage = (overrides: Partial<MainPromptUsage> = {}): MainPromptUsage => ({
  count: 0,
  lastUsed: new Date(),
  averageRating: 4.5,
  successRate: 0.95,
  totalTokens: 1000,
  totalCost: 0.01,
  averageExecutionTime: 150,
  contexts: [
    {
      type: 'manual' as const,
      timestamp: new Date(),
      metadata: { source: 'test' }
    }
  ],
  ...overrides
});

/**
 * Creates a complete test Prompt object for main types (storage manager tests)
 */
export const createTestMainPrompt = (overrides: Partial<MainPrompt> = {}): MainPrompt => {
  const basePrompt: MainPrompt = {
    id: 'test-main-prompt-id',
    title: 'Test Prompt',
    content: 'Test content with {{variable}}',
    description: 'Test prompt description',
    createdAt: new Date(),
    updatedAt: new Date(),
    categoryId: 'test-category-id',
    tags: ['test', 'example'],
    variables: [],
    metadata: createTestMainPromptMetadata(),
    version: 1,
    isTemplate: false,
    isFavorite: false,
    isArchived: false,
    usage: createTestMainPromptUsage(),
    estimatedTokens: 100,
    complexity: 'simple' as const,
    lastUsed: new Date()
  };

  return { ...basePrompt, ...overrides };
};

/**
 * Creates a complete test Category object for main types
 */
export const createTestMainCategory = (overrides: Partial<MainCategory> = {}): MainCategory => {
  const baseCategory: MainCategory = {
    id: 'test-main-category-id',
    name: 'Test Category',
    description: 'Test category description',
    color: '#FF0000',
    icon: 'folder',
    sortOrder: 0,
    promptCount: 0,
    isSystem: false,
    metadata: {
      createdAt: new Date(),
      updatedAt: new Date(),
      lastUsed: new Date(),
      totalUsage: 0,
      averageRating: 4.5,
      customFields: {}
    },
    path: ['Test Category'],
    depth: 0,
    hasChildren: false
  };

  return { ...baseCategory, ...overrides };
};

/**
 * Creates a test User object
 */
export const createTestUser = (overrides: Partial<User> = {}): User => {
  const baseUser: User = {
    id: 'test-user-id',
  username: 'testuser',
  email: '<EMAIL>',
  displayName: 'Test User',
  preferences: {
    appearance: {
      theme: 'light' as const,
      language: 'en',
      fontSize: 'medium' as const,
      colorScheme: 'default',
      compactMode: false,
      animations: true
    },
    behavior: {
      autoSave: true,
      autoBackup: true,
      confirmDeletions: true,
      rememberWindowState: true,
      startMinimized: false,
      checkUpdates: true
    },
    privacy: {
      analytics: false,
      crashReports: true,
      usageStatistics: false,
      dataSharing: false,
      anonymizeData: true,
      retentionPeriod: 30
    },
    ai: {
      defaultProvider: 'openai',
      defaultModel: 'gpt-4',
      temperature: 0.7,
      maxTokens: 2048,
      autoEnhance: false,
      contextInjection: true,
      costWarnings: true,
      customInstructions: 'Be helpful and concise'
    },
    voice: {
      defaultLanguage: 'en-US',
      provider: 'browser',
      sensitivity: 0.5,
      noiseReduction: true,
      autoStop: true,
      maxDuration: 60,
      commands: true
    },
    hotkeys: {
      showFloatingWindow: 'Ctrl+Shift+P',
      captureScreen: 'Ctrl+Shift+O',
      startVoiceInput: 'Ctrl+Shift+V',
      quickSearch: 'Ctrl+Shift+F',
      enhanceClipboard: 'Ctrl+Shift+E',
      customHotkeys: {}
    },
    notifications: {
      enabled: true,
      sound: false,
      desktop: true,
      duration: 5000,
      position: 'top-right' as const,
      types: ['success', 'error', 'warning', 'info']
    }
  },
  metadata: {
    createdAt: new Date(),
    lastLoginAt: new Date(),
    lastActiveAt: new Date(),
    loginCount: 1,
    appVersion: '1.0.0',
    platform: 'test',
    timezone: 'UTC',
    locale: 'en-US'
  },
    isActive: true,
    membershipDuration: 30,
    totalPrompts: 0
  };

  return { ...baseUser, ...overrides };
};

// =============================================================================
// MOCK SERVICE FACTORIES
// =============================================================================

/**
 * Creates a mock PromptLibrary service with proper method signatures
 */
export const createMockPromptLibrary = () => ({
  initialize: jest.fn().mockResolvedValue(undefined),
  cleanup: jest.fn().mockResolvedValue(undefined),
  createPrompt: jest.fn().mockImplementation((data: any) =>
    Promise.resolve(createTestPrompt(data))
  ),
  getPrompt: jest.fn().mockResolvedValue(null),
  getAllPrompts: jest.fn().mockResolvedValue([]),
  updatePrompt: jest.fn().mockImplementation((id: string, data: any) =>
    Promise.resolve({ ...createTestPrompt(data), id })
  ),
  deletePrompt: jest.fn().mockResolvedValue(true),
  searchPrompts: jest.fn().mockResolvedValue([]),
  getPromptsByCategory: jest.fn().mockResolvedValue([]),
  getPromptsByTags: jest.fn().mockResolvedValue([]),
  createCategory: jest.fn().mockImplementation((data: any) =>
    Promise.resolve(createTestCategory(data))
  ),
  getAllCategories: jest.fn().mockResolvedValue([]),
  updateCategory: jest.fn().mockImplementation((id: string, data: any) =>
    Promise.resolve({ ...createTestCategory(data), id })
  ),
  deleteCategory: jest.fn().mockResolvedValue(true),
  processTemplate: jest.fn().mockResolvedValue('processed template'),
  exportPrompts: jest.fn().mockResolvedValue({
    prompts: [],
    version: '1.0.0',
    exportedAt: new Date()
  }),
  importPrompts: jest.fn().mockResolvedValue({
    imported: 1,
    errors: []
  })
});

/**
 * Creates a mock StorageManager service
 */
export const createMockStorageManager = () => ({
  initialize: jest.fn().mockResolvedValue(undefined),
  close: jest.fn().mockResolvedValue(undefined),
  transaction: jest.fn().mockImplementation((callback: any) => callback({})),
  vacuum: jest.fn().mockResolvedValue(undefined),
  backup: jest.fn().mockResolvedValue('backup-path'),
  restore: jest.fn().mockResolvedValue(undefined),
  getStatistics: jest.fn().mockResolvedValue({
    totalSize: 1024,
    tableCount: 5,
    recordCount: 100
  })
});

/**
 * Creates a mock AIService
 */
export const createMockAIService = () => ({
  initialize: jest.fn().mockResolvedValue(undefined),
  complete: jest.fn().mockResolvedValue({
    id: 'mock-response',
    content: 'Mock AI response',
    provider: 'mock',
    model: 'mock-model',
    usage: { totalTokens: 30 }
  }),
  stream: jest.fn().mockImplementation(async function* () {
    yield { delta: { content: 'Mock ' } };
    yield { delta: { content: 'stream ' } };
    yield { delta: { content: 'response' }, finishReason: 'stop' };
  }),
  getAvailableProviders: jest.fn().mockReturnValue(['mock']),
  getAvailableModels: jest.fn().mockResolvedValue([]),
  estimateTokens: jest.fn().mockResolvedValue(10),
  estimateCost: jest.fn().mockResolvedValue(0.001)
});

// =============================================================================
// CONFIGURATION AND UTILITY HELPERS
// =============================================================================

/**
 * Creates a test application configuration
 */
export const createTestConfig = (overrides: Partial<ApplicationConfig> = {}): ApplicationConfig => ({
  database: {
    path: ':memory:',
    enableWAL: false,
    enableForeignKeys: true
  },
  logging: {
    level: 'error' as const,
    enableFileLogging: false,
    logDirectory: './test-logs'
  },
  hotkeys: {
    enableGlobalHotkeys: false,
    defaultHotkeys: {
      'show-window': 'Ctrl+Shift+P',
      'capture-screen': 'Ctrl+Shift+O'
    }
  },
  ui: {
    theme: 'light' as const,
    enableAnimations: true,
    windowOpacity: 1.0
  },
  voice: {
    enableVoiceInput: false,
    defaultLanguage: 'en-US',
    enableContinuousListening: false
  },
  context: {
    enableScreenCapture: false,
    enableClipboardMonitoring: false,
    ocrLanguage: 'eng'
  },
  ai: {
    defaultProvider: 'openai',
    enableStreaming: false,
    maxTokens: 1000
  },
  ...overrides
});

/**
 * Cleanup utility for test data
 */
export const cleanupTestData = async () => {
  // Reset all mocks
  jest.clearAllMocks();

  // Clear any global state if needed
  if ((global as any).testState) {
    (global as any).testState = {};
  }
};

/**
 * Custom assertion helpers
 */
export const expectValidPrompt = (prompt: any) => {
  expect(prompt).toBeDefined();
  expect(prompt.id).toBeDefined();
  expect(prompt.title).toBeDefined();
  expect(prompt.content).toBeDefined();
  expect(prompt.metadata).toBeDefined();
  expect(prompt.usage).toBeDefined();
};

export const expectValidCategory = (category: any) => {
  expect(category).toBeDefined();
  expect(category.id).toBeDefined();
  expect(category.name).toBeDefined();
  expect(category.description).toBeDefined();
  expect(category.metadata).toBeDefined();
};

// Make test helpers available globally
(global as any).testHelpers = {
  createTestPrompt,
  createTestCategory,
  createTestUser,
  createTestPromptMetadata,
  createTestPromptUsage,
  createTestPromptVariable,
  createTestMainPrompt,
  createTestMainCategory,
  createTestMainPromptMetadata,
  createTestMainPromptUsage,
  createMockPromptLibrary,
  createMockStorageManager,
  createMockAIService,
  createTestConfig,
  cleanupTestData,
  expectValidPrompt,
  expectValidCategory
};
