/**
 * Configuration module type definitions
 * Provides centralized settings management with validation and persistence
 */

export interface ConfigurationSchema {
  version: string;
  sections: {
    [sectionName: string]: ConfigSection;
  };
}

export interface ConfigSection {
  title: string;
  description: string;
  settings: {
    [settingKey: string]: ConfigSetting;
  };
}

export interface ConfigSetting {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'enum';
  title: string;
  description: string;
  defaultValue: any;
  required: boolean;
  validation?: ValidationRule[];
  options?: any[]; // For enum types
  sensitive?: boolean; // For passwords, API keys
  restart_required?: boolean;
  category: 'basic' | 'advanced' | 'expert';
}

export interface ValidationRule {
  type: 'min' | 'max' | 'pattern' | 'custom';
  value?: any;
  message: string;
  validator?: (value: any) => boolean;
}

export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

export interface ConfigChangeEvent {
  key: string;
  oldValue: any;
  newValue: any;
  timestamp: Date;
  source: 'user' | 'system' | 'import' | 'migration';
}

export type ConfigChangeListener = (event: ConfigChangeEvent) => void;

export interface ConfigValidator {
  validate(value: any, setting: ConfigSetting): ValidationResult;
}

export interface ConfigurationStore {
  load(): Promise<any>;
  save(data: any): Promise<void>;
  get(key: string): any;
  set(key: string, value: any): Promise<void>;
  delete(key: string): Promise<void>;
  exists(): Promise<boolean>;
  backup(): Promise<string>;
  restore(backupPath: string): Promise<void>;
  watch(callback: (event: any) => void): void;
  unwatch(): void;
}

export interface ExportOptions {
  format: 'json' | 'yaml' | 'env';
  includeDefaults: boolean;
  includeSensitive: boolean;
  sections?: string[];
  minify?: boolean;
}

export interface ImportOptions {
  format: 'json' | 'yaml' | 'env';
  merge: boolean;
  validate: boolean;
  backup: boolean;
  sections?: string[];
}

export interface ConfigurationManagerOptions {
  configPath?: string;
  schema?: ConfigurationSchema;
  autoSave?: boolean;
  watchChanges?: boolean;
  backupCount?: number;
  validateOnSet?: boolean;
}

export interface MigrationRule {
  fromVersion: string;
  toVersion: string;
  migrate: (config: any) => any;
  description: string;
}

export interface ConfigurationError extends Error {
  code: string;
  details?: any;
}

// Default configuration values
export interface AppConfig {
  general: {
    theme: 'light' | 'dark' | 'system';
    language: string;
    startup: {
      autoStart: boolean;
      startMinimized: boolean;
      checkUpdates: boolean;
      restoreSession: boolean;
    };
    notifications: {
      enabled: boolean;
      sound: boolean;
      desktop: boolean;
      duration: number;
    };
  };
  hotkeys: {
    showFloatingWindow: string;
    captureScreen: string;
    startVoiceInput: string;
    quickSearch: string;
  };
  ai: {
    defaultProvider: string;
    openai: {
      apiKey: string;
      model: string;
      temperature: number;
      maxTokens: number;
      timeout: number;
    };
    anthropic: {
      apiKey: string;
      model: string;
      temperature: number;
      maxTokens: number;
      timeout: number;
    };
    enhancement: {
      style: string;
      length: string;
      tone: string;
      autoEnhance: boolean;
      contextInjection: boolean;
    };
  };
  voice: {
    defaultProvider: string;
    language: string;
    recording: {
      maxDuration: number;
      autoStop: boolean;
      noiseReduction: boolean;
      format: string;
      sampleRate: number;
      channels: number;
    };
    commands: {
      enabled: boolean;
      sensitivity: number;
      timeout: number;
      confirmationRequired: boolean;
    };
  };
  storage: {
    localPath: string;
    backup: {
      enabled: boolean;
      interval: number;
      maxBackups: number;
      compress: boolean;
      location: string;
    };
    sync: {
      enabled: boolean;
      provider: string;
      autoSync: boolean;
      conflictResolution: string;
    };
    encryption: {
      enabled: boolean;
      algorithm: string;
      keyDerivation: string;
    };
  };
  privacy: {
    analytics: {
      enabled: boolean;
      anonymous: boolean;
      crashReports: boolean;
      performanceMetrics: boolean;
    };
    dataRetention: {
      prompts: number;
      usage: number;
      logs: number;
      cache: number;
    };
    security: {
      requireAuth: boolean;
      sessionTimeout: number;
      clearClipboard: boolean;
      secureDelete: boolean;
    };
  };
  advanced: {
    performance: {
      maxMemoryUsage: number;
      cacheSize: number;
      workerThreads: number;
      hardwareAcceleration: boolean;
    };
    logging: {
      level: string;
      file: boolean;
      console: boolean;
      maxFileSize: number;
      maxFiles: number;
    };
    experimental: {
      enabled: boolean;
      features: string[];
    };
  };
}
