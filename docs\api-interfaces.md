# API Interfaces Specification

## 🎯 Purpose

This document defines all internal API interfaces, contracts, and communication protocols used within the PromptPilot Desktop application, ensuring type safety and consistent data exchange between modules.

## 📋 Interface Categories

### Core Data Models
- Prompt and template interfaces
- User and session management
- Configuration and settings
- Analytics and metrics

### Service Interfaces
- AI service providers
- Speech-to-text services
- Storage and persistence
- File system operations

### Event and Communication
- IPC message protocols
- Event system interfaces
- Error handling contracts
- Validation schemas

## 🏗️ Core Data Interfaces

### Prompt Interfaces
```typescript
interface Prompt {
  id: string;
  title: string;
  content: string;
  description?: string;
  category: string;
  tags: string[];
  variables?: PromptVariable[];
  metadata: PromptMetadata;
  version: number;
  parentId?: string;
  isTemplate: boolean;
  isFavorite: boolean;
  isArchived: boolean;
  usage: PromptUsage;
}

interface PromptVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'enum';
  required: boolean;
  defaultValue?: any;
  description: string;
  validation?: ValidationRule[];
  options?: any[]; // For enum types
}

interface PromptMetadata {
  author: string;
  createdAt: Date;
  updatedAt: Date;
  lastUsedAt?: Date;
  source: 'user' | 'system' | 'imported' | 'generated';
  language: string;
  estimatedTokens: number;
  complexity?: 'simple' | 'medium' | 'complex';
  aiModel?: string;
}

interface PromptUsage {
  count: number;
  lastUsed: Date;
  averageRating?: number;
  successRate?: number;
  totalTokens?: number;
  totalCost?: number;
}

interface PromptTemplate extends Prompt {
  templateEngine: 'handlebars' | 'mustache' | 'simple';
  requiredVariables: string[];
  optionalVariables: string[];
  examples?: TemplateExample[];
}

interface TemplateExample {
  name: string;
  description: string;
  variables: Record<string, any>;
  expectedOutput: string;
}
```

### Category and Organization
```typescript
interface Category {
  id: string;
  name: string;
  description: string;
  parentId?: string;
  color?: string;
  icon?: string;
  sortOrder: number;
  promptCount: number;
  isSystem: boolean;
  metadata: CategoryMetadata;
}

interface CategoryMetadata {
  createdAt: Date;
  updatedAt: Date;
  lastUsed?: Date;
  totalUsage: number;
}

interface Tag {
  name: string;
  color?: string;
  description?: string;
  usageCount: number;
  createdAt: Date;
  lastUsedAt?: Date;
}

interface CategoryTree {
  category: Category;
  children: CategoryTree[];
  promptCount: number;
  depth: number;
}
```

### User and Session
```typescript
interface User {
  id: string;
  username?: string;
  email?: string;
  preferences: UserPreferences;
  subscription?: Subscription;
  createdAt: Date;
  lastLoginAt?: Date;
}

interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications: NotificationSettings;
  privacy: PrivacySettings;
  ai: AIPreferences;
  voice: VoicePreferences;
}

interface NotificationSettings {
  enabled: boolean;
  sound: boolean;
  desktop: boolean;
  duration: number;
  types: string[];
}

interface PrivacySettings {
  analytics: boolean;
  crashReports: boolean;
  dataSharing: boolean;
  anonymizeData: boolean;
}

interface Session {
  id: string;
  userId?: string;
  startedAt: Date;
  endedAt?: Date;
  duration?: number;
  activities: SessionActivity[];
  metadata: SessionMetadata;
}

interface SessionActivity {
  type: 'prompt_used' | 'voice_command' | 'screen_capture' | 'error';
  timestamp: Date;
  data: any;
}

interface SessionMetadata {
  appVersion: string;
  platform: string;
  promptsUsed: number;
  voiceCommands: number;
  screenCaptures: number;
  errorsEncountered: number;
}
```

## 🤖 AI Service Interfaces

### AI Provider Interface
```typescript
interface AIProvider {
  name: string;
  displayName: string;
  config: AIProviderConfig;
  capabilities: AICapability[];
  
  // Core methods
  complete(request: CompletionRequest): Promise<CompletionResponse>;
  stream(request: CompletionRequest): Promise<AsyncIterable<CompletionChunk>>;
  enhance(text: string, options?: EnhancementOptions): Promise<string>;
  
  // Provider management
  validateConfig(): Promise<ValidationResult>;
  getModels(): Promise<AIModel[]>;
  getUsage(): Promise<UsageInfo>;
  estimateCost(request: CompletionRequest): Promise<CostEstimate>;
}

interface AIProviderConfig {
  apiKey: string;
  baseURL?: string;
  organization?: string;
  defaultModel: string;
  timeout: number;
  retryAttempts: number;
  rateLimits: RateLimits;
  customHeaders?: Record<string, string>;
}

interface AICapability {
  type: 'completion' | 'chat' | 'embedding' | 'image' | 'audio';
  models: string[];
  maxTokens: number;
  supportedLanguages?: string[];
}

interface CompletionRequest {
  model: string;
  messages?: ChatMessage[];
  prompt?: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stop?: string | string[];
  stream?: boolean;
  user?: string;
  metadata?: Record<string, any>;
}

interface CompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: CompletionChoice[];
  usage: TokenUsage;
  metadata?: ResponseMetadata;
}

interface CompletionChoice {
  index: number;
  message?: ChatMessage;
  text?: string;
  finishReason: 'stop' | 'length' | 'content_filter' | 'tool_calls';
  logprobs?: any;
}

interface ChatMessage {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string;
  name?: string;
  toolCalls?: ToolCall[];
  toolCallId?: string;
}

interface TokenUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
}

interface AIModel {
  id: string;
  name: string;
  description: string;
  contextLength: number;
  inputCost: number; // per 1K tokens
  outputCost: number; // per 1K tokens
  capabilities: string[];
  deprecated?: boolean;
}
```

### Enhancement Options
```typescript
interface EnhancementOptions {
  style?: 'professional' | 'casual' | 'technical' | 'creative' | 'academic';
  tone?: 'formal' | 'friendly' | 'assertive' | 'questioning' | 'neutral';
  length?: 'concise' | 'detailed' | 'comprehensive' | 'minimal';
  audience?: 'general' | 'technical' | 'executive' | 'academic';
  purpose?: 'instruction' | 'question' | 'analysis' | 'creative' | 'summary';
  context?: ContextSource[];
  preserveStructure?: boolean;
  addExamples?: boolean;
  includeReasoning?: boolean;
  customInstructions?: string;
}

interface ContextSource {
  type: 'clipboard' | 'screen' | 'file' | 'selection' | 'conversation';
  content: string;
  metadata?: Record<string, any>;
}
```

## 🗣️ Speech-to-Text Interfaces

### STT Provider Interface
```typescript
interface STTProvider {
  name: string;
  displayName: string;
  config: STTProviderConfig;
  
  // Core methods
  transcribe(audio: AudioInput, options?: TranscriptionOptions): Promise<TranscriptionResult>;
  transcribeStream(stream: ReadableStream, options?: TranscriptionOptions): Promise<AsyncIterable<TranscriptionChunk>>;
  
  // Provider management
  validateConfig(): Promise<ValidationResult>;
  getSupportedLanguages(): string[];
  getSupportedFormats(): string[];
  getModels(): Promise<STTModel[]>;
  detectLanguage(audio: AudioInput): Promise<LanguageDetectionResult>;
}

interface STTProviderConfig {
  apiKey?: string;
  baseURL?: string;
  defaultModel?: string;
  timeout: number;
  maxFileSize: number;
  supportedFormats: string[];
  rateLimits: RateLimits;
}

interface AudioInput {
  data: Buffer | Blob | ArrayBuffer;
  format: string;
  sampleRate?: number;
  channels?: number;
  duration?: number;
  metadata?: AudioMetadata;
}

interface AudioMetadata {
  source: 'microphone' | 'file' | 'stream';
  quality: 'low' | 'medium' | 'high';
  noiseLevel?: number;
  language?: string;
}

interface TranscriptionOptions {
  language?: string;
  model?: string;
  prompt?: string;
  temperature?: number;
  responseFormat?: 'json' | 'text' | 'srt' | 'vtt' | 'verbose_json';
  timestampGranularities?: ('word' | 'segment')[];
  wordTimestamps?: boolean;
  speakerLabels?: boolean;
  maxSpeakers?: number;
  punctuation?: boolean;
  profanityFilter?: boolean;
  diarization?: boolean;
}

interface TranscriptionResult {
  text: string;
  language: string;
  confidence: number;
  duration: number;
  segments?: TranscriptionSegment[];
  words?: WordTimestamp[];
  speakers?: SpeakerInfo[];
  metadata: TranscriptionMetadata;
}

interface TranscriptionChunk {
  text: string;
  confidence: number;
  isFinal: boolean;
  startTime: number;
  endTime: number;
  alternatives?: TranscriptionAlternative[];
}

interface TranscriptionSegment {
  id: number;
  start: number;
  end: number;
  text: string;
  confidence: number;
  speaker?: string;
  language?: string;
}

interface WordTimestamp {
  word: string;
  start: number;
  end: number;
  confidence: number;
  speaker?: string;
}

interface SpeakerInfo {
  id: string;
  name?: string;
  segments: number[];
  totalDuration: number;
  confidence: number;
}

interface STTModel {
  id: string;
  name: string;
  description: string;
  languages: string[];
  maxDuration: number;
  accuracy: number;
  speed: number;
  cost: number;
}
```

## 💾 Storage Interfaces

### Storage Provider Interface
```typescript
interface StorageProvider {
  name: string;
  type: 'local' | 'cloud' | 'hybrid';
  
  // CRUD operations
  save<T>(collection: string, data: T): Promise<string>;
  load<T>(collection: string, id: string): Promise<T | null>;
  loadAll<T>(collection: string, filter?: QueryFilter): Promise<T[]>;
  update<T>(collection: string, id: string, updates: Partial<T>): Promise<void>;
  delete(collection: string, id: string): Promise<void>;
  
  // Batch operations
  saveBatch<T>(collection: string, items: T[]): Promise<string[]>;
  deleteBatch(collection: string, ids: string[]): Promise<void>;
  
  // Query operations
  query<T>(collection: string, query: Query): Promise<QueryResult<T>>;
  count(collection: string, filter?: QueryFilter): Promise<number>;
  
  // Transaction support
  transaction<T>(operations: TransactionOperation[]): Promise<T>;
  
  // Backup and sync
  backup(options?: BackupOptions): Promise<BackupResult>;
  restore(backupId: string): Promise<void>;
  sync(options?: SyncOptions): Promise<SyncResult>;
}

interface QueryFilter {
  [key: string]: any;
  $and?: QueryFilter[];
  $or?: QueryFilter[];
  $not?: QueryFilter;
  $in?: any[];
  $nin?: any[];
  $gt?: any;
  $gte?: any;
  $lt?: any;
  $lte?: any;
  $regex?: string;
  $exists?: boolean;
}

interface Query {
  filter?: QueryFilter;
  sort?: SortOptions;
  limit?: number;
  offset?: number;
  projection?: string[];
}

interface SortOptions {
  [field: string]: 'asc' | 'desc' | 1 | -1;
}

interface QueryResult<T> {
  data: T[];
  total: number;
  hasMore: boolean;
  cursor?: string;
}

interface TransactionOperation {
  type: 'save' | 'update' | 'delete';
  collection: string;
  id?: string;
  data?: any;
}
```

## 🔧 Configuration Interfaces

### Configuration Management
```typescript
interface ConfigurationManager {
  // Core operations
  get<T>(key: string): T;
  set<T>(key: string, value: T): Promise<void>;
  has(key: string): boolean;
  delete(key: string): Promise<void>;
  reset(key?: string): Promise<void>;
  
  // Validation and schema
  validate(key: string, value: any): ValidationResult;
  getSchema(key: string): ConfigSchema | null;
  
  // Change management
  onChange(key: string, listener: ConfigChangeListener): () => void;
  onAnyChange(listener: ConfigChangeListener): () => void;
  
  // Import/export
  export(options?: ExportOptions): Promise<string>;
  import(data: string, options?: ImportOptions): Promise<ImportResult>;
  
  // Backup and restore
  backup(): Promise<ConfigBackup>;
  restore(backup: ConfigBackup): Promise<void>;
}

interface ConfigSchema {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'enum';
  title: string;
  description: string;
  defaultValue: any;
  required: boolean;
  validation?: ValidationRule[];
  options?: any[];
  sensitive?: boolean;
  category: string;
  restartRequired?: boolean;
}

interface ValidationRule {
  type: 'min' | 'max' | 'pattern' | 'custom' | 'required' | 'enum';
  value?: any;
  message: string;
  validator?: (value: any) => boolean;
}

interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

interface ConfigChangeEvent {
  key: string;
  oldValue: any;
  newValue: any;
  timestamp: Date;
  source: 'user' | 'system' | 'import' | 'reset';
}

type ConfigChangeListener = (event: ConfigChangeEvent) => void;
```

## 📊 Analytics Interfaces

### Analytics Provider
```typescript
interface AnalyticsProvider {
  name: string;
  enabled: boolean;
  
  // Event tracking
  track(event: AnalyticsEvent): Promise<void>;
  trackBatch(events: AnalyticsEvent[]): Promise<void>;
  
  // User tracking
  identify(userId: string, traits?: UserTraits): Promise<void>;
  
  // Page/screen tracking
  page(name: string, properties?: PageProperties): Promise<void>;
  
  // Custom metrics
  metric(name: string, value: number, tags?: Record<string, string>): Promise<void>;
  
  // Error tracking
  error(error: Error, context?: ErrorContext): Promise<void>;
  
  // Performance tracking
  performance(metric: PerformanceMetric): Promise<void>;
  
  // Configuration
  configure(config: AnalyticsConfig): void;
  flush(): Promise<void>;
}

interface AnalyticsEvent {
  name: string;
  properties?: Record<string, any>;
  timestamp?: Date;
  userId?: string;
  sessionId?: string;
  anonymousId?: string;
  context?: EventContext;
}

interface UserTraits {
  email?: string;
  name?: string;
  plan?: string;
  createdAt?: Date;
  [key: string]: any;
}

interface PageProperties {
  title?: string;
  url?: string;
  path?: string;
  referrer?: string;
  [key: string]: any;
}

interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  sessionId?: string;
  extra?: Record<string, any>;
}

interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  tags?: Record<string, string>;
  timestamp?: Date;
}

interface EventContext {
  app: AppContext;
  device: DeviceContext;
  os: OSContext;
  screen?: ScreenContext;
}

interface AppContext {
  name: string;
  version: string;
  build?: string;
  namespace?: string;
}

interface DeviceContext {
  id?: string;
  manufacturer?: string;
  model?: string;
  type?: string;
}

interface OSContext {
  name: string;
  version: string;
}

interface ScreenContext {
  width: number;
  height: number;
  density?: number;
}
```

## 🧪 Testing Interfaces

### Test Utilities
```typescript
interface TestContext {
  describe: string;
  beforeEach?: () => Promise<void>;
  afterEach?: () => Promise<void>;
  mocks: Map<string, any>;
  fixtures: Map<string, any>;
}

interface MockProvider<T> {
  create(): T;
  reset(): void;
  verify(): boolean;
}

interface TestFixture<T> {
  name: string;
  data: T;
  setup?: () => Promise<void>;
  teardown?: () => Promise<void>;
}

// Common test interfaces for each module
interface PromptTestFixtures {
  simplePrompt: Prompt;
  templatePrompt: PromptTemplate;
  complexPrompt: Prompt;
  categoryWithPrompts: Category;
}

interface AIProviderMock extends AIProvider {
  setResponse(response: CompletionResponse): void;
  setError(error: Error): void;
  getCallHistory(): CompletionRequest[];
}

interface StorageMock extends StorageProvider {
  setData<T>(collection: string, data: Record<string, T>): void;
  getData<T>(collection: string): Record<string, T>;
  clearData(): void;
}
```

This comprehensive API interface specification ensures type safety and consistent communication patterns throughout the PromptPilot Desktop application, facilitating maintainable and scalable development.
