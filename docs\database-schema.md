# Database Schema Specification

## 🎯 Purpose

This document defines the complete database schema for the PromptPilot Desktop application, including table structures, relationships, indexes, and migration strategies for SQLite database implementation.

## 📋 Database Overview

### Database Engine
- **Primary**: SQLite 3.x
- **File Location**: `{AppData}/PromptPilot/data/promptpilot.db`
- **Backup Location**: `{AppData}/PromptPilot/backups/`
- **Encryption**: Optional AES-256 encryption for sensitive data

### Schema Version Management
```sql
CREATE TABLE schema_version (
  version INTEGER PRIMARY KEY,
  description TEXT NOT NULL,
  applied_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  checksum TEXT
);
```

## 📊 Core Tables

### Prompts Table
```sql
CREATE TABLE prompts (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  description TEXT,
  category_id TEXT NOT NULL,
  tags TEXT, -- J<PERSON>N array of strings
  variables TEXT, -- JSON array of variable definitions
  metadata TEXT NOT NULL, -- J<PERSON>N object with additional metadata
  version INTEGER DEFAULT 1,
  parent_id TEXT, -- For versioning/forking
  is_template BOOLEAN DEFAULT FALSE,
  is_favorite BOOLEAN DEFAULT FALSE,
  is_archived BOOLEAN DEFAULT FALSE,
  usage_count INTEGER DEFAULT 0,
  last_used_at DATETIME,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  created_by TEXT DEFAULT 'user',
  
  FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
  FOREIGN KEY (parent_id) REFERENCES prompts(id) ON DELETE SET NULL,
  
  CHECK (length(title) > 0),
  CHECK (length(content) > 0),
  CHECK (version > 0),
  CHECK (usage_count >= 0)
);

-- Indexes for performance
CREATE INDEX idx_prompts_category_id ON prompts(category_id);
CREATE INDEX idx_prompts_created_at ON prompts(created_at DESC);
CREATE INDEX idx_prompts_updated_at ON prompts(updated_at DESC);
CREATE INDEX idx_prompts_last_used_at ON prompts(last_used_at DESC);
CREATE INDEX idx_prompts_usage_count ON prompts(usage_count DESC);
CREATE INDEX idx_prompts_is_favorite ON prompts(is_favorite) WHERE is_favorite = TRUE;
CREATE INDEX idx_prompts_is_template ON prompts(is_template) WHERE is_template = TRUE;
CREATE INDEX idx_prompts_is_archived ON prompts(is_archived);
CREATE INDEX idx_prompts_parent_id ON prompts(parent_id);

-- Full-text search index
CREATE VIRTUAL TABLE prompts_fts USING fts5(
  title, content, description, tags,
  content='prompts',
  content_rowid='rowid'
);

-- Triggers to keep FTS in sync
CREATE TRIGGER prompts_fts_insert AFTER INSERT ON prompts BEGIN
  INSERT INTO prompts_fts(rowid, title, content, description, tags)
  VALUES (new.rowid, new.title, new.content, new.description, new.tags);
END;

CREATE TRIGGER prompts_fts_update AFTER UPDATE ON prompts BEGIN
  UPDATE prompts_fts SET 
    title = new.title,
    content = new.content,
    description = new.description,
    tags = new.tags
  WHERE rowid = new.rowid;
END;

CREATE TRIGGER prompts_fts_delete AFTER DELETE ON prompts BEGIN
  DELETE FROM prompts_fts WHERE rowid = old.rowid;
END;

-- Trigger to update updated_at timestamp
CREATE TRIGGER prompts_updated_at AFTER UPDATE ON prompts BEGIN
  UPDATE prompts SET updated_at = CURRENT_TIMESTAMP WHERE id = new.id;
END;
```

### Categories Table
```sql
CREATE TABLE categories (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  parent_id TEXT,
  color TEXT, -- Hex color code
  icon TEXT, -- Icon identifier
  sort_order INTEGER DEFAULT 0,
  prompt_count INTEGER DEFAULT 0,
  is_system BOOLEAN DEFAULT FALSE, -- System categories cannot be deleted
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE CASCADE,
  
  CHECK (length(name) > 0),
  CHECK (sort_order >= 0),
  CHECK (prompt_count >= 0),
  CHECK (color IS NULL OR length(color) = 7) -- #RRGGBB format
);

CREATE INDEX idx_categories_parent_id ON categories(parent_id);
CREATE INDEX idx_categories_sort_order ON categories(sort_order);
CREATE INDEX idx_categories_name ON categories(name);

-- Trigger to update prompt count
CREATE TRIGGER categories_prompt_count_insert AFTER INSERT ON prompts BEGIN
  UPDATE categories 
  SET prompt_count = prompt_count + 1 
  WHERE id = new.category_id;
END;

CREATE TRIGGER categories_prompt_count_delete AFTER DELETE ON prompts BEGIN
  UPDATE categories 
  SET prompt_count = prompt_count - 1 
  WHERE id = old.category_id AND prompt_count > 0;
END;

CREATE TRIGGER categories_prompt_count_update AFTER UPDATE OF category_id ON prompts BEGIN
  UPDATE categories 
  SET prompt_count = prompt_count - 1 
  WHERE id = old.category_id AND prompt_count > 0;
  
  UPDATE categories 
  SET prompt_count = prompt_count + 1 
  WHERE id = new.category_id;
END;
```

### Tags Table
```sql
CREATE TABLE tags (
  name TEXT PRIMARY KEY,
  color TEXT, -- Hex color code
  description TEXT,
  usage_count INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  last_used_at DATETIME,
  
  CHECK (length(name) > 0),
  CHECK (usage_count >= 0),
  CHECK (color IS NULL OR length(color) = 7)
);

CREATE INDEX idx_tags_usage_count ON tags(usage_count DESC);
CREATE INDEX idx_tags_last_used_at ON tags(last_used_at DESC);
```

### Prompt Versions Table
```sql
CREATE TABLE prompt_versions (
  id TEXT PRIMARY KEY,
  prompt_id TEXT NOT NULL,
  version_number INTEGER NOT NULL,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  description TEXT,
  changes_summary TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  created_by TEXT DEFAULT 'user',
  
  FOREIGN KEY (prompt_id) REFERENCES prompts(id) ON DELETE CASCADE,
  
  UNIQUE(prompt_id, version_number),
  CHECK (version_number > 0)
);

CREATE INDEX idx_prompt_versions_prompt_id ON prompt_versions(prompt_id);
CREATE INDEX idx_prompt_versions_created_at ON prompt_versions(created_at DESC);
```

## 📈 Analytics & Usage Tables

### Usage Analytics Table
```sql
CREATE TABLE usage_analytics (
  id TEXT PRIMARY KEY,
  prompt_id TEXT NOT NULL,
  session_id TEXT,
  used_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  context_type TEXT, -- 'manual', 'voice', 'hotkey', 'api'
  enhancement_used BOOLEAN DEFAULT FALSE,
  rating INTEGER, -- 1-5 scale
  success BOOLEAN,
  execution_time_ms INTEGER,
  tokens_used INTEGER,
  cost REAL,
  metadata TEXT, -- JSON object for additional context
  
  FOREIGN KEY (prompt_id) REFERENCES prompts(id) ON DELETE CASCADE,
  
  CHECK (rating IS NULL OR (rating >= 1 AND rating <= 5)),
  CHECK (execution_time_ms IS NULL OR execution_time_ms >= 0),
  CHECK (tokens_used IS NULL OR tokens_used >= 0),
  CHECK (cost IS NULL OR cost >= 0)
);

CREATE INDEX idx_usage_analytics_prompt_id ON usage_analytics(prompt_id);
CREATE INDEX idx_usage_analytics_used_at ON usage_analytics(used_at DESC);
CREATE INDEX idx_usage_analytics_session_id ON usage_analytics(session_id);
CREATE INDEX idx_usage_analytics_context_type ON usage_analytics(context_type);
CREATE INDEX idx_usage_analytics_rating ON usage_analytics(rating);
```

### Performance Metrics Table
```sql
CREATE TABLE performance_metrics (
  id TEXT PRIMARY KEY,
  metric_name TEXT NOT NULL,
  metric_value REAL NOT NULL,
  metric_unit TEXT, -- 'ms', 'bytes', 'count', etc.
  component TEXT, -- Which component generated the metric
  session_id TEXT,
  recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  metadata TEXT, -- JSON object for additional context
  
  CHECK (length(metric_name) > 0)
);

CREATE INDEX idx_performance_metrics_name ON performance_metrics(metric_name);
CREATE INDEX idx_performance_metrics_recorded_at ON performance_metrics(recorded_at DESC);
CREATE INDEX idx_performance_metrics_component ON performance_metrics(component);
```

## ⚙️ Configuration & Settings Tables

### Settings Table
```sql
CREATE TABLE settings (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL,
  value_type TEXT NOT NULL, -- 'string', 'number', 'boolean', 'json'
  category TEXT DEFAULT 'general',
  description TEXT,
  is_sensitive BOOLEAN DEFAULT FALSE, -- For encryption
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  
  CHECK (length(key) > 0),
  CHECK (value_type IN ('string', 'number', 'boolean', 'json'))
);

CREATE INDEX idx_settings_category ON settings(category);
CREATE INDEX idx_settings_is_sensitive ON settings(is_sensitive);

-- Trigger to update updated_at timestamp
CREATE TRIGGER settings_updated_at AFTER UPDATE ON settings BEGIN
  UPDATE settings SET updated_at = CURRENT_TIMESTAMP WHERE key = new.key;
END;
```

### User Sessions Table
```sql
CREATE TABLE user_sessions (
  id TEXT PRIMARY KEY,
  started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  ended_at DATETIME,
  duration_ms INTEGER,
  prompts_used INTEGER DEFAULT 0,
  voice_commands INTEGER DEFAULT 0,
  screen_captures INTEGER DEFAULT 0,
  errors_encountered INTEGER DEFAULT 0,
  app_version TEXT,
  os_platform TEXT,
  metadata TEXT, -- JSON object for additional session data
  
  CHECK (duration_ms IS NULL OR duration_ms >= 0),
  CHECK (prompts_used >= 0),
  CHECK (voice_commands >= 0),
  CHECK (screen_captures >= 0),
  CHECK (errors_encountered >= 0)
);

CREATE INDEX idx_user_sessions_started_at ON user_sessions(started_at DESC);
CREATE INDEX idx_user_sessions_app_version ON user_sessions(app_version);
```

## 🔐 Security & Audit Tables

### Audit Log Table
```sql
CREATE TABLE audit_log (
  id TEXT PRIMARY KEY,
  action TEXT NOT NULL, -- 'CREATE', 'UPDATE', 'DELETE', 'ACCESS'
  resource_type TEXT NOT NULL, -- 'prompt', 'category', 'setting'
  resource_id TEXT,
  old_values TEXT, -- JSON object of previous values
  new_values TEXT, -- JSON object of new values
  user_id TEXT DEFAULT 'system',
  session_id TEXT,
  ip_address TEXT,
  user_agent TEXT,
  timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
  
  CHECK (length(action) > 0),
  CHECK (length(resource_type) > 0)
);

CREATE INDEX idx_audit_log_timestamp ON audit_log(timestamp DESC);
CREATE INDEX idx_audit_log_action ON audit_log(action);
CREATE INDEX idx_audit_log_resource_type ON audit_log(resource_type);
CREATE INDEX idx_audit_log_resource_id ON audit_log(resource_id);
CREATE INDEX idx_audit_log_user_id ON audit_log(user_id);
```

### Error Log Table
```sql
CREATE TABLE error_log (
  id TEXT PRIMARY KEY,
  error_type TEXT NOT NULL,
  error_message TEXT NOT NULL,
  error_stack TEXT,
  component TEXT,
  severity TEXT DEFAULT 'error', -- 'warning', 'error', 'critical'
  session_id TEXT,
  user_action TEXT, -- What the user was doing when error occurred
  app_version TEXT,
  os_platform TEXT,
  timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
  resolved BOOLEAN DEFAULT FALSE,
  resolution_notes TEXT,
  
  CHECK (severity IN ('warning', 'error', 'critical'))
);

CREATE INDEX idx_error_log_timestamp ON error_log(timestamp DESC);
CREATE INDEX idx_error_log_severity ON error_log(severity);
CREATE INDEX idx_error_log_component ON error_log(component);
CREATE INDEX idx_error_log_resolved ON error_log(resolved);
```

## 🔄 Migration Scripts

### Initial Schema (Version 1)
```sql
-- Migration 001: Initial schema
-- This creates all the base tables and indexes

PRAGMA foreign_keys = ON;

-- Create all tables in dependency order
-- (Schema creation SQL from above sections)

-- Insert default categories
INSERT INTO categories (id, name, description, is_system, sort_order) VALUES
  ('general', 'General', 'General purpose prompts', TRUE, 0),
  ('development', 'Development', 'Programming and development prompts', TRUE, 1),
  ('writing', 'Writing', 'Content creation and writing prompts', TRUE, 2),
  ('analysis', 'Analysis', 'Data analysis and research prompts', TRUE, 3),
  ('templates', 'Templates', 'Reusable prompt templates', TRUE, 4);

-- Insert default settings
INSERT INTO settings (key, value, value_type, category, description) VALUES
  ('app.version', '1.0.0', 'string', 'system', 'Application version'),
  ('app.first_run', 'true', 'boolean', 'system', 'First time running the app'),
  ('ui.theme', 'system', 'string', 'appearance', 'UI theme preference'),
  ('hotkeys.show_floating_window', 'Ctrl+Shift+P', 'string', 'hotkeys', 'Show floating window hotkey'),
  ('ai.default_provider', 'openai', 'string', 'ai', 'Default AI provider'),
  ('voice.default_language', 'en', 'string', 'voice', 'Default voice recognition language');

-- Record schema version
INSERT INTO schema_version (version, description) VALUES (1, 'Initial schema');
```

### Example Migration (Version 2)
```sql
-- Migration 002: Add full-text search
-- Adds FTS5 virtual table for better search performance

-- Create FTS virtual table
CREATE VIRTUAL TABLE prompts_fts USING fts5(
  title, content, description, tags,
  content='prompts',
  content_rowid='rowid'
);

-- Populate FTS table with existing data
INSERT INTO prompts_fts(rowid, title, content, description, tags)
SELECT rowid, title, content, description, tags FROM prompts;

-- Create triggers (as defined above)

-- Record migration
INSERT INTO schema_version (version, description) VALUES (2, 'Add full-text search');
```

## 📊 Views for Common Queries

### Prompt Statistics View
```sql
CREATE VIEW prompt_statistics AS
SELECT 
  p.id,
  p.title,
  p.category_id,
  c.name as category_name,
  p.usage_count,
  p.is_favorite,
  p.is_template,
  COUNT(ua.id) as total_uses,
  AVG(ua.rating) as average_rating,
  MAX(ua.used_at) as last_used,
  SUM(ua.tokens_used) as total_tokens,
  SUM(ua.cost) as total_cost
FROM prompts p
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN usage_analytics ua ON p.id = ua.prompt_id
GROUP BY p.id;
```

### Popular Tags View
```sql
CREATE VIEW popular_tags AS
SELECT 
  t.name,
  t.usage_count,
  t.color,
  COUNT(DISTINCT p.id) as prompt_count,
  MAX(t.last_used_at) as last_used
FROM tags t
LEFT JOIN prompts p ON json_extract(p.tags, '$') LIKE '%"' || t.name || '"%'
GROUP BY t.name
ORDER BY t.usage_count DESC;
```

## 🧪 Database Constraints & Validation

### Data Integrity Rules
1. **Referential Integrity**: All foreign keys must reference valid records
2. **Unique Constraints**: Category names, tag names must be unique
3. **Check Constraints**: Ratings must be 1-5, counts must be non-negative
4. **Not Null Constraints**: Essential fields cannot be null
5. **Length Constraints**: Text fields have minimum length requirements

### Performance Considerations
1. **Indexes**: Strategic indexes on frequently queried columns
2. **FTS**: Full-text search for content searching
3. **Partitioning**: Consider partitioning large tables by date
4. **Archival**: Implement data archival for old records
5. **Vacuum**: Regular VACUUM operations to optimize database file

This schema provides a robust foundation for the PromptPilot Desktop application with proper normalization, indexing, and extensibility for future features.
