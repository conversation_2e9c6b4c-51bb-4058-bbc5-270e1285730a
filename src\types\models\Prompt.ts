/**
 * Prompt and template model definitions
 * Core business models for prompt management and templating
 */

// Variable types
export type VariableType = 
  | 'string' 
  | 'number' 
  | 'boolean' 
  | 'array' 
  | 'object' 
  | 'enum' 
  | 'date' 
  | 'url' 
  | 'email' 
  | 'file';

// Prompt source types
export type PromptSource = 'user' | 'system' | 'imported' | 'generated' | 'shared';

// Prompt complexity levels
export type PromptComplexity = 'simple' | 'medium' | 'complex' | 'expert';

// Usage context types
export type UsageContextType = 'manual' | 'voice' | 'hotkey' | 'api' | 'scheduled';

// Template engines
export type TemplateEngine = 'handlebars' | 'mustache' | 'simple' | 'liquid';

// Validation rule interface (imported from validation module)
// Note: This is defined in ../validation/index.ts to avoid conflicts
import type { ValidationRule } from '../validation';

// Base prompt interface
export interface BasePrompt {
  readonly id: string;
  title: string;
  content: string;
  description?: string;
  readonly createdAt: Date;
  updatedAt: Date;
}

// Prompt variable definition
export interface PromptVariable {
  readonly name: string;
  readonly type: VariableType;
  readonly required: boolean;
  readonly defaultValue?: unknown;
  readonly description: string;
  readonly validation?: readonly ValidationRule[];
  readonly options?: readonly unknown[]; // For enum types
  readonly placeholder?: string;
  readonly helpText?: string;
}

// Usage context tracking
export interface UsageContext {
  readonly type: UsageContextType;
  readonly timestamp: Date;
  readonly metadata?: Readonly<Record<string, unknown>>;
}

// Prompt usage statistics
export interface PromptUsage {
  count: number;
  readonly lastUsed: Date;
  readonly averageRating?: number;
  readonly successRate?: number;
  readonly totalTokens: number;
  readonly totalCost: number;
  readonly averageExecutionTime: number;
  readonly contexts: readonly UsageContext[];
}

// Prompt metadata
export interface PromptMetadata {
  readonly author: string;
  readonly source: PromptSource;
  readonly language: string;
  readonly aiModel?: string;
  readonly estimatedTokens: number;
  readonly complexity: PromptComplexity;
  readonly keywords: readonly string[];
  readonly relatedPrompts: readonly string[];
  readonly customFields: Readonly<Record<string, unknown>>;
}

// Full prompt model
export interface Prompt extends BasePrompt {
  categoryId: string;
  tags: readonly string[];
  variables: readonly PromptVariable[];
  metadata: PromptMetadata;
  version: number;
  parentId?: string;
  isTemplate: boolean;
  isFavorite: boolean;
  isArchived: boolean;
  usage: PromptUsage;
  
  // Computed properties
  readonly estimatedTokens: number;
  readonly complexity: PromptComplexity;
  readonly lastUsed?: Date;
}

// Template example
export interface TemplateExample {
  readonly name: string;
  readonly description: string;
  readonly variables: Readonly<Record<string, unknown>>;
  readonly expectedOutput: string;
  readonly tags: readonly string[];
}

// Template-specific model
export interface PromptTemplate extends Prompt {
  readonly templateEngine: TemplateEngine;
  readonly requiredVariables: readonly string[];
  readonly optionalVariables: readonly string[];
  readonly examples: readonly TemplateExample[];
  readonly documentation?: string;
  readonly category: 'form' | 'workflow' | 'snippet' | 'macro';
}

// Diff representation
export interface DiffChunk {
  readonly type: 'addition' | 'deletion' | 'modification';
  readonly content: string;
  readonly lineNumber: number;
  readonly context?: string;
}

export interface PromptDiff {
  readonly additions: readonly DiffChunk[];
  readonly deletions: readonly DiffChunk[];
  readonly modifications: readonly DiffChunk[];
}

// Prompt version for history tracking
export interface PromptVersion {
  readonly id: string;
  readonly promptId: string;
  readonly version: number;
  readonly title: string;
  readonly content: string;
  readonly description?: string;
  readonly changesSummary: string;
  readonly createdAt: Date;
  readonly createdBy: string;
  readonly diff?: PromptDiff;
}

// Prompt creation/update DTOs
export interface CreatePromptRequest {
  title: string;
  content: string;
  description?: string;
  categoryId: string;
  tags?: string[];
  variables?: PromptVariable[];
  isTemplate?: boolean;
  metadata?: Partial<PromptMetadata>;
}

export interface UpdatePromptRequest {
  title?: string;
  content?: string;
  description?: string;
  categoryId?: string;
  tags?: string[];
  variables?: PromptVariable[];
  isTemplate?: boolean;
  isFavorite?: boolean;
  isArchived?: boolean;
  metadata?: Partial<PromptMetadata>;
}

// Prompt search and filtering
export interface PromptSearchCriteria {
  query?: string;
  categoryId?: string;
  tags?: string[];
  complexity?: PromptComplexity;
  source?: PromptSource;
  isTemplate?: boolean;
  isFavorite?: boolean;
  isArchived?: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
  sortBy?: 'title' | 'createdAt' | 'updatedAt' | 'usage' | 'rating';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export interface PromptSearchResult {
  prompts: readonly Prompt[];
  total: number;
  hasMore: boolean;
  facets?: {
    categories: Record<string, number>;
    tags: Record<string, number>;
    complexity: Record<PromptComplexity, number>;
    source: Record<PromptSource, number>;
  };
}
