/**
 * Main Logger implementation
 * Provides structured logging with multiple transports, filtering, and performance monitoring
 */

import {
  LogLevel,
  LogEntry,
  LogContext,
  LogMetadata,
  LogTransport,
  LogFilter,
  ErrorInfo,
  PerformanceInfo,
  LoggerOptions
} from './types';

export class Logger {
  private transports: LogTransport[] = [];
  private level: LogLevel = LogLevel.INFO;
  private context: LogContext = {};
  private filters: LogFilter[] = [];
  private timers: Map<string, number> = new Map();

  constructor(options: LoggerOptions = {}) {
    if (options.level !== undefined) {
      this.level = options.level;
    }
    if (options.context) {
      this.context = { ...options.context };
    }
    if (options.transports) {
      this.transports = [...options.transports];
    }
    if (options.filters) {
      this.filters = [...options.filters];
    }
  }

  /**
   * Log a trace message
   */
  trace(message: string, meta?: LogMetadata): void {
    this.log(LogLevel.TRACE, message, meta);
  }

  /**
   * Log a debug message
   */
  debug(message: string, meta?: LogMetadata): void {
    this.log(LogLevel.DEBUG, message, meta);
  }

  /**
   * Log an info message
   */
  info(message: string, meta?: LogMetadata): void {
    this.log(LogLevel.INFO, message, meta);
  }

  /**
   * Log a warning message
   */
  warn(message: string, meta?: LogMetadata): void {
    this.log(LogLevel.WARN, message, meta);
  }

  /**
   * Log an error message
   */
  error(message: string | Error, meta?: LogMetadata): void {
    if (message instanceof Error) {
      const errorInfo: ErrorInfo = {
        name: message.name,
        message: message.message,
        stack: message.stack,
        code: (message as any).code
      };
      
      this.log(LogLevel.ERROR, message.message, {
        ...meta,
        error: errorInfo
      });
    } else {
      this.log(LogLevel.ERROR, message, meta);
    }
  }

  /**
   * Log a fatal message
   */
  fatal(message: string | Error, meta?: LogMetadata): void {
    if (message instanceof Error) {
      const errorInfo: ErrorInfo = {
        name: message.name,
        message: message.message,
        stack: message.stack,
        code: (message as any).code
      };
      
      this.log(LogLevel.FATAL, message.message, {
        ...meta,
        error: errorInfo
      });
    } else {
      this.log(LogLevel.FATAL, message, meta);
    }
  }

  /**
   * Start a performance timer
   */
  time(label: string): void {
    this.timers.set(label, performance.now());
  }

  /**
   * End a performance timer and log the result
   */
  timeEnd(label: string): number {
    const startTime = this.timers.get(label);
    if (!startTime) {
      this.warn(`Timer '${label}' does not exist`);
      return 0;
    }

    const endTime = performance.now();
    const duration = endTime - startTime;
    
    this.timers.delete(label);

    const performanceInfo: PerformanceInfo = {
      startTime,
      endTime,
      duration,
      memoryBefore: process.memoryUsage(),
      memoryAfter: process.memoryUsage()
    };

    this.info(`Performance: ${label}`, {
      performance: performanceInfo
    });

    return duration;
  }

  /**
   * Create a child logger with additional context
   */
  child(context: LogContext): Logger {
    return new Logger({
      level: this.level,
      context: { ...this.context, ...context },
      transports: [...this.transports],
      filters: [...this.filters]
    });
  }

  /**
   * Add a transport to the logger
   */
  addTransport(transport: LogTransport): void {
    this.transports.push(transport);
  }

  /**
   * Set the minimum log level
   */
  setLevel(level: LogLevel): void {
    this.level = level;
  }

  /**
   * Set the logger context
   */
  setContext(context: LogContext): void {
    this.context = { ...context };
  }

  /**
   * Add a filter to the logger
   */
  addFilter(filter: LogFilter): void {
    this.filters.push(filter);
  }

  /**
   * Core logging method
   */
  private async log(level: LogLevel, message: string, meta?: LogMetadata): Promise<void> {
    // Check if log level meets minimum threshold
    if (level < this.level) {
      return;
    }

    const entry: LogEntry = {
      timestamp: new Date(),
      level,
      message,
      context: { ...this.context },
      metadata: meta
    };

    // Apply filters
    for (const filter of this.filters) {
      if (!filter.shouldLog(entry)) {
        return;
      }
    }

    // Write to all transports
    const writePromises = this.transports.map(transport => 
      transport.write(entry).catch(error => {
        // Prevent infinite recursion by using console directly
        console.error('Failed to write to transport:', error);
      })
    );

    await Promise.allSettled(writePromises);
  }

  /**
   * Close all transports
   */
  async close(): Promise<void> {
    const closePromises = this.transports
      .filter(transport => transport.close)
      .map(transport => transport.close!());

    await Promise.allSettled(closePromises);
  }
}
