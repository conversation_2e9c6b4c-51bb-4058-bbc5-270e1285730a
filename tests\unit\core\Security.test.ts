/**
 * Security module unit tests
 */

import * as crypto from 'crypto';
import { SecurityManager } from '../../../src/core/security/SecurityManager';
import { AESGCMEncryptionService, EncryptionUtils } from '../../../src/core/security/Encryption';
// import { SecureDataStorage } from '../../../src/core/security/SecureStorage';
// import { DEFAULT_SECURITY_CONFIG } from '../../../src/core/security/types';

// Mock file system operations
jest.mock('fs/promises');
jest.mock('fs');

describe('SecurityManager', () => {
  let securityManager: SecurityManager;

  beforeEach(() => {
    securityManager = new SecurityManager();
    jest.clearAllMocks();
  });

  describe('Initialization', () => {
    test('should initialize with default config', () => {
      expect(securityManager).toBeDefined();
      expect(securityManager.isSecureContext()).toBe(false);
    });

    test('should initialize with master password', async () => {
      // Mock the file operations to avoid actual file system calls
      const mockFs = require('fs/promises');
      mockFs.mkdir = jest.fn().mockResolvedValue(undefined);
      mockFs.readFile = jest.fn().mockRejectedValue({ code: 'ENOENT' });
      mockFs.writeFile = jest.fn().mockResolvedValue(undefined);

      await securityManager.initialize('test-password');
      expect(securityManager.isSecureContext()).toBe(true);
    });
  });

  describe('Token Generation', () => {
    test('should generate secure tokens', () => {
      const token1 = securityManager.generateSecureToken();
      const token2 = securityManager.generateSecureToken();
      
      expect(token1).toBeDefined();
      expect(token2).toBeDefined();
      expect(token1).not.toBe(token2);
      expect(token1.length).toBe(32); // Default length
    });

    test('should generate tokens with custom options', () => {
      const token = securityManager.generateSecureToken({
        length: 16,
        charset: 'hex',
        prefix: 'test_',
        suffix: '_end'
      });
      
      expect(token).toMatch(/^test_[0-9a-f]{16}_end$/);
    });
  });

  describe('Password Hashing', () => {
    test('should hash passwords securely', async () => {
      const password = 'test-password-123';
      const hash = await securityManager.hashPassword(password);
      
      expect(hash).toBeDefined();
      expect(hash).not.toBe(password);
      expect(hash.length).toBeGreaterThan(password.length);
    });

    test('should verify passwords correctly', async () => {
      const password = 'test-password-123';
      const hash = await securityManager.hashPassword(password);
      
      const isValid = await securityManager.verifyPassword(password, hash);
      expect(isValid).toBe(true);
      
      const isInvalid = await securityManager.verifyPassword('wrong-password', hash);
      expect(isInvalid).toBe(false);
    });
  });

  describe('Data Sanitization', () => {
    test('should sanitize sensitive data', async () => {
      const sensitiveData = {
        username: 'john_doe',
        password: 'secret123',
        apiKey: 'sk-1234567890',
        email: '<EMAIL>',
        normalField: 'normal value'
      };

      const sanitized = await securityManager.sanitizeData(sensitiveData);
      
      expect(sanitized.username).toBe('john_doe');
      expect(sanitized.password).toBe('[REDACTED]');
      expect(sanitized.apiKey).toBe('[REDACTED]');
      expect(sanitized.email).toBe('[REDACTED]');
      expect(sanitized.normalField).toBe('normal value');
    });

    test('should handle nested objects', async () => {
      const nestedData = {
        user: {
          name: 'John',
          credentials: {
            password: 'secret',
            token: 'abc123'
          }
        },
        config: {
          apiKey: 'key123',
          setting: 'value'
        }
      };

      const sanitized = await securityManager.sanitizeData(nestedData);
      
      expect(sanitized.user.name).toBe('John');
      expect(sanitized.user.credentials.password).toBe('[REDACTED]');
      expect(sanitized.user.credentials.token).toBe('[REDACTED]');
      expect(sanitized.config.apiKey).toBe('[REDACTED]');
      expect(sanitized.config.setting).toBe('value');
    });
  });

  describe('Security Context', () => {
    test('should check secure context', () => {
      expect(securityManager.isSecureContext()).toBe(false);
    });

    test('should enforce security policies', async () => {
      const context = {
        userId: 'user123',
        sessionId: 'session456',
        operation: 'read_data',
        resource: 'prompts',
        timestamp: new Date()
      };

      const allowed = await securityManager.enforceSecurityPolicy('read_data', context);
      expect(allowed).toBe(true); // Default policy allows all
    });
  });

  describe('Audit Events', () => {
    test('should track audit events', async () => {
      // Mock file operations
      const mockFs = require('fs/promises');
      mockFs.mkdir = jest.fn().mockResolvedValue(undefined);
      mockFs.readFile = jest.fn().mockRejectedValue({ code: 'ENOENT' });
      mockFs.writeFile = jest.fn().mockResolvedValue(undefined);

      await securityManager.initialize('test-password');
      
      const events = securityManager.getAuditEvents();
      expect(events.length).toBeGreaterThan(0);
      
      const initEvent = events.find(e => e.operation === 'security_manager_init');
      expect(initEvent).toBeDefined();
      expect(initEvent?.success).toBe(true);
    });

    test('should clear audit events', async () => {
      securityManager.clearAuditEvents();
      const events = securityManager.getAuditEvents();
      expect(events.length).toBe(0);
    });
  });
});

describe('AESGCMEncryptionService', () => {
  let encryptionService: AESGCMEncryptionService;
  let testKey: Buffer;

  beforeEach(async () => {
    encryptionService = new AESGCMEncryptionService();
    testKey = await encryptionService.generateKey();
  });

  describe('Key Generation', () => {
    test('should generate random keys', async () => {
      const key1 = await encryptionService.generateKey();
      const key2 = await encryptionService.generateKey();
      
      expect(key1).toBeDefined();
      expect(key2).toBeDefined();
      expect(key1.length).toBe(32); // 256 bits
      expect(key2.length).toBe(32);
      expect(key1.equals(key2)).toBe(false);
    });

    test('should generate keys from passwords', async () => {
      const password = 'test-password';
      const salt = crypto.randomBytes(32);
      
      const key1 = await encryptionService.generateKey(password, salt);
      const key2 = await encryptionService.generateKey(password, salt);
      
      expect(key1.equals(key2)).toBe(true); // Same password + salt = same key
    });
  });

  describe('Encryption/Decryption', () => {
    test('should encrypt and decrypt strings', async () => {
      const plaintext = 'Hello, World!';
      
      const encrypted = await encryptionService.encrypt(plaintext, testKey);
      expect(encrypted).toBeDefined();
      expect(encrypted.algorithm).toBe('aes-256-cbc');
      expect(encrypted.data).not.toBe(plaintext);
      
      const decrypted = await encryptionService.decrypt(encrypted, testKey);
      expect(decrypted.toString('utf8')).toBe(plaintext);
    });

    test('should encrypt and decrypt buffers', async () => {
      const plaintext = Buffer.from('Binary data test', 'utf8');
      
      const encrypted = await encryptionService.encrypt(plaintext, testKey);
      const decrypted = await encryptionService.decrypt(encrypted, testKey);
      
      expect(decrypted.equals(plaintext)).toBe(true);
    });

    test('should include metadata in encrypted data', async () => {
      const plaintext = 'Test data';
      const context = {
        purpose: 'storage' as const,
        sensitivity: 'high' as const,
        userId: 'user123'
      };
      
      const encrypted = await encryptionService.encrypt(plaintext, testKey, context);
      
      expect(encrypted.metadata).toBeDefined();
      expect(encrypted.metadata.timestamp).toBeInstanceOf(Date);
      expect(encrypted.metadata.keyId).toBeDefined();
      expect(encrypted.metadata.version).toBe(1);
      expect(encrypted.metadata.context).toEqual(context);
    });

    test('should fail with wrong key', async () => {
      const plaintext = 'Test data';
      const wrongKey = await encryptionService.generateKey();
      
      const encrypted = await encryptionService.encrypt(plaintext, testKey);
      
      await expect(
        encryptionService.decrypt(encrypted, wrongKey)
      ).rejects.toThrow();
    });
  });

  describe('Data Integrity', () => {
    test('should verify encrypted data integrity', async () => {
      const plaintext = 'Test data';
      const encrypted = await encryptionService.encrypt(plaintext, testKey);
      
      const isValid = await encryptionService.verifyIntegrity(encrypted);
      expect(isValid).toBe(true);
    });

    test('should detect corrupted data', async () => {
      const plaintext = 'Test data';
      const encrypted = await encryptionService.encrypt(plaintext, testKey);
      
      // Corrupt the data
      encrypted.data = 'corrupted';
      
      const isValid = await encryptionService.verifyIntegrity(encrypted);
      expect(isValid).toBe(true); // Structure is still valid
      
      // But decryption should fail
      await expect(
        encryptionService.decrypt(encrypted, testKey)
      ).rejects.toThrow();
    });
  });
});

describe('EncryptionUtils', () => {
  test('should generate secure salts', () => {
    const salt1 = EncryptionUtils.generateSalt();
    const salt2 = EncryptionUtils.generateSalt();
    
    expect(salt1.length).toBe(32);
    expect(salt2.length).toBe(32);
    expect(salt1.equals(salt2)).toBe(false);
  });

  test('should generate secure passwords', () => {
    const password1 = EncryptionUtils.generateSecurePassword();
    const password2 = EncryptionUtils.generateSecurePassword();
    
    expect(password1.length).toBe(32);
    expect(password2.length).toBe(32);
    expect(password1).not.toBe(password2);
  });

  test('should hash data', () => {
    const data = 'test data';
    const hash1 = EncryptionUtils.hash(data);
    const hash2 = EncryptionUtils.hash(data);
    
    expect(hash1).toBe(hash2); // Same data = same hash
    expect(hash1.length).toBe(64); // SHA-256 hex = 64 chars
  });

  test('should generate and verify HMAC', () => {
    const data = 'test data';
    const key = crypto.randomBytes(32);
    
    const hmac = EncryptionUtils.generateHMAC(data, key);
    const isValid = EncryptionUtils.verifyHMAC(data, key, hmac);
    
    expect(isValid).toBe(true);
    
    const isInvalid = EncryptionUtils.verifyHMAC('wrong data', key, hmac);
    expect(isInvalid).toBe(false);
  });
});
