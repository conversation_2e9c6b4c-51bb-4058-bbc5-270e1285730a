/**
 * Prompt Library module exports
 * Comprehensive prompt management with organization, search, and versioning
 */

// Core classes
export { PromptLibrary, getPromptLibrary, initializePromptLibrary } from './PromptLibrary';
export { SearchEngine } from './SearchEngine';
export { CategoryManager } from './CategoryManager';
export { TagManager } from './TagManager';
export { VersionManager } from './VersionManager';
export { ExportManager } from './ExportManager';
export { ImportManager } from './ImportManager';
export { AnalyticsManager } from './AnalyticsManager';

// Types and interfaces
export * from './types';

// Import for local use
import { getPromptLibrary, initializePromptLibrary } from './PromptLibrary';

// Re-export commonly used types for convenience
export type {
  IPromptLibrary,
  Prompt,
  PromptMetadata,
  PromptUsage,
  PromptVariable,
  Category,
  CategoryNode,
  SearchQuery,
  SearchResult,
  SearchHighlight,
  Tag,
  PromptVersion,
  ExportOptions,
  ImportOptions,
  ImportResult,
  PromptAnalytics,
  ReportOptions,
  AnalyticsReport,
  PromptLibraryConfig
} from './types';

// Convenience functions
export const createPromptLibrary = async (config?: any): Promise<any> => {
  return initializePromptLibrary(config);
};

export const getDefaultPromptLibrary = (): any => {
  return getPromptLibrary();
};

// Prompt management functions
export const savePrompt = async (prompt: any): Promise<string> => {
  return getPromptLibrary().save(prompt);
};

export const loadPrompt = async (id: string): Promise<any> => {
  return getPromptLibrary().load(id);
};

export const loadAllPrompts = async (filter?: any): Promise<any[]> => {
  return getPromptLibrary().loadAll(filter);
};

export const updatePrompt = async (id: string, updates: any): Promise<void> => {
  return getPromptLibrary().update(id, updates);
};

export const deletePrompt = async (id: string): Promise<void> => {
  return getPromptLibrary().delete(id);
};

export const searchPrompts = async (query: any): Promise<any[]> => {
  return getPromptLibrary().search(query);
};

// Category management functions
export const createCategory = async (category: any): Promise<string> => {
  return getPromptLibrary().createCategory(category);
};

export const updateCategory = async (id: string, updates: any): Promise<void> => {
  return getPromptLibrary().updateCategory(id, updates);
};

export const deleteCategory = async (id: string, movePromptsTo?: string): Promise<void> => {
  return getPromptLibrary().deleteCategory(id, movePromptsTo);
};

export const getCategoryTree = async (): Promise<any[]> => {
  return getPromptLibrary().getCategoryTree();
};

export const getPromptsInCategory = async (categoryId: string, includeSubcategories?: boolean): Promise<any[]> => {
  return getPromptLibrary().getPromptsInCategory(categoryId, includeSubcategories);
};

// Tag management functions
export const createTag = async (name: string, options?: any): Promise<void> => {
  return getPromptLibrary().createTag(name, options);
};

export const deleteTag = async (name: string): Promise<void> => {
  return getPromptLibrary().deleteTag(name);
};

export const renameTag = async (oldName: string, newName: string): Promise<void> => {
  return getPromptLibrary().renameTag(oldName, newName);
};

export const getPopularTags = async (limit?: number): Promise<any[]> => {
  return getPromptLibrary().getPopularTags(limit);
};

export const getTagSuggestions = async (partial: string): Promise<string[]> => {
  return getPromptLibrary().getTagSuggestions(partial);
};

// Version management functions
export const createVersion = async (promptId: string, changes: string): Promise<string> => {
  return getPromptLibrary().createVersion(promptId, changes);
};

export const getVersionHistory = async (promptId: string): Promise<any[]> => {
  return getPromptLibrary().getVersionHistory(promptId);
};

export const restoreVersion = async (promptId: string, versionId: string): Promise<void> => {
  return getPromptLibrary().restoreVersion(promptId, versionId);
};

// Import/Export functions
export const exportPrompts = async (options: any): Promise<string> => {
  return getPromptLibrary().exportPrompts(options);
};

export const exportToFile = async (filePath: string, options: any): Promise<void> => {
  return getPromptLibrary().exportToFile(filePath, options);
};

export const importFromJSON = async (jsonData: string, options: any): Promise<any> => {
  return getPromptLibrary().importFromJSON(jsonData, options);
};

export const importFromFile = async (filePath: string, options: any): Promise<any> => {
  return getPromptLibrary().importFromFile(filePath, options);
};

// Analytics functions
export const recordUsage = async (promptId: string, rating?: number, success?: boolean): Promise<void> => {
  return getPromptLibrary().recordUsage(promptId, rating, success);
};

export const getPromptAnalytics = async (promptId: string): Promise<any> => {
  return getPromptLibrary().getPromptAnalytics(promptId);
};

export const getTopPrompts = async (period: 'week' | 'month' | 'year', limit: number): Promise<any[]> => {
  return getPromptLibrary().getTopPrompts(period, limit);
};

export const generateReport = async (options: any): Promise<any> => {
  return getPromptLibrary().generateReport(options);
};

// Utility functions
export const validatePrompt = (prompt: any): boolean => {
  try {
    if (!prompt.title || prompt.title.trim().length === 0) {
      return false;
    }
    if (!prompt.content || prompt.content.trim().length === 0) {
      return false;
    }
    if (!prompt.category || prompt.category.trim().length === 0) {
      return false;
    }
    return true;
  } catch {
    return false;
  }
};

export const estimateTokens = (text: string): number => {
  // Simple estimation: roughly 4 characters per token
  return Math.ceil(text.length / 4);
};

export const normalizeTagName = (name: string): string => {
  return name
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')
    .replace(/[^a-z0-9_-]/g, '')
    .replace(/^[-_]+|[-_]+$/g, '')
    .replace(/[-_]{2,}/g, '-');
};

export const validateTagName = (name: string): boolean => {
  if (!name || name.length === 0 || name.length > 50) {
    return false;
  }
  const validPattern = /^[a-z0-9][a-z0-9_-]*[a-z0-9]$|^[a-z0-9]$/;
  return validPattern.test(name);
};

// Default categories
export const DEFAULT_CATEGORIES = [
  {
    name: 'General',
    description: 'General purpose prompts',
    color: '#6B7280',
    icon: 'folder',
    sortOrder: 0
  },
  {
    name: 'Development',
    description: 'Programming and development prompts',
    color: '#10B981',
    icon: 'code',
    sortOrder: 1
  },
  {
    name: 'Writing',
    description: 'Content creation and writing prompts',
    color: '#8B5CF6',
    icon: 'pencil',
    sortOrder: 2
  },
  {
    name: 'Analysis',
    description: 'Data analysis and research prompts',
    color: '#F59E0B',
    icon: 'chart',
    sortOrder: 3
  },
  {
    name: 'Templates',
    description: 'Reusable prompt templates',
    color: '#EF4444',
    icon: 'template',
    sortOrder: 4
  }
];

// Configuration helpers
export const getDefaultConfig = () => ({
  storage: {
    provider: 'sqlite' as const,
    encryption: false
  },
  search: {
    indexingEnabled: true,
    maxResults: 100,
    fuzzySearch: true
  },
  versioning: {
    enabled: true,
    maxVersions: 10,
    autoVersion: true
  },
  analytics: {
    enabled: true,
    trackUsage: true,
    trackRatings: true
  }
});

export const createDefaultPrompt = (title: string, content: string, category: string = 'general') => ({
  title,
  content,
  description: '',
  category,
  tags: [],
  variables: [],
  metadata: {
    author: 'user',
    createdAt: new Date(),
    updatedAt: new Date(),
    source: 'user' as const,
    language: 'en',
    estimatedTokens: estimateTokens(content)
  },
  version: 1,
  isTemplate: false,
  isFavorite: false,
  usage: {
    count: 0,
    lastUsed: new Date()
  }
});
