/**
 * Context Extractor module exports
 * Screen capture, OCR, clipboard monitoring, and context gathering capabilities
 */

// Core classes
export { ContextExtractor, getContextExtractor, initializeContextExtractor } from './ContextExtractor';
export { ScreenCaptureService } from './ScreenCaptureService';
export { OCREngineFactory } from './OCREngineFactory';
export { TesseractOCREngine } from './TesseractOCREngine';
export { ClipboardMonitor } from './ClipboardMonitor';
export { WindowManager } from './WindowManager';
export { FileContentReader } from './FileContentReader';
export { WebContentExtractor } from './WebContentExtractor';
export { ContextProcessor } from './ContextProcessor';

// Types and interfaces
export * from './types';

// Import for local use
import { getContextExtractor, initializeContextExtractor } from './ContextExtractor';
import { ContextProcessor } from './ContextProcessor';

// Re-export commonly used types for convenience
export type {
  IContextExtractor,
  IOCREngine,
  IClipboardMonitor,
  IWindowManager,
  IFileReader,
  IWebExtractor,
  ContextExtractorConfig,
  ScreenCaptureOptions,
  ScreenRegion,
  CaptureResult,
  WindowInfo,
  SystemInfo,
  ClipboardEntry,
  OCROptions,
  WebContent
} from './types';

// Convenience functions
export const createContextExtractor = async (config?: any): Promise<any> => {
  return initializeContextExtractor(config);
};

export const getDefaultContextExtractor = (): any => {
  return getContextExtractor();
};

// Screen capture functions
export const captureScreen = async (options?: any): Promise<any> => {
  return getContextExtractor().captureScreen(options);
};

export const captureRegion = async (region: any, options?: any): Promise<any> => {
  return getContextExtractor().captureRegion(region, options);
};

export const captureActiveWindow = async (): Promise<any> => {
  return getContextExtractor().captureActiveWindow();
};

// Text extraction functions
export const captureScreenText = async (options?: any): Promise<string> => {
  const result = await getContextExtractor().captureScreen({ ...options, includeOCR: true });
  return result.extractedText || '';
};

export const getClipboardText = async (): Promise<string> => {
  return getContextExtractor().getClipboardText();
};

export const getActiveWindowText = async (): Promise<string> => {
  return getContextExtractor().getActiveWindowText();
};

export const getActiveWindowInfo = async (): Promise<any> => {
  return getContextExtractor().getActiveWindowInfo();
};

export const getSelectedText = async (): Promise<string> => {
  return getContextExtractor().getSelectedText();
};

export const getSystemInfo = async (): Promise<any> => {
  return getContextExtractor().getSystemInfo();
};

// File and web extraction functions
export const extractFromFile = async (filePath: string): Promise<string> => {
  return getContextExtractor().extractFromFile(filePath);
};

export const extractFromUrl = async (url: string): Promise<string> => {
  return getContextExtractor().extractFromUrl(url);
};

// Configuration functions
export const updateContextExtractorConfig = (config: any): void => {
  return getContextExtractor().updateConfig(config);
};

export const getContextExtractorConfig = (): any => {
  return getContextExtractor().getConfig();
};

export const getContextExtractorMetrics = (): any => {
  return getContextExtractor().getMetrics();
};

// Built-in configurations
export const getDefaultContextExtractorConfig = () => ({
  ocr: {
    engine: 'tesseract' as const,
    defaultLanguage: 'eng',
    confidence: 0.7,
    enableCache: true
  },
  clipboard: {
    monitoringEnabled: false,
    monitoringInterval: 1000,
    historySize: 100,
    enableHistory: true
  },
  screen: {
    defaultFormat: 'png' as const,
    defaultQuality: 90,
    tempDirectory: '',
    autoCleanup: true
  },
  files: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    supportedExtensions: ['.txt', '.md', '.json', '.js', '.ts', '.py', '.html', '.css'],
    encoding: 'utf-8'
  },
  web: {
    timeout: 30000,
    userAgent: 'ContextExtractor/1.0',
    enableJavaScript: false
  }
});

// Screen region helpers
export const createScreenRegion = (x: number, y: number, width: number, height: number): any => ({
  x, y, width, height
});

export const getFullScreenRegion = async (): Promise<any> => {
  // In a real implementation, this would get actual screen dimensions
  return createScreenRegion(0, 0, 1920, 1080);
};

// OCR helpers
export const getSupportedOCRLanguages = (): string[] => {
  return [
    'eng', 'spa', 'fra', 'deu', 'ita', 'por', 'rus', 'jpn', 'kor', 'chi_sim', 'chi_tra',
    'ara', 'hin', 'tur', 'pol', 'nld', 'swe', 'dan', 'nor', 'fin', 'heb', 'tha', 'vie'
  ];
};

export const getSupportedOCREngines = (): string[] => {
  return ['tesseract', 'google', 'azure', 'aws'];
};

// File type helpers
export const getSupportedFileExtensions = (): string[] => {
  return ['.txt', '.md', '.json', '.js', '.ts', '.py', '.html', '.css', '.csv', '.java', '.cpp', '.c'];
};

export const isFileSupported = (filePath: string): boolean => {
  const extension = filePath.toLowerCase().split('.').pop();
  return getSupportedFileExtensions().some(ext => ext === `.${extension}`);
};

// Content type detection
export const detectContentType = (content: string): string => {
  try {
    JSON.parse(content);
    return 'json';
  } catch {}

  if (content.includes('<html') || content.includes('<!DOCTYPE')) {
    return 'html';
  }

  if (content.includes('```') || content.includes('function') || content.includes('class')) {
    return 'code';
  }

  if (content.includes(',') && content.split('\n').length > 1) {
    return 'csv';
  }

  if (content.includes('#') || content.includes('**') || content.includes('*')) {
    return 'markdown';
  }

  return 'text';
};

// Utility functions
export const cleanExtractedText = (text: string): string => {
  const processor = new ContextProcessor();
  return processor.cleanText(text);
};

export const summarizeExtractedText = (text: string, maxLength: number = 500): string => {
  const processor = new ContextProcessor();
  return processor.summarizeText(text, maxLength);
};

export const extractKeywords = (text: string): string[] => {
  const processor = new ContextProcessor();
  return processor.extractKeywords(text);
};

export const detectTextLanguage = (text: string): string => {
  const processor = new ContextProcessor();
  return processor.detectLanguage(text);
};

export const removeSensitiveInfo = (text: string): string => {
  const processor = new ContextProcessor();
  return processor.removeSensitiveInfo(text);
};

// Error handling helpers
export const isContextExtractorError = (error: any): boolean => {
  return error && (
    error.name === 'ContextExtractorError' ||
    error.name === 'ScreenCaptureError' ||
    error.name === 'OCRError' ||
    error.name === 'FileReadError' ||
    error.name === 'WebExtractionError'
  );
};

export const getErrorMessage = (error: any): string => {
  if (isContextExtractorError(error)) {
    return error.message;
  }
  return 'An unknown context extractor error occurred';
};

// Context source constants
export const CONTEXT_SOURCES = {
  CLIPBOARD: 'clipboard',
  SCREEN_CAPTURE: 'screen-capture',
  ACTIVE_WINDOW: 'active-window',
  FILE_CONTENT: 'file-content',
  SELECTED_TEXT: 'selected-text',
  CONVERSATION_HISTORY: 'conversation-history',
  BROWSER_TAB: 'browser-tab',
  SYSTEM_INFO: 'system-info'
} as const;

// Quick extraction functions
export const quickScreenCapture = async (): Promise<string> => {
  try {
    const result = await captureScreen({ includeOCR: true });
    return result.extractedText || '';
  } catch (error) {
    console.error('Quick screen capture failed:', error);
    return '';
  }
};

export const quickClipboardRead = async (): Promise<string> => {
  try {
    return await getClipboardText();
  } catch (error) {
    console.error('Quick clipboard read failed:', error);
    return '';
  }
};

export const quickWindowCapture = async (): Promise<string> => {
  try {
    return await getActiveWindowText();
  } catch (error) {
    console.error('Quick window capture failed:', error);
    return '';
  }
};
