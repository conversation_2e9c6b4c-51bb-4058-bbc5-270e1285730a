/**
 * Test file to validate the Storage Manager implementation with centralized test utilities
 */

import {
  createTestMainPrompt,
  createTestMainCategory,
  createTestMainPromptUsage,
  expectValidPrompt,
  expectValidCategory
} from './setup';

describe('Storage Manager Validation', () => {
  describe('Test Object Validation', () => {
    it('should create valid main prompt objects', () => {
      const prompt = createTestMainPrompt();
      expectValidPrompt(prompt);
      
      // Verify all required properties are present
      expect(prompt.id).toBeDefined();
      expect(prompt.title).toBeDefined();
      expect(prompt.content).toBeDefined();
      expect(prompt.categoryId).toBeDefined();
      expect(prompt.metadata).toBeDefined();
      expect(prompt.usage).toBeDefined();
      expect(prompt.createdAt).toBeDefined();
      expect(prompt.updatedAt).toBeDefined();
      expect(prompt.estimatedTokens).toBeDefined();
      expect(prompt.complexity).toBeDefined();
      
      // Verify no invalid properties
      expect((prompt as any).createdBy).toBeUndefined();
    });

    it('should create valid main category objects', () => {
      const category = createTestMainCategory();
      expectValidCategory(category);
      
      // Verify all required properties are present
      expect(category.id).toBeDefined();
      expect(category.name).toBeDefined();
      expect(category.description).toBeDefined();
      expect(category.metadata).toBeDefined();
      expect(category.promptCount).toBeDefined();
      expect(category.isSystem).toBeDefined();
      expect(category.path).toBeDefined();
      expect(category.depth).toBeDefined();
      expect(category.hasChildren).toBeDefined();
    });

    it('should create valid prompt usage objects', () => {
      const usage = createTestMainPromptUsage();
      
      // Verify all required properties are present
      expect(usage.count).toBeDefined();
      expect(usage.lastUsed).toBeDefined();
      expect(usage.totalTokens).toBeDefined();
      expect(usage.totalCost).toBeDefined();
      expect(usage.averageExecutionTime).toBeDefined();
      expect(usage.contexts).toBeDefined();
      expect(Array.isArray(usage.contexts)).toBe(true);
    });

    it('should allow property overrides', () => {
      const prompt = createTestMainPrompt({
        title: 'Custom Title',
        content: 'Custom content',
        categoryId: 'custom-category',
        isFavorite: true,
        isTemplate: true
      });
      
      expect(prompt.title).toBe('Custom Title');
      expect(prompt.content).toBe('Custom content');
      expect(prompt.categoryId).toBe('custom-category');
      expect(prompt.isFavorite).toBe(true);
      expect(prompt.isTemplate).toBe(true);
    });

    it('should create consistent metadata structures', () => {
      const prompt = createTestMainPrompt();
      
      // Verify metadata structure
      expect(prompt.metadata.author).toBeDefined();
      expect(prompt.metadata.source).toBeDefined();
      expect(prompt.metadata.language).toBeDefined();
      expect(prompt.metadata.aiModel).toBeDefined();
      expect(prompt.metadata.estimatedTokens).toBeDefined();
      expect(prompt.metadata.complexity).toBeDefined();
      expect(prompt.metadata.keywords).toBeDefined();
      expect(prompt.metadata.relatedPrompts).toBeDefined();
      expect(prompt.metadata.customFields).toBeDefined();
      
      // Verify types
      expect(typeof prompt.metadata.author).toBe('string');
      expect(typeof prompt.metadata.source).toBe('string');
      expect(typeof prompt.metadata.language).toBe('string');
      expect(typeof prompt.metadata.estimatedTokens).toBe('number');
      expect(Array.isArray(prompt.metadata.keywords)).toBe(true);
      expect(Array.isArray(prompt.metadata.relatedPrompts)).toBe(true);
      expect(typeof prompt.metadata.customFields).toBe('object');
    });

    it('should create consistent usage structures', () => {
      const usage = createTestMainPromptUsage();
      
      // Verify usage structure
      expect(typeof usage.count).toBe('number');
      expect(usage.lastUsed instanceof Date).toBe(true);
      expect(typeof usage.totalTokens).toBe('number');
      expect(typeof usage.totalCost).toBe('number');
      expect(typeof usage.averageExecutionTime).toBe('number');
      expect(Array.isArray(usage.contexts)).toBe(true);
      
      // Verify optional properties
      if (usage.averageRating !== undefined) {
        expect(typeof usage.averageRating).toBe('number');
      }
      if (usage.successRate !== undefined) {
        expect(typeof usage.successRate).toBe('number');
      }
    });

    it('should create consistent category structures', () => {
      const category = createTestMainCategory();
      
      // Verify category structure
      expect(typeof category.name).toBe('string');
      expect(typeof category.description).toBe('string');
      expect(typeof category.color).toBe('string');
      expect(typeof category.icon).toBe('string');
      expect(typeof category.sortOrder).toBe('number');
      expect(typeof category.promptCount).toBe('number');
      expect(typeof category.isSystem).toBe('boolean');
      expect(typeof category.depth).toBe('number');
      expect(typeof category.hasChildren).toBe('boolean');
      expect(Array.isArray(category.path)).toBe(true);
      
      // Verify metadata structure
      expect(category.metadata.createdAt instanceof Date).toBe(true);
      expect(category.metadata.updatedAt instanceof Date).toBe(true);
      expect(category.metadata.lastUsed instanceof Date).toBe(true);
      expect(typeof category.metadata.totalUsage).toBe('number');
      expect(typeof category.metadata.averageRating).toBe('number');
      expect(typeof category.metadata.customFields).toBe('object');
    });
  });

  describe('Interface Compatibility', () => {
    it('should match Prompt interface requirements', () => {
      const prompt = createTestMainPrompt();
      
      // Test that the object can be used where Prompt interface is expected
      const testFunction = (p: any) => {
        expect(p.id).toBeDefined();
        expect(p.title).toBeDefined();
        expect(p.content).toBeDefined();
        return true;
      };
      
      expect(testFunction(prompt)).toBe(true);
    });

    it('should match Category interface requirements', () => {
      const category = createTestMainCategory();
      
      // Test that the object can be used where Category interface is expected
      const testFunction = (c: any) => {
        expect(c.id).toBeDefined();
        expect(c.name).toBeDefined();
        expect(c.description).toBeDefined();
        return true;
      };
      
      expect(testFunction(category)).toBe(true);
    });

    it('should support partial updates', () => {
      const prompt = createTestMainPrompt();
      
      // Test partial update scenarios
      const partialUpdate = {
        title: 'Updated Title',
        isFavorite: true
      };
      
      const updated = { ...prompt, ...partialUpdate };
      expect(updated.title).toBe('Updated Title');
      expect(updated.isFavorite).toBe(true);
      expect(updated.content).toBe(prompt.content); // Should preserve other properties
    });
  });

  describe('Data Consistency', () => {
    it('should maintain consistent date formats', () => {
      const prompt = createTestMainPrompt();
      
      expect(prompt.createdAt instanceof Date).toBe(true);
      expect(prompt.updatedAt instanceof Date).toBe(true);
      expect(prompt.usage.lastUsed instanceof Date).toBe(true);
    });

    it('should maintain consistent ID formats', () => {
      const prompt = createTestMainPrompt();
      const category = createTestMainCategory();
      
      expect(typeof prompt.id).toBe('string');
      expect(prompt.id.length).toBeGreaterThan(0);
      expect(typeof category.id).toBe('string');
      expect(category.id.length).toBeGreaterThan(0);
    });

    it('should maintain consistent array structures', () => {
      const prompt = createTestMainPrompt();
      
      expect(Array.isArray(prompt.tags)).toBe(true);
      expect(Array.isArray(prompt.variables)).toBe(true);
      expect(Array.isArray(prompt.metadata.keywords)).toBe(true);
      expect(Array.isArray(prompt.metadata.relatedPrompts)).toBe(true);
      expect(Array.isArray(prompt.usage.contexts)).toBe(true);
    });
  });
});
