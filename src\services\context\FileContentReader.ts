/**
 * File Content Reader Service
 * Reads and processes various file types for context extraction
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import { 
  IFileContentReader, 
  FileContent, 
  FileContentOptions,
  ContextExtractionError,
  ContextErrorCode
} from './types';
import { logger } from '../../core/logger';

export class FileContentReader implements IFileContentReader {
  private supportedExtensions = new Set([
    // Text files
    '.txt', '.md', '.markdown', '.rst', '.log',
    // Code files
    '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.c', '.h', '.hpp',
    '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala',
    // Web files
    '.html', '.htm', '.css', '.scss', '.sass', '.less',
    // Data files
    '.json', '.xml', '.yaml', '.yml', '.csv', '.tsv',
    // Config files
    '.ini', '.conf', '.config', '.env', '.properties',
    // Documentation
    '.tex', '.rtf'
  ]);

  private readonly defaultMaxSize = 10 * 1024 * 1024; // 10MB
  private readonly defaultPreviewLines = 50;

  constructor() {
    logger.debug('FileContentReader initialized', { 
      supportedExtensions: this.supportedExtensions.size 
    });
  }

  /**
   * Read a single file
   */
  async readFile(filePath: string, options: FileContentOptions = {}): Promise<FileContent> {
    try {
      // Validate file path
      if (!filePath || typeof filePath !== 'string') {
        throw new Error('Invalid file path provided');
      }

      // Check if file is supported
      if (!this.isFileSupported(filePath)) {
        throw new Error(`Unsupported file type: ${path.extname(filePath)}`);
      }

      // Get file stats
      const stats = await fs.stat(filePath);
      
      if (!stats.isFile()) {
        throw new Error('Path is not a file');
      }

      // Check file size
      const maxSize = options.maxSize || this.defaultMaxSize;
      if (stats.size > maxSize) {
        throw new Error(`File too large: ${stats.size} bytes (max: ${maxSize} bytes)`);
      }

      logger.debug('Reading file', { filePath, size: stats.size, options });

      // Read file content
      const encoding = options.encoding || 'utf-8';
      let content = await fs.readFile(filePath, encoding);

      // Process content based on file type
      const extension = path.extname(filePath).toLowerCase();
      content = this.processFileContent(content, extension, options);

      const fileContent: FileContent = {
        path: filePath,
        content,
        metadata: {
          size: stats.size,
          extension,
          encoding,
          lastModified: stats.mtime,
          mimeType: this.getMimeType(extension)
        }
      };

      logger.debug('File read successfully', { 
        filePath, 
        contentLength: content.length,
        extension 
      });

      return fileContent;
    } catch (error) {
      logger.error('Failed to read file', { 
        filePath, 
        error: error instanceof Error ? error.message : String(error) 
      });
      
      throw new ContextExtractionError(
        `Failed to read file: ${error instanceof Error ? error.message : String(error)}`,
        ContextErrorCode.FILE_READ_ERROR,
        'FileContentReader',
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Read multiple files
   */
  async readMultipleFiles(filePaths: string[], options: FileContentOptions = {}): Promise<FileContent[]> {
    const results: FileContent[] = [];
    const errors: Array<{ path: string; error: string }> = [];

    logger.debug('Reading multiple files', { count: filePaths.length, options });

    // Process files in parallel with concurrency limit
    const concurrency = 5;
    for (let i = 0; i < filePaths.length; i += concurrency) {
      const batch = filePaths.slice(i, i + concurrency);
      
      const batchPromises = batch.map(async (filePath) => {
        try {
          const content = await this.readFile(filePath, options);
          results.push(content);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          errors.push({ path: filePath, error: errorMessage });
          logger.warn('Failed to read file in batch', { filePath, error: errorMessage });
        }
      });

      await Promise.all(batchPromises);
    }

    if (errors.length > 0) {
      logger.warn('Some files failed to read', { 
        successCount: results.length, 
        errorCount: errors.length,
        errors: errors.slice(0, 5) // Log first 5 errors
      });
    }

    logger.info('Multiple files read completed', { 
      totalFiles: filePaths.length,
      successCount: results.length,
      errorCount: errors.length 
    });

    return results;
  }

  /**
   * Get supported file extensions
   */
  getSupportedExtensions(): string[] {
    return Array.from(this.supportedExtensions);
  }

  /**
   * Check if file is supported
   */
  isFileSupported(filePath: string): boolean {
    const extension = path.extname(filePath).toLowerCase();
    return this.supportedExtensions.has(extension);
  }

  /**
   * Process file content based on type
   */
  private processFileContent(content: string, extension: string, options: FileContentOptions): string {
    // Handle preview mode
    if (options.preview) {
      const lines = content.split('\n');
      const previewLines = options.previewLines || this.defaultPreviewLines;
      
      if (lines.length > previewLines) {
        content = lines.slice(0, previewLines).join('\n') + 
                 `\n\n... [File truncated - showing first ${previewLines} lines of ${lines.length} total lines]`;
      }
    }

    // Process based on file type
    switch (extension) {
      case '.json':
        return this.processJSON(content);
      
      case '.csv':
      case '.tsv':
        return this.processCSV(content, extension === '.tsv' ? '\t' : ',');
      
      case '.xml':
        return this.processXML(content);
      
      case '.yaml':
      case '.yml':
        return this.processYAML(content);
      
      case '.md':
      case '.markdown':
        return this.processMarkdown(content);
      
      default:
        return content;
    }
  }

  /**
   * Process JSON content
   */
  private processJSON(content: string): string {
    try {
      const parsed = JSON.parse(content);
      return JSON.stringify(parsed, null, 2);
    } catch (error) {
      logger.debug('Invalid JSON, returning raw content', { error });
      return content;
    }
  }

  /**
   * Process CSV/TSV content
   */
  private processCSV(content: string, delimiter: string): string {
    const lines = content.split('\n').filter(line => line.trim());
    if (lines.length < 2) return content;

    const headers = lines[0].split(delimiter);
    const sampleRows = lines.slice(1, 6); // First 5 data rows

    let formatted = `File Type: ${delimiter === '\t' ? 'TSV' : 'CSV'}\n`;
    formatted += `Total Rows: ${lines.length - 1}\n`;
    formatted += `Columns: ${headers.length}\n\n`;
    formatted += `Headers: ${headers.join(' | ')}\n\n`;
    formatted += `Sample Data (first ${sampleRows.length} rows):\n`;
    
    sampleRows.forEach((row, index) => {
      const cells = row.split(delimiter);
      formatted += `Row ${index + 1}: ${cells.join(' | ')}\n`;
    });

    if (lines.length > 6) {
      formatted += `\n... and ${lines.length - 6} more rows`;
    }

    return formatted;
  }

  /**
   * Process XML content
   */
  private processXML(content: string): string {
    // Basic XML formatting - just add some structure info
    const tagMatches = content.match(/<[^>]+>/g);
    const uniqueTags = new Set(tagMatches?.map(tag => tag.replace(/[<>/]|\/.*$/g, '')) || []);
    
    let formatted = `XML Document\n`;
    formatted += `Unique Tags: ${Array.from(uniqueTags).join(', ')}\n\n`;
    formatted += content;
    
    return formatted;
  }

  /**
   * Process YAML content
   */
  private processYAML(content: string): string {
    // Add YAML indicator
    return `YAML Document\n\n${content}`;
  }

  /**
   * Process Markdown content
   */
  private processMarkdown(content: string): string {
    // Extract headers for quick overview
    const headers = content.match(/^#+\s+.+$/gm) || [];
    
    if (headers.length > 0) {
      let formatted = `Markdown Document\n`;
      formatted += `Headers found:\n${headers.join('\n')}\n\n`;
      formatted += content;
      return formatted;
    }
    
    return content;
  }

  /**
   * Get MIME type for file extension
   */
  private getMimeType(extension: string): string {
    const mimeTypes: Record<string, string> = {
      '.txt': 'text/plain',
      '.md': 'text/markdown',
      '.html': 'text/html',
      '.css': 'text/css',
      '.js': 'application/javascript',
      '.json': 'application/json',
      '.xml': 'application/xml',
      '.csv': 'text/csv',
      '.yaml': 'application/x-yaml',
      '.yml': 'application/x-yaml'
    };

    return mimeTypes[extension] || 'text/plain';
  }

  /**
   * Get file preview (first few lines)
   */
  async getFilePreview(filePath: string, lines: number = 20): Promise<string> {
    try {
      const content = await this.readFile(filePath, {
        preview: true,
        previewLines: lines
      });
      return content.content;
    } catch (error) {
      throw new ContextExtractionError(
        `Failed to get file preview: ${error instanceof Error ? error.message : String(error)}`,
        ContextErrorCode.FILE_READ_ERROR,
        'FileContentReader',
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Check if file exists and is readable
   */
  async isFileReadable(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath, fs.constants.R_OK);
      const stats = await fs.stat(filePath);
      return stats.isFile();
    } catch (error) {
      return false;
    }
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    logger.debug('FileContentReader destroyed');
  }
}
