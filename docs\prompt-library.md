# Prompt Library Module Specification

## 🎯 Purpose

The Prompt Library module provides comprehensive prompt management capabilities, including storage, organization, search, and template management for user-created and system prompts.

## 📋 Responsibilities

### Prompt Management
- Create, read, update, delete (CRUD) operations
- Prompt categorization and tagging
- Version control and history tracking
- Duplicate detection and merging

### Organization & Search
- Hierarchical folder structure
- Advanced search and filtering
- Tag-based organization
- Favorites and recent items

### Import/Export
- JSON format import/export
- Template sharing and distribution
- Backup and restore functionality
- Cross-platform synchronization

## 🏗️ Architecture

### Core Library
```typescript
class PromptLibrary {
  private storage: StorageModule;
  private searchEngine: SearchEngine;
  private templateProcessor: TemplateProcessor;
  private versionManager: VersionManager;
  
  async save(prompt: Prompt): Promise<string>;
  async load(id: string): Promise<Prompt | null>;
  async loadAll(filter?: PromptFilter): Promise<Prompt[]>;
  async update(id: string, updates: Partial<Prompt>): Promise<void>;
  async delete(id: string): Promise<void>;
  async search(query: SearchQuery): Promise<SearchResult[]>;
}
```

### Prompt Data Model
```typescript
interface Prompt {
  id: string;
  title: string;
  content: string;
  description?: string;
  category: string;
  tags: string[];
  variables?: PromptVariable[];
  metadata: PromptMetadata;
  version: number;
  parentId?: string; // For versioning
  isTemplate: boolean;
  isFavorite: boolean;
  usage: PromptUsage;
}

interface PromptMetadata {
  author: string;
  createdAt: Date;
  updatedAt: Date;
  lastUsedAt?: Date;
  source: 'user' | 'system' | 'imported';
  language: string;
  estimatedTokens: number;
}

interface PromptUsage {
  count: number;
  lastUsed: Date;
  averageRating?: number;
  successRate?: number;
}

interface PromptVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  required: boolean;
  defaultValue?: any;
  description: string;
  validation?: ValidationRule[];
}
```

## 🗂️ Category Management

### Category System
```typescript
interface Category {
  id: string;
  name: string;
  description: string;
  parentId?: string;
  color?: string;
  icon?: string;
  sortOrder: number;
  metadata: {
    createdAt: Date;
    promptCount: number;
  };
}

class CategoryManager {
  private categories: Map<string, Category> = new Map();
  
  async createCategory(category: Omit<Category, 'id'>): Promise<string>;
  async updateCategory(id: string, updates: Partial<Category>): Promise<void>;
  async deleteCategory(id: string, movePromptsTo?: string): Promise<void>;
  async getCategoryTree(): Promise<CategoryNode[]>;
  async getPromptsInCategory(categoryId: string, includeSubcategories?: boolean): Promise<Prompt[]>;
}

interface CategoryNode {
  category: Category;
  children: CategoryNode[];
  promptCount: number;
}
```

### Default Categories
```typescript
const DEFAULT_CATEGORIES: Category[] = [
  {
    id: 'general',
    name: 'General',
    description: 'General purpose prompts',
    color: '#6B7280',
    icon: 'folder',
    sortOrder: 0,
    metadata: { createdAt: new Date(), promptCount: 0 }
  },
  {
    id: 'development',
    name: 'Development',
    description: 'Programming and development prompts',
    color: '#10B981',
    icon: 'code',
    sortOrder: 1,
    metadata: { createdAt: new Date(), promptCount: 0 }
  },
  {
    id: 'writing',
    name: 'Writing',
    description: 'Content creation and writing prompts',
    color: '#8B5CF6',
    icon: 'pencil',
    sortOrder: 2,
    metadata: { createdAt: new Date(), promptCount: 0 }
  },
  {
    id: 'analysis',
    name: 'Analysis',
    description: 'Data analysis and research prompts',
    color: '#F59E0B',
    icon: 'chart',
    sortOrder: 3,
    metadata: { createdAt: new Date(), promptCount: 0 }
  },
  {
    id: 'templates',
    name: 'Templates',
    description: 'Reusable prompt templates',
    color: '#EF4444',
    icon: 'template',
    sortOrder: 4,
    metadata: { createdAt: new Date(), promptCount: 0 }
  }
];
```

## 🔍 Search & Filtering

### Search Engine
```typescript
interface SearchQuery {
  text?: string;
  tags?: string[];
  category?: string;
  author?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  isTemplate?: boolean;
  isFavorite?: boolean;
  minRating?: number;
  sortBy?: 'relevance' | 'date' | 'usage' | 'rating' | 'title';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

interface SearchResult {
  prompt: Prompt;
  score: number;
  highlights: SearchHighlight[];
}

interface SearchHighlight {
  field: 'title' | 'content' | 'description' | 'tags';
  text: string;
  startIndex: number;
  endIndex: number;
}

class SearchEngine {
  private index: Map<string, SearchIndex> = new Map();
  
  async indexPrompt(prompt: Prompt): Promise<void>;
  async removeFromIndex(promptId: string): Promise<void>;
  async search(query: SearchQuery): Promise<SearchResult[]>;
  async rebuildIndex(prompts: Prompt[]): Promise<void>;
  
  private tokenize(text: string): string[];
  private calculateRelevanceScore(prompt: Prompt, query: SearchQuery): number;
  private highlightMatches(text: string, searchTerms: string[]): SearchHighlight[];
}
```

### Advanced Filtering
```typescript
class PromptFilter {
  static byCategory(prompts: Prompt[], categoryId: string): Prompt[] {
    return prompts.filter(p => p.category === categoryId);
  }
  
  static byTags(prompts: Prompt[], tags: string[]): Prompt[] {
    return prompts.filter(p => 
      tags.some(tag => p.tags.includes(tag))
    );
  }
  
  static byDateRange(prompts: Prompt[], start: Date, end: Date): Prompt[] {
    return prompts.filter(p => 
      p.metadata.createdAt >= start && p.metadata.createdAt <= end
    );
  }
  
  static byUsage(prompts: Prompt[], minUsage: number): Prompt[] {
    return prompts.filter(p => p.usage.count >= minUsage);
  }
  
  static favorites(prompts: Prompt[]): Prompt[] {
    return prompts.filter(p => p.isFavorite);
  }
  
  static templates(prompts: Prompt[]): Prompt[] {
    return prompts.filter(p => p.isTemplate);
  }
  
  static recentlyUsed(prompts: Prompt[], days: number = 7): Prompt[] {
    const cutoff = new Date();
    cutoff.setDate(cutoff.getDate() - days);
    
    return prompts
      .filter(p => p.usage.lastUsed && p.usage.lastUsed >= cutoff)
      .sort((a, b) => b.usage.lastUsed.getTime() - a.usage.lastUsed.getTime());
  }
}
```

## 📊 Version Management

### Version Control
```typescript
interface PromptVersion {
  id: string;
  promptId: string;
  version: number;
  content: string;
  title: string;
  changes: string;
  createdAt: Date;
  createdBy: string;
}

class VersionManager {
  async createVersion(promptId: string, changes: string): Promise<string>;
  async getVersionHistory(promptId: string): Promise<PromptVersion[]>;
  async restoreVersion(promptId: string, versionId: string): Promise<void>;
  async compareVersions(versionId1: string, versionId2: string): Promise<VersionDiff>;
  async deleteVersion(versionId: string): Promise<void>;
}

interface VersionDiff {
  additions: DiffChunk[];
  deletions: DiffChunk[];
  modifications: DiffChunk[];
}

interface DiffChunk {
  type: 'addition' | 'deletion' | 'modification';
  content: string;
  lineNumber: number;
}
```

## 📤 Import/Export

### Export Functionality
```typescript
interface ExportOptions {
  format: 'json' | 'csv' | 'markdown';
  includeMetadata: boolean;
  includeVersions: boolean;
  categories?: string[];
  tags?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
}

class ExportManager {
  async exportPrompts(options: ExportOptions): Promise<string>;
  async exportToFile(filePath: string, options: ExportOptions): Promise<void>;
  async exportCategory(categoryId: string, format: string): Promise<string>;
  async exportFavorites(format: string): Promise<string>;
  
  private formatAsJSON(prompts: Prompt[], options: ExportOptions): string;
  private formatAsCSV(prompts: Prompt[], options: ExportOptions): string;
  private formatAsMarkdown(prompts: Prompt[], options: ExportOptions): string;
}
```

### Import Functionality
```typescript
interface ImportOptions {
  mergeStrategy: 'skip' | 'overwrite' | 'create_new';
  defaultCategory?: string;
  preserveIds: boolean;
  validateContent: boolean;
}

interface ImportResult {
  imported: number;
  skipped: number;
  errors: ImportError[];
  warnings: string[];
}

interface ImportError {
  line?: number;
  prompt?: Partial<Prompt>;
  error: string;
}

class ImportManager {
  async importFromJSON(jsonData: string, options: ImportOptions): Promise<ImportResult>;
  async importFromFile(filePath: string, options: ImportOptions): Promise<ImportResult>;
  async importFromURL(url: string, options: ImportOptions): Promise<ImportResult>;
  
  private validatePromptData(data: any): ValidationResult;
  private resolveConflicts(existing: Prompt, imported: Prompt, strategy: string): Prompt;
}
```

## 🏷️ Tag Management

### Tag System
```typescript
interface Tag {
  name: string;
  color?: string;
  description?: string;
  usage: number;
  createdAt: Date;
}

class TagManager {
  private tags: Map<string, Tag> = new Map();
  
  async createTag(name: string, options?: Partial<Tag>): Promise<void>;
  async deleteTag(name: string): Promise<void>;
  async renameTag(oldName: string, newName: string): Promise<void>;
  async getPopularTags(limit: number = 20): Promise<Tag[]>;
  async getTagSuggestions(partial: string): Promise<string[]>;
  async updateTagUsage(): Promise<void>;
  
  validateTagName(name: string): boolean;
  normalizeTagName(name: string): string;
}
```

## 📈 Analytics & Usage Tracking

### Usage Analytics
```typescript
interface PromptAnalytics {
  promptId: string;
  totalUsage: number;
  recentUsage: number; // Last 30 days
  averageRating: number;
  successRate: number;
  usageHistory: UsageHistoryEntry[];
  popularVariables: VariableUsage[];
}

interface UsageHistoryEntry {
  date: Date;
  count: number;
  rating?: number;
  success?: boolean;
}

class AnalyticsManager {
  async recordUsage(promptId: string, rating?: number, success?: boolean): Promise<void>;
  async getPromptAnalytics(promptId: string): Promise<PromptAnalytics>;
  async getTopPrompts(period: 'week' | 'month' | 'year', limit: number): Promise<Prompt[]>;
  async getUsageTrends(): Promise<UsageTrend[]>;
  async generateReport(options: ReportOptions): Promise<AnalyticsReport>;
}
```

## 🧪 Testing Strategy

### Unit Tests
```typescript
describe('PromptLibrary', () => {
  let library: PromptLibrary;
  let mockStorage: jest.Mocked<StorageModule>;
  
  beforeEach(() => {
    mockStorage = {
      save: jest.fn(),
      load: jest.fn(),
      loadAll: jest.fn(),
      update: jest.fn(),
      delete: jest.fn()
    };
    
    library = new PromptLibrary(mockStorage);
  });
  
  test('should save prompt with generated ID', async () => {
    const prompt: Omit<Prompt, 'id'> = {
      title: 'Test Prompt',
      content: 'Test content',
      category: 'general',
      tags: ['test'],
      metadata: {
        author: 'user',
        createdAt: new Date(),
        updatedAt: new Date(),
        source: 'user',
        language: 'en',
        estimatedTokens: 10
      },
      version: 1,
      isTemplate: false,
      isFavorite: false,
      usage: {
        count: 0,
        lastUsed: new Date()
      }
    };
    
    const id = await library.save(prompt as Prompt);
    
    expect(typeof id).toBe('string');
    expect(mockStorage.save).toHaveBeenCalledWith(
      expect.objectContaining({ ...prompt, id })
    );
  });
  
  test('should search prompts by text', async () => {
    const prompts = [
      { id: '1', title: 'JavaScript Function', content: 'Write a function' },
      { id: '2', title: 'Python Script', content: 'Create a script' }
    ];
    
    mockStorage.loadAll.mockResolvedValue(prompts as Prompt[]);
    
    const results = await library.search({ text: 'function' });
    
    expect(results).toHaveLength(1);
    expect(results[0].prompt.title).toBe('JavaScript Function');
  });
});
```

### Integration Tests
```typescript
describe('PromptLibrary Integration', () => {
  test('should handle complete prompt lifecycle', async () => {
    const library = new PromptLibrary(new SQLiteStorage());
    
    // Create
    const promptId = await library.save({
      title: 'Integration Test',
      content: 'Test content',
      category: 'test',
      tags: ['integration']
    } as Prompt);
    
    // Read
    const saved = await library.load(promptId);
    expect(saved?.title).toBe('Integration Test');
    
    // Update
    await library.update(promptId, { title: 'Updated Test' });
    const updated = await library.load(promptId);
    expect(updated?.title).toBe('Updated Test');
    
    // Delete
    await library.delete(promptId);
    const deleted = await library.load(promptId);
    expect(deleted).toBeNull();
  });
});
```
