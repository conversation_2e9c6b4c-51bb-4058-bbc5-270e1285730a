/**
 * Main Voice Module implementation
 * Provides speech-to-text capabilities with real-time transcription and voice commands
 */

import { EventEmitter } from 'events';
import {
  IVoiceModule,
  VoiceModuleConfig,
  RecordingOptions,
  RecordingState,
  STTOptions,
  // TranscriptionResult,
  VoiceCommand,
  // VoiceCommandResult,
  AudioProcessingOptions,
  VoiceModuleError,
  AudioRecordingError,
  STTServiceError,
  // VoiceModuleEvent
} from './types';
import { AudioRecorder } from './AudioRecorder';
import { STTBridge } from './STTBridge';
import { AudioProcessor } from './AudioProcessor';
import { VoiceCommandProcessor } from './VoiceCommandProcessor';
import { LoggerFactory } from '../../core/logger';
import { toError } from '../../core/utils/errorUtils';

const logger = LoggerFactory.getInstance().getLogger('VoiceModule');

/**
 * Main Voice Module implementation
 */
export class VoiceModule extends EventEmitter implements IVoiceModule {
  private config: VoiceModuleConfig;
  private audioRecorder!: AudioRecorder;
  private sttBridge!: STTBridge;
  private audioProcessor!: AudioProcessor;
  private commandProcessor!: VoiceCommandProcessor;
  private isInitialized: boolean = false;
  private continuousListening: boolean = false;
  private continuousListeningInterval: NodeJS.Timeout | undefined;

  constructor(config?: Partial<VoiceModuleConfig>) {
    super();
    
    this.config = {
      stt: {
        provider: 'openai',
        defaultModel: 'whisper-1',
        defaultLanguage: 'en-US',
        ...config?.stt
      },
      recording: {
        defaultFormat: 'wav',
        defaultSampleRate: 16000,
        defaultChannels: 1,
        maxDuration: 300, // 5 minutes
        autoStop: true,
        noiseReduction: true,
        ...config?.recording
      },
      commands: {
        enabled: true,
        customCommands: [],
        ...config?.commands
      },
      processing: {
        enableNoiseReduction: true,
        enableVolumeNormalization: true,
        enableFormatConversion: false,
        ...config?.processing
      }
    };

    this.initializeComponents();
  }

  /**
   * Initialize all components
   */
  private initializeComponents(): void {
    this.audioRecorder = new AudioRecorder();
    this.sttBridge = new STTBridge({
      provider: this.config.stt.provider,
      apiKey: this.config.stt.apiKey || '',
      ...(this.config.stt.baseURL && { baseURL: this.config.stt.baseURL }),
      defaultModel: this.config.stt.defaultModel || 'whisper-1'
    });
    this.audioProcessor = new AudioProcessor();
    this.commandProcessor = new VoiceCommandProcessor();

    // Register built-in commands if enabled
    if (this.config.commands.enabled) {
      this.registerBuiltInCommands();
    }

    // Register custom commands
    for (const command of this.config.commands.customCommands) {
      this.commandProcessor.registerCommand(command);
    }
  }

  /**
   * Initialize the voice module
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      await this.audioRecorder.initialize();
      this.isInitialized = true;
      
      logger.info('Voice module initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize voice module', { error: toError(error) });
      throw new VoiceModuleError('Failed to initialize voice module', toError(error));
    }
  }

  /**
   * Start recording audio
   */
  async startRecording(options?: RecordingOptions): Promise<void> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const recordingOptions: RecordingOptions = {
        language: this.config.stt.defaultLanguage || 'en-US',
        maxDuration: this.config.recording.maxDuration,
        autoStop: this.config.recording.autoStop,
        noiseReduction: this.config.recording.noiseReduction,
        format: this.config.recording.defaultFormat,
        sampleRate: this.config.recording.defaultSampleRate,
        channels: this.config.recording.defaultChannels,
        ...(options?.bitDepth !== undefined && { bitDepth: options.bitDepth }),
        ...options
      };

      await this.audioRecorder.startRecording(recordingOptions);
      
      this.emit('recording-start');
      logger.info('Recording started', { options: recordingOptions });
    } catch (error) {
      logger.error('Failed to start recording', { error: toError(error), options });
      this.emit('error', new AudioRecordingError('Failed to start recording', toError(error)));
      throw error;
    }
  }

  /**
   * Stop recording and transcribe audio
   */
  async stopRecording(): Promise<string> {
    try {
      if (!this.audioRecorder.isRecording()) {
        throw new AudioRecordingError('No active recording to stop');
      }

      const audioBlob = await this.audioRecorder.stopRecording();
      this.emit('recording-stop');

      // Process audio if needed
      let processedAudio = audioBlob;
      if (this.config.processing.enableNoiseReduction || 
          this.config.processing.enableVolumeNormalization ||
          this.config.processing.enableFormatConversion) {
        
        const processingOptions: AudioProcessingOptions = {
          noiseReduction: this.config.processing.enableNoiseReduction,
          volumeNormalization: this.config.processing.enableVolumeNormalization,
          ...(this.config.processing.enableFormatConversion && {
            formatConversion: this.config.recording.defaultFormat
          })
        };
        processedAudio = await this.audioProcessor.processAudio(audioBlob, processingOptions);
      }

      // Transcribe audio
      const transcriptionResult = await this.sttBridge.transcribe(processedAudio, {
        language: this.config.stt.defaultLanguage || 'en-US',
        model: this.config.stt.defaultModel || 'whisper-1',
        punctuation: true
      });

      this.emit('transcription', transcriptionResult);

      // Process voice commands if enabled
      if (this.config.commands.enabled) {
        const commandResult = await this.commandProcessor.processTranscription(transcriptionResult.text);
        if (commandResult) {
          this.emit('command', commandResult);
        }
      }

      logger.info('Recording transcribed', { 
        text: transcriptionResult.text.substring(0, 100),
        confidence: transcriptionResult.confidence 
      });

      return transcriptionResult.text;
    } catch (error) {
      logger.error('Failed to stop recording and transcribe', { error: toError(error) });
      this.emit('error', new STTServiceError('Failed to transcribe recording', toError(error)));
      throw error;
    }
  }

  /**
   * Check if currently recording
   */
  isRecording(): boolean {
    return this.audioRecorder.isRecording();
  }

  /**
   * Get current recording state
   */
  getRecordingState(): RecordingState {
    return this.audioRecorder.getRecordingState();
  }

  /**
   * Process an audio file
   */
  async processAudioFile(filePath: string, options?: STTOptions): Promise<string> {
    try {
      const transcriptionOptions: STTOptions = {
        language: this.config.stt.defaultLanguage || 'en-US',
        model: this.config.stt.defaultModel || 'whisper-1',
        punctuation: true,
        ...(options?.profanityFilter !== undefined && { profanityFilter: options.profanityFilter }),
        ...(options?.speakerDiarization !== undefined && { speakerDiarization: options.speakerDiarization }),
        ...(options?.wordTimestamps !== undefined && { wordTimestamps: options.wordTimestamps }),
        ...options
      };

      const result = await this.sttBridge.transcribeFile(filePath, transcriptionOptions);
      
      this.emit('transcription', result);
      
      // Process voice commands if enabled
      if (this.config.commands.enabled) {
        const commandResult = await this.commandProcessor.processTranscription(result.text);
        if (commandResult) {
          this.emit('command', commandResult);
        }
      }

      logger.info('Audio file processed', { 
        filePath, 
        text: result.text.substring(0, 100),
        confidence: result.confidence 
      });

      return result.text;
    } catch (error) {
      logger.error('Failed to process audio file', { error: toError(error), filePath });
      this.emit('error', new STTServiceError('Failed to process audio file', toError(error)));
      throw error;
    }
  }

  /**
   * Start continuous listening mode
   */
  async startContinuousListening(): Promise<void> {
    try {
      if (this.continuousListening) {
        return; // Already listening
      }

      this.continuousListening = true;
      this.emit('continuous-start');

      // Start continuous recording loop
      this.continuousListeningInterval = setInterval(async () => {
        if (!this.continuousListening) {
          return;
        }

        try {
          if (!this.isRecording()) {
            await this.startRecording({
              language: this.config.stt.defaultLanguage || 'en-US',
              maxDuration: 10, // 10 second chunks
              autoStop: true,
              noiseReduction: this.config.recording.noiseReduction,
              format: this.config.recording.defaultFormat,
              sampleRate: this.config.recording.defaultSampleRate,
              channels: this.config.recording.defaultChannels
            });
          }
        } catch (error) {
          logger.error('Error in continuous listening', { error });
          this.emit('error', error);
        }
      }, 100); // Check every 100ms

      logger.info('Continuous listening started');
    } catch (error) {
      logger.error('Failed to start continuous listening', { error: toError(error) });
      this.emit('error', new VoiceModuleError('Failed to start continuous listening', toError(error)));
      throw error;
    }
  }

  /**
   * Stop continuous listening mode
   */
  async stopContinuousListening(): Promise<void> {
    try {
      this.continuousListening = false;
      
      if (this.continuousListeningInterval) {
        clearInterval(this.continuousListeningInterval);
        this.continuousListeningInterval = undefined;
      }

      if (this.isRecording()) {
        await this.stopRecording();
      }

      this.emit('continuous-stop');
      logger.info('Continuous listening stopped');
    } catch (error) {
      logger.error('Failed to stop continuous listening', { error: toError(error) });
      this.emit('error', new VoiceModuleError('Failed to stop continuous listening', toError(error)));
      throw error;
    }
  }

  /**
   * Check if in continuous listening mode
   */
  isContinuousListening(): boolean {
    return this.continuousListening;
  }

  /**
   * Register a voice command
   */
  registerCommand(command: VoiceCommand): void {
    this.commandProcessor.registerCommand(command);
    logger.info('Voice command registered', { name: command.name, trigger: command.trigger });
  }

  /**
   * Unregister a voice command
   */
  unregisterCommand(commandName: string): void {
    this.commandProcessor.unregisterCommand(commandName);
    logger.info('Voice command unregistered', { name: commandName });
  }

  /**
   * Get all registered commands
   */
  getRegisteredCommands(): VoiceCommand[] {
    return this.commandProcessor.getRegisteredCommands();
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<VoiceModuleConfig>): void {
    this.config = { ...this.config, ...config };
    
    // Reinitialize components if needed
    if (config.stt) {
      this.sttBridge = new STTBridge({
        provider: this.config.stt.provider,
        apiKey: this.config.stt.apiKey || '',
        ...(this.config.stt.baseURL && { baseURL: this.config.stt.baseURL }),
        defaultModel: this.config.stt.defaultModel || 'whisper-1'
      });
    }

    logger.info('Voice module configuration updated');
  }

  /**
   * Get current configuration
   */
  getConfig(): VoiceModuleConfig {
    return { ...this.config };
  }

  /**
   * Register built-in voice commands
   */
  private registerBuiltInCommands(): void {
    const builtInCommands: VoiceCommand[] = [
      {
        name: 'enhance-prompt',
        trigger: 'enhance this',
        description: 'Enhance the current prompt',
        execute: async (text: string) => {
          const promptText = text.replace(/^enhance this\s*/i, '');
          // This would integrate with the prompt engine
          return { action: 'enhance', text: promptText };
        }
      },
      {
        name: 'save-prompt',
        trigger: 'save prompt',
        description: 'Save the current prompt',
        execute: async (text: string) => {
          const promptText = text.replace(/^save prompt\s*/i, '');
          // This would integrate with the prompt library
          return { action: 'save', text: promptText };
        }
      },
      {
        name: 'stop-recording',
        trigger: 'stop recording',
        description: 'Stop the current recording',
        execute: async () => {
          if (this.isRecording()) {
            return await this.stopRecording();
          }
          return { action: 'stop', message: 'No active recording' };
        }
      }
    ];

    for (const command of builtInCommands) {
      this.commandProcessor.registerCommand(command);
    }

    logger.info('Built-in voice commands registered', { count: builtInCommands.length });
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    try {
      if (this.continuousListening) {
        await this.stopContinuousListening();
      }

      if (this.isRecording()) {
        await this.stopRecording();
      }

      this.audioRecorder.cleanup();
      this.removeAllListeners();

      logger.info('Voice module cleaned up');
    } catch (error) {
      logger.error('Error during voice module cleanup', { error });
    }
  }
}

// Global instance management
let globalVoiceModule: VoiceModule | null = null;

export const getVoiceModule = (): VoiceModule => {
  if (!globalVoiceModule) {
    globalVoiceModule = new VoiceModule();
  }
  return globalVoiceModule;
};

export const initializeVoiceModule = async (config?: Partial<VoiceModuleConfig>): Promise<VoiceModule> => {
  globalVoiceModule = new VoiceModule(config);
  await globalVoiceModule.initialize();
  return globalVoiceModule;
};
