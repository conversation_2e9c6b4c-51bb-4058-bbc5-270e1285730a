/**
 * Context Processor
 * Processes and cleans extracted context data
 */

import { ContentType } from './types';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('ContextProcessor');

/**
 * Context Processor implementation
 */
export class ContextProcessor {
  /**
   * Clean and normalize text
   */
  cleanText(text: string): string {
    try {
      return text
        .replace(/\r\n/g, '\n')           // Normalize line endings
        .replace(/\t/g, '  ')             // Replace tabs with spaces
        .replace(/\s+/g, ' ')             // Collapse multiple spaces
        .replace(/\n\s*\n/g, '\n')        // Remove empty lines
        .trim();                          // Remove leading/trailing whitespace
    } catch (error) {
      logger.error('Failed to clean text', { error });
      return text;
    }
  }

  /**
   * Detect language of text
   */
  detectLanguage(text: string): string {
    try {
      // Simple language detection based on character patterns
      const cleanText = text.toLowerCase().replace(/[^a-z\s]/g, '');
      
      // Common English words
      const englishWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
      const englishCount = englishWords.reduce((count, word) => {
        return count + (cleanText.split(word).length - 1);
      }, 0);

      // Spanish words
      const spanishWords = ['el', 'la', 'de', 'que', 'y', 'en', 'un', 'es', 'se', 'no', 'te', 'lo'];
      const spanishCount = spanishWords.reduce((count, word) => {
        return count + (cleanText.split(word).length - 1);
      }, 0);

      // French words
      const frenchWords = ['le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir', 'que', 'pour'];
      const frenchCount = frenchWords.reduce((count, word) => {
        return count + (cleanText.split(word).length - 1);
      }, 0);

      if (englishCount > spanishCount && englishCount > frenchCount) {
        return 'en';
      } else if (spanishCount > frenchCount) {
        return 'es';
      } else if (frenchCount > 0) {
        return 'fr';
      }

      return 'en'; // Default to English
    } catch (error) {
      logger.error('Failed to detect language', { error });
      return 'en';
    }
  }

  /**
   * Summarize text to specified length
   */
  summarizeText(text: string, maxLength: number = 500): string {
    try {
      if (text.length <= maxLength) {
        return text;
      }

      // Split into sentences
      const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
      
      if (sentences.length === 0) {
        return text.substring(0, maxLength) + '...';
      }

      // Take first few sentences that fit within maxLength
      let summary = '';
      for (const sentence of sentences) {
        const trimmedSentence = sentence.trim();
        if (summary.length + trimmedSentence.length + 1 <= maxLength) {
          summary += (summary ? '. ' : '') + trimmedSentence;
        } else {
          break;
        }
      }

      return summary + (summary.length < text.length ? '...' : '');
    } catch (error) {
      logger.error('Failed to summarize text', { error });
      return text.substring(0, maxLength) + '...';
    }
  }

  /**
   * Extract keywords from text
   */
  extractKeywords(text: string): string[] {
    try {
      const cleanText = text.toLowerCase()
        .replace(/[^\w\s]/g, ' ')
        .replace(/\s+/g, ' ')
        .trim();

      const words = cleanText.split(' ');
      
      // Common stop words to filter out
      const stopWords = new Set([
        'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
        'a', 'an', 'as', 'are', 'was', 'were', 'been', 'be', 'have', 'has', 'had',
        'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might',
        'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they'
      ]);

      // Count word frequencies
      const wordCount = new Map<string, number>();
      for (const word of words) {
        if (word.length > 2 && !stopWords.has(word)) {
          wordCount.set(word, (wordCount.get(word) || 0) + 1);
        }
      }

      // Sort by frequency and return top keywords
      return Array.from(wordCount.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10)
        .map(([word]) => word);
    } catch (error) {
      logger.error('Failed to extract keywords', { error });
      return [];
    }
  }

  /**
   * Format content based on type
   */
  formatContent(content: string, type: ContentType): string {
    try {
      switch (type) {
        case 'code':
          return this.formatCodeContent(content);
        case 'json':
          return this.formatJsonContent(content);
        case 'csv':
          return this.formatCsvContent(content);
        case 'html':
          return this.formatHtmlContent(content);
        case 'markdown':
          return this.formatMarkdownContent(content);
        case 'text':
        default:
          return this.cleanText(content);
      }
    } catch (error) {
      logger.error('Failed to format content', { error, type });
      return content;
    }
  }

  /**
   * Format code content
   */
  private formatCodeContent(content: string): string {
    // Add syntax highlighting markers and clean up
    return content
      .replace(/\r\n/g, '\n')
      .replace(/\t/g, '  ')
      .trim();
  }

  /**
   * Format JSON content
   */
  private formatJsonContent(content: string): string {
    try {
      const parsed = JSON.parse(content);
      return JSON.stringify(parsed, null, 2);
    } catch {
      return content;
    }
  }

  /**
   * Format CSV content
   */
  private formatCsvContent(content: string): string {
    const lines = content.split('\n').filter(line => line.trim());
    if (lines.length === 0) return content;

    // Format as table-like structure
    const rows = lines.map(line => line.split(',').map(cell => cell.trim()));
    const maxCols = Math.max(...rows.map(row => row.length));
    
    return rows.map(row => {
      const paddedRow = [...row];
      while (paddedRow.length < maxCols) {
        paddedRow.push('');
      }
      return paddedRow.join(' | ');
    }).join('\n');
  }

  /**
   * Format HTML content
   */
  private formatHtmlContent(content: string): string {
    // Extract text content from HTML
    return content
      .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
      .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
      .replace(/<[^>]*>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * Format Markdown content
   */
  private formatMarkdownContent(content: string): string {
    return content
      .replace(/\r\n/g, '\n')
      .replace(/\n{3,}/g, '\n\n')
      .trim();
  }

  /**
   * Remove sensitive information
   */
  removeSensitiveInfo(text: string): string {
    try {
      return text
        .replace(/\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g, '[CARD-NUMBER]')
        .replace(/\b\d{3}-\d{2}-\d{4}\b/g, '[SSN]')
        .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL]')
        .replace(/\b(?:\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b/g, '[PHONE]');
    } catch (error) {
      logger.error('Failed to remove sensitive info', { error });
      return text;
    }
  }

  /**
   * Normalize whitespace
   */
  normalizeWhitespace(text: string): string {
    return text
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      .replace(/\t/g, '  ')
      .replace(/ +/g, ' ')
      .replace(/\n +/g, '\n')
      .replace(/ +\n/g, '\n')
      .replace(/\n{3,}/g, '\n\n')
      .trim();
  }
}
