# Logger Module Specification

## 🎯 Purpose

The Logger module provides comprehensive logging capabilities for the PromptPilot Desktop application, offering structured logging, multiple output targets, log rotation, and performance monitoring with configurable log levels and filtering.

## 📋 Responsibilities

### Logging Operations
- Structured logging with multiple levels
- Contextual logging with metadata
- Performance and timing measurements
- Error tracking and stack traces

### Output Management
- Multiple output targets (file, console, remote)
- Log rotation and archival
- Real-time log streaming
- Log formatting and serialization

### Monitoring & Analytics
- Log aggregation and analysis
- Performance metrics collection
- Error rate monitoring
- Custom event tracking

## 🏗️ Architecture

### Core Logger
```typescript
class Logger {
  private transports: LogTransport[] = [];
  private level: LogLevel = LogLevel.INFO;
  private context: LogContext = {};
  private filters: LogFilter[] = [];
  
  debug(message: string, meta?: LogMetadata): void;
  info(message: string, meta?: LogMetadata): void;
  warn(message: string, meta?: LogMetadata): void;
  error(message: string | Error, meta?: LogMetadata): void;
  fatal(message: string | Error, meta?: LogMetadata): void;
  
  time(label: string): void;
  timeEnd(label: string): void;
  
  child(context: LogContext): Logger;
  addTransport(transport: LogTransport): void;
  setLevel(level: LogLevel): void;
  addFilter(filter: LogFilter): void;
}
```

### Log Levels and Types
```typescript
enum LogLevel {
  TRACE = 0,
  DEBUG = 1,
  INFO = 2,
  WARN = 3,
  ERROR = 4,
  FATAL = 5
}

interface LogEntry {
  timestamp: Date;
  level: LogLevel;
  message: string;
  context: LogContext;
  metadata?: LogMetadata;
  error?: ErrorInfo;
  performance?: PerformanceInfo;
}

interface LogContext {
  module?: string;
  component?: string;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  [key: string]: any;
}

interface LogMetadata {
  tags?: string[];
  duration?: number;
  memoryUsage?: NodeJS.MemoryUsage;
  [key: string]: any;
}

interface ErrorInfo {
  name: string;
  message: string;
  stack?: string;
  code?: string | number;
  cause?: ErrorInfo;
}

interface PerformanceInfo {
  startTime: number;
  endTime: number;
  duration: number;
  memoryBefore?: NodeJS.MemoryUsage;
  memoryAfter?: NodeJS.MemoryUsage;
}
```

## 📝 Log Transports

### File Transport
```typescript
class FileTransport implements LogTransport {
  private filePath: string;
  private maxFileSize: number;
  private maxFiles: number;
  private currentSize: number = 0;
  private writeStream: fs.WriteStream;
  
  constructor(options: FileTransportOptions) {
    this.filePath = options.filePath;
    this.maxFileSize = options.maxFileSize || 10 * 1024 * 1024; // 10MB
    this.maxFiles = options.maxFiles || 5;
    this.initializeStream();
  }
  
  async write(entry: LogEntry): Promise<void> {
    const formatted = this.format(entry);
    const size = Buffer.byteLength(formatted, 'utf8');
    
    if (this.currentSize + size > this.maxFileSize) {
      await this.rotate();
    }
    
    return new Promise((resolve, reject) => {
      this.writeStream.write(formatted + '\n', (error) => {
        if (error) {
          reject(error);
        } else {
          this.currentSize += size;
          resolve();
        }
      });
    });
  }
  
  private async rotate(): Promise<void> {
    await this.closeStream();
    await this.archiveCurrentFile();
    await this.cleanupOldFiles();
    this.initializeStream();
  }
  
  private async archiveCurrentFile(): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const archivePath = `${this.filePath}.${timestamp}`;
    await fs.rename(this.filePath, archivePath);
  }
  
  private async cleanupOldFiles(): Promise<void> {
    const dir = path.dirname(this.filePath);
    const basename = path.basename(this.filePath);
    const files = await fs.readdir(dir);
    
    const logFiles = files
      .filter(file => file.startsWith(basename) && file !== basename)
      .map(file => ({
        name: file,
        path: path.join(dir, file),
        stat: fs.statSync(path.join(dir, file))
      }))
      .sort((a, b) => b.stat.mtime.getTime() - a.stat.mtime.getTime());
    
    const filesToDelete = logFiles.slice(this.maxFiles - 1);
    
    for (const file of filesToDelete) {
      await fs.unlink(file.path);
    }
  }
  
  private initializeStream(): void {
    const dir = path.dirname(this.filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    this.writeStream = fs.createWriteStream(this.filePath, { flags: 'a' });
    this.currentSize = fs.existsSync(this.filePath) 
      ? fs.statSync(this.filePath).size 
      : 0;
  }
  
  private async closeStream(): Promise<void> {
    return new Promise((resolve) => {
      this.writeStream.end(() => resolve());
    });
  }
  
  private format(entry: LogEntry): string {
    return JSON.stringify({
      timestamp: entry.timestamp.toISOString(),
      level: LogLevel[entry.level],
      message: entry.message,
      context: entry.context,
      metadata: entry.metadata,
      error: entry.error,
      performance: entry.performance
    });
  }
}

interface FileTransportOptions {
  filePath: string;
  maxFileSize?: number;
  maxFiles?: number;
  format?: LogFormatter;
}
```

### Console Transport
```typescript
class ConsoleTransport implements LogTransport {
  private colorize: boolean;
  private formatter: LogFormatter;
  
  constructor(options: ConsoleTransportOptions = {}) {
    this.colorize = options.colorize ?? true;
    this.formatter = options.formatter || new DefaultFormatter();
  }
  
  async write(entry: LogEntry): Promise<void> {
    const formatted = this.formatter.format(entry);
    const output = this.colorize ? this.applyColors(formatted, entry.level) : formatted;
    
    if (entry.level >= LogLevel.ERROR) {
      console.error(output);
    } else {
      console.log(output);
    }
  }
  
  private applyColors(message: string, level: LogLevel): string {
    const colors = {
      [LogLevel.TRACE]: '\x1b[90m', // Gray
      [LogLevel.DEBUG]: '\x1b[36m', // Cyan
      [LogLevel.INFO]: '\x1b[32m',  // Green
      [LogLevel.WARN]: '\x1b[33m',  // Yellow
      [LogLevel.ERROR]: '\x1b[31m', // Red
      [LogLevel.FATAL]: '\x1b[35m'  // Magenta
    };
    
    const reset = '\x1b[0m';
    return `${colors[level]}${message}${reset}`;
  }
}

interface ConsoleTransportOptions {
  colorize?: boolean;
  formatter?: LogFormatter;
}
```

### Remote Transport
```typescript
class RemoteTransport implements LogTransport {
  private endpoint: string;
  private apiKey: string;
  private batchSize: number;
  private flushInterval: number;
  private buffer: LogEntry[] = [];
  private flushTimer: NodeJS.Timeout | null = null;
  
  constructor(options: RemoteTransportOptions) {
    this.endpoint = options.endpoint;
    this.apiKey = options.apiKey;
    this.batchSize = options.batchSize || 100;
    this.flushInterval = options.flushInterval || 5000;
    this.startFlushTimer();
  }
  
  async write(entry: LogEntry): Promise<void> {
    this.buffer.push(entry);
    
    if (this.buffer.length >= this.batchSize) {
      await this.flush();
    }
  }
  
  private async flush(): Promise<void> {
    if (this.buffer.length === 0) return;
    
    const entries = this.buffer.splice(0);
    
    try {
      await fetch(this.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({ entries })
      });
    } catch (error) {
      console.error('Failed to send logs to remote endpoint:', error);
      // Could implement retry logic here
    }
  }
  
  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flush().catch(console.error);
    }, this.flushInterval);
  }
  
  async close(): Promise<void> {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    await this.flush();
  }
}

interface RemoteTransportOptions {
  endpoint: string;
  apiKey: string;
  batchSize?: number;
  flushInterval?: number;
}
```

## 🎨 Log Formatting

### Default Formatter
```typescript
class DefaultFormatter implements LogFormatter {
  format(entry: LogEntry): string {
    const timestamp = entry.timestamp.toISOString();
    const level = LogLevel[entry.level].padEnd(5);
    const context = this.formatContext(entry.context);
    const metadata = entry.metadata ? ` ${JSON.stringify(entry.metadata)}` : '';
    
    let message = `${timestamp} [${level}]${context} ${entry.message}${metadata}`;
    
    if (entry.error) {
      message += `\n${this.formatError(entry.error)}`;
    }
    
    if (entry.performance) {
      message += ` (${entry.performance.duration}ms)`;
    }
    
    return message;
  }
  
  private formatContext(context: LogContext): string {
    const parts: string[] = [];
    
    if (context.module) parts.push(`${context.module}`);
    if (context.component) parts.push(`${context.component}`);
    if (context.requestId) parts.push(`req:${context.requestId.slice(0, 8)}`);
    
    return parts.length > 0 ? ` [${parts.join(':')}]` : '';
  }
  
  private formatError(error: ErrorInfo): string {
    let formatted = `${error.name}: ${error.message}`;
    
    if (error.stack) {
      formatted += `\n${error.stack}`;
    }
    
    if (error.cause) {
      formatted += `\nCaused by: ${this.formatError(error.cause)}`;
    }
    
    return formatted;
  }
}

class JSONFormatter implements LogFormatter {
  format(entry: LogEntry): string {
    return JSON.stringify({
      timestamp: entry.timestamp.toISOString(),
      level: LogLevel[entry.level],
      message: entry.message,
      context: entry.context,
      metadata: entry.metadata,
      error: entry.error,
      performance: entry.performance
    });
  }
}
```

## 📊 Performance Monitoring

### Performance Logger
```typescript
class PerformanceLogger {
  private timers: Map<string, number> = new Map();
  private logger: Logger;
  
  constructor(logger: Logger) {
    this.logger = logger;
  }
  
  time(label: string): void {
    this.timers.set(label, performance.now());
  }
  
  timeEnd(label: string): number {
    const startTime = this.timers.get(label);
    if (!startTime) {
      this.logger.warn(`Timer '${label}' does not exist`);
      return 0;
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    this.timers.delete(label);
    
    this.logger.info(`Performance: ${label}`, {
      performance: {
        startTime,
        endTime,
        duration,
        memoryBefore: process.memoryUsage(),
        memoryAfter: process.memoryUsage()
      }
    });
    
    return duration;
  }
  
  async measure<T>(label: string, fn: () => Promise<T>): Promise<T> {
    this.time(label);
    try {
      const result = await fn();
      this.timeEnd(label);
      return result;
    } catch (error) {
      this.timeEnd(label);
      throw error;
    }
  }
  
  measureSync<T>(label: string, fn: () => T): T {
    this.time(label);
    try {
      const result = fn();
      this.timeEnd(label);
      return result;
    } catch (error) {
      this.timeEnd(label);
      throw error;
    }
  }
}
```

## 🔍 Log Filtering

### Log Filters
```typescript
interface LogFilter {
  shouldLog(entry: LogEntry): boolean;
}

class LevelFilter implements LogFilter {
  constructor(private minLevel: LogLevel) {}
  
  shouldLog(entry: LogEntry): boolean {
    return entry.level >= this.minLevel;
  }
}

class ModuleFilter implements LogFilter {
  constructor(private allowedModules: string[]) {}
  
  shouldLog(entry: LogEntry): boolean {
    if (!entry.context.module) return true;
    return this.allowedModules.includes(entry.context.module);
  }
}

class RateLimitFilter implements LogFilter {
  private counts: Map<string, { count: number; resetTime: number }> = new Map();
  
  constructor(
    private maxCount: number,
    private windowMs: number
  ) {}
  
  shouldLog(entry: LogEntry): boolean {
    const key = `${entry.level}:${entry.message}`;
    const now = Date.now();
    const window = this.counts.get(key);
    
    if (!window || now > window.resetTime) {
      this.counts.set(key, { count: 1, resetTime: now + this.windowMs });
      return true;
    }
    
    if (window.count >= this.maxCount) {
      return false;
    }
    
    window.count++;
    return true;
  }
}
```

## 🏭 Logger Factory

### Logger Factory
```typescript
class LoggerFactory {
  private static instance: LoggerFactory;
  private loggers: Map<string, Logger> = new Map();
  private defaultConfig: LoggerConfig;
  
  static getInstance(): LoggerFactory {
    if (!LoggerFactory.instance) {
      LoggerFactory.instance = new LoggerFactory();
    }
    return LoggerFactory.instance;
  }
  
  configure(config: LoggerConfig): void {
    this.defaultConfig = config;
  }
  
  getLogger(name: string, context?: LogContext): Logger {
    const key = `${name}:${JSON.stringify(context || {})}`;
    
    if (!this.loggers.has(key)) {
      const logger = this.createLogger(name, context);
      this.loggers.set(key, logger);
    }
    
    return this.loggers.get(key)!;
  }
  
  private createLogger(name: string, context?: LogContext): Logger {
    const logger = new Logger();
    
    // Set context
    if (context) {
      logger.setContext({ module: name, ...context });
    } else {
      logger.setContext({ module: name });
    }
    
    // Apply default configuration
    if (this.defaultConfig) {
      logger.setLevel(this.defaultConfig.level);
      
      this.defaultConfig.transports.forEach(transport => {
        logger.addTransport(transport);
      });
      
      this.defaultConfig.filters.forEach(filter => {
        logger.addFilter(filter);
      });
    }
    
    return logger;
  }
}

interface LoggerConfig {
  level: LogLevel;
  transports: LogTransport[];
  filters: LogFilter[];
}
```

## 🧪 Testing Strategy

### Unit Tests
```typescript
describe('Logger', () => {
  let logger: Logger;
  let mockTransport: jest.Mocked<LogTransport>;
  
  beforeEach(() => {
    mockTransport = {
      write: jest.fn().mockResolvedValue(undefined)
    };
    
    logger = new Logger();
    logger.addTransport(mockTransport);
  });
  
  test('should log info message', async () => {
    await logger.info('Test message', { tag: 'test' });
    
    expect(mockTransport.write).toHaveBeenCalledWith(
      expect.objectContaining({
        level: LogLevel.INFO,
        message: 'Test message',
        metadata: { tag: 'test' }
      })
    );
  });
  
  test('should measure performance', () => {
    const perfLogger = new PerformanceLogger(logger);
    
    const result = perfLogger.measureSync('test-operation', () => {
      return 'result';
    });
    
    expect(result).toBe('result');
    expect(mockTransport.write).toHaveBeenCalledWith(
      expect.objectContaining({
        message: 'Performance: test-operation',
        performance: expect.objectContaining({
          duration: expect.any(Number)
        })
      })
    );
  });
});
```
