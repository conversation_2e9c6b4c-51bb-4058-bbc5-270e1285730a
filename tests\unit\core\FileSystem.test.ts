/**
 * File system module unit tests
 */

import * as fs from 'fs/promises';
import * as fsSync from 'fs';
import * as path from 'path';
import * as os from 'os';
import { FileSystemManager } from '../../../src/core/filesystem/FileSystemManager';
import { PathUtils } from '../../../src/core/filesystem/PathUtils';
import { FileWatcher } from '../../../src/core/filesystem/FileWatcher';

// Mock file system operations
jest.mock('fs/promises');
jest.mock('fs');

const mockFs = fs as jest.Mocked<typeof fs>;
const mockFsSync = fsSync as jest.Mocked<typeof fsSync>;

describe('FileSystemManager', () => {
  let fileSystemManager: FileSystemManager;
  let tempDir: string;

  beforeEach(() => {
    fileSystemManager = new FileSystemManager();
    tempDir = path.join(os.tmpdir(), 'test-filesystem');
    jest.clearAllMocks();
  });

  afterEach(async () => {
    // Cleanup
    try {
      await fileSystemManager.deleteDirectory(tempDir, { recursive: true });
    } catch {
      // Ignore cleanup errors
    }
  });

  describe('Basic File Operations', () => {
    test('should check if file exists', async () => {
      mockFs.access.mockResolvedValue();
      
      const exists = await fileSystemManager.exists('/test/file.txt');
      
      expect(exists).toBe(true);
      expect(mockFs.access).toHaveBeenCalledWith('/test/file.txt');
    });

    test('should return false for non-existent file', async () => {
      mockFs.access.mockRejectedValue(new Error('ENOENT'));
      
      const exists = await fileSystemManager.exists('/test/nonexistent.txt');
      
      expect(exists).toBe(false);
    });

    test('should read file contents', async () => {
      const testContent = 'Hello, World!';
      mockFs.stat.mockResolvedValue({ isFile: () => true } as any);
      mockFs.readFile.mockResolvedValue(testContent);
      
      const content = await fileSystemManager.readFile('/test/file.txt');
      
      expect(content).toBe(testContent);
      expect(mockFs.readFile).toHaveBeenCalledWith('/test/file.txt', expect.any(Object));
    });

    test('should write file contents', async () => {
      const testContent = 'Hello, World!';
      mockFs.mkdir.mockResolvedValue(undefined);
      mockFs.writeFile.mockResolvedValue();
      
      await fileSystemManager.writeFile('/test/file.txt', testContent);
      
      expect(mockFs.writeFile).toHaveBeenCalledWith('/test/file.txt', testContent, expect.any(Object));
    });

    test('should write file atomically', async () => {
      const testContent = 'Hello, World!';
      mockFs.mkdir.mockResolvedValue(undefined);
      mockFs.writeFile.mockResolvedValue();
      mockFs.rename.mockResolvedValue();
      
      await fileSystemManager.writeFile('/test/file.txt', testContent, { atomic: true });
      
      expect(mockFs.writeFile).toHaveBeenCalled();
      expect(mockFs.rename).toHaveBeenCalled();
    });

    test('should delete file', async () => {
      mockFs.stat.mockResolvedValue({ isFile: () => true } as any);
      mockFs.unlink.mockResolvedValue();
      
      await fileSystemManager.deleteFile('/test/file.txt');
      
      expect(mockFs.unlink).toHaveBeenCalledWith('/test/file.txt');
    });

    test('should copy file', async () => {
      mockFs.stat.mockResolvedValue({ 
        isFile: () => true,
        atime: new Date(),
        mtime: new Date()
      } as any);
      mockFs.access.mockRejectedValue(new Error('ENOENT')); // Destination doesn't exist
      mockFs.mkdir.mockResolvedValue(undefined);
      mockFs.copyFile.mockResolvedValue();
      
      await fileSystemManager.copyFile('/test/source.txt', '/test/dest.txt');
      
      expect(mockFs.copyFile).toHaveBeenCalledWith('/test/source.txt', '/test/dest.txt');
    });

    test('should move file', async () => {
      mockFs.access.mockRejectedValue(new Error('ENOENT')); // Destination doesn't exist
      mockFs.mkdir.mockResolvedValue(undefined);
      mockFs.rename.mockResolvedValue();
      
      await fileSystemManager.moveFile('/test/source.txt', '/test/dest.txt');
      
      expect(mockFs.rename).toHaveBeenCalledWith('/test/source.txt', '/test/dest.txt');
    });
  });

  describe('Directory Operations', () => {
    test('should create directory', async () => {
      mockFs.mkdir.mockResolvedValue(undefined);
      
      await fileSystemManager.createDirectory('/test/newdir');
      
      expect(mockFs.mkdir).toHaveBeenCalledWith('/test/newdir', expect.any(Object));
    });

    test('should create directory recursively', async () => {
      mockFs.mkdir.mockResolvedValue(undefined);
      
      await fileSystemManager.createDirectory('/test/deep/nested/dir', { recursive: true });
      
      expect(mockFs.mkdir).toHaveBeenCalledWith('/test/deep/nested/dir', { recursive: true });
    });

    test('should delete directory', async () => {
      mockFs.rmdir.mockResolvedValue();
      
      await fileSystemManager.deleteDirectory('/test/dir');
      
      expect(mockFs.rmdir).toHaveBeenCalledWith('/test/dir', expect.any(Object));
    });

    test('should list directory contents', async () => {
      const mockEntries = [
        { name: 'file1.txt', isDirectory: () => false, isFile: () => true },
        { name: 'subdir', isDirectory: () => true, isFile: () => false }
      ];
      
      mockFs.readdir.mockResolvedValue(mockEntries as any);
      mockFs.stat.mockResolvedValue({
        size: 1024,
        isFile: () => true,
        isDirectory: () => false,
        isSymbolicLink: () => false,
        mode: 0o644,
        birthtime: new Date(),
        mtime: new Date(),
        atime: new Date()
      } as any);
      
      const contents = await fileSystemManager.listDirectory('/test');
      
      expect(contents).toHaveLength(2);
      expect(mockFs.readdir).toHaveBeenCalledWith('/test', { withFileTypes: true });
    });
  });

  describe('File Metadata', () => {
    test('should get file metadata', async () => {
      const mockStats = {
        size: 1024,
        isFile: () => true,
        isDirectory: () => false,
        isSymbolicLink: () => false,
        mode: 0o644,
        birthtime: new Date('2023-01-01'),
        mtime: new Date('2023-01-02'),
        atime: new Date('2023-01-03')
      };
      
      mockFs.stat.mockResolvedValue(mockStats as any);
      
      const metadata = await fileSystemManager.getMetadata('/test/file.txt');
      
      expect(metadata.size).toBe(1024);
      expect(metadata.type).toBe('file');
      expect(metadata.name).toBe('file');
      expect(metadata.extension).toBe('.txt');
    });

    test('should set file permissions', async () => {
      mockFs.chmod.mockResolvedValue();
      
      await fileSystemManager.setPermissions('/test/file.txt', 0o755);
      
      expect(mockFs.chmod).toHaveBeenCalledWith('/test/file.txt', 0o755);
    });
  });

  describe('Backup Operations', () => {
    test('should create backup', async () => {
      mockFs.access.mockResolvedValueOnce(); // Source file exists
      mockFs.access.mockRejectedValueOnce(new Error('ENOENT')); // Backup doesn't exist
      mockFs.stat.mockResolvedValue({
        isFile: () => true,
        atime: new Date(),
        mtime: new Date()
      } as any);
      mockFs.mkdir.mockResolvedValue(undefined);
      mockFs.copyFile.mockResolvedValue();
      mockFs.utimes.mockResolvedValue();

      const backupPath = await fileSystemManager.createBackup('/test/file.txt');

      expect(backupPath).toContain('.backup.');
      expect(mockFs.copyFile).toHaveBeenCalled();
    });

    test('should restore backup', async () => {
      mockFs.stat.mockResolvedValue({ 
        isFile: () => true,
        atime: new Date(),
        mtime: new Date()
      } as any);
      mockFs.mkdir.mockResolvedValue(undefined);
      mockFs.copyFile.mockResolvedValue();
      mockFs.utimes.mockResolvedValue();
      
      await fileSystemManager.restoreBackup('/test/file.txt.backup.123', '/test/file.txt');
      
      expect(mockFs.copyFile).toHaveBeenCalledWith(
        '/test/file.txt.backup.123',
        '/test/file.txt'
      );
    });
  });

  describe('Path Validation', () => {
    test('should validate safe paths', () => {
      const result = fileSystemManager.validatePath('/safe/path/file.txt');

      expect(result.valid).toBe(true);
      expect(result.normalized).toBe('/safe/path/file.txt');
    });

    test('should reject dangerous paths', () => {
      const result = fileSystemManager.validatePath('../../../etc/passwd');

      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('should reject paths with null bytes', () => {
      const result = fileSystemManager.validatePath('/test/file\0.txt');

      expect(result.valid).toBe(false);
      expect(result.errors.some(e => e.includes('dangerous pattern'))).toBe(true);
    });
  });
});

describe('PathUtils', () => {
  describe('Path Normalization', () => {
    test('should normalize paths', () => {
      expect(PathUtils.normalize('/test//path/../file.txt')).toBe('/test/file.txt');
      expect(PathUtils.normalize('test\\path\\file.txt')).toBe('test/path/file.txt');
    });

    test('should handle empty and invalid paths', () => {
      expect(() => PathUtils.normalize('')).toThrow();
      expect(() => PathUtils.normalize(null as any)).toThrow();
    });
  });

  describe('Path Validation', () => {
    test('should validate safe filenames', () => {
      const result = PathUtils.validatePath('/safe/path/file.txt');
      expect(result.valid).toBe(true);
    });

    test('should reject directory traversal', () => {
      const result = PathUtils.validatePath('../../../etc/passwd');
      expect(result.valid).toBe(false);
    });

    test('should reject reserved names on Windows', () => {
      const originalPlatform = process.platform;
      Object.defineProperty(process, 'platform', { value: 'win32' });
      
      const result = PathUtils.validatePath('/test/CON.txt');
      expect(result.valid).toBe(false);
      
      Object.defineProperty(process, 'platform', { value: originalPlatform });
    });
  });

  describe('Filename Sanitization', () => {
    test('should sanitize invalid characters', () => {
      const sanitized = PathUtils.sanitizeFilename('file<>:"|?*.txt');
      expect(sanitized).toBe('file_______.txt');
    });

    test('should handle reserved names', () => {
      const originalPlatform = process.platform;
      Object.defineProperty(process, 'platform', { value: 'win32' });
      
      const sanitized = PathUtils.sanitizeFilename('CON.txt');
      expect(sanitized).toBe('_CON.txt');
      
      Object.defineProperty(process, 'platform', { value: originalPlatform });
    });

    test('should handle empty filenames', () => {
      expect(() => PathUtils.sanitizeFilename('')).toThrow('Filename must be a non-empty string');
    });
  });

  describe('Path Utilities', () => {
    test('should join paths correctly', () => {
      expect(PathUtils.join('/test', 'path', 'file.txt')).toBe(path.join('/test', 'path', 'file.txt'));
    });

    test('should resolve paths correctly', () => {
      expect(PathUtils.resolve('test', 'file.txt')).toBe(path.resolve('test', 'file.txt'));
    });

    test('should get path components', () => {
      expect(PathUtils.dirname('/test/path/file.txt')).toBe('/test/path');
      expect(PathUtils.basename('/test/path/file.txt')).toBe('file.txt');
      expect(PathUtils.extname('/test/path/file.txt')).toBe('.txt');
    });

    test('should generate unique filenames', () => {
      const existsCheck = (path: string) => PathUtils.normalize(path) === '/test/file.txt';
      const unique = PathUtils.generateUniqueFilename('/test/file.txt', existsCheck);
      expect(PathUtils.normalize(unique)).toBe('/test/file_1.txt');
    });
  });
});

describe('FileWatcher', () => {
  let watcher: FileWatcher;

  beforeEach(() => {
    // Mock fs.watch
    const mockWatcher = {
      on: jest.fn(),
      close: jest.fn()
    };
    mockFsSync.watch.mockReturnValue(mockWatcher as any);

    mockFsSync.existsSync.mockReturnValue(true);
    mockFsSync.statSync.mockReturnValue({
      size: 1024,
      isDirectory: () => false,
      isSymbolicLink: () => false,
      mode: 0o644,
      birthtime: new Date(),
      mtime: new Date(),
      atime: new Date()
    } as any);
  });

  afterEach(async () => {
    if (watcher && watcher.isActive) {
      await watcher.close();
    }
  });

  test('should create file watcher', () => {
    watcher = new FileWatcher('/test/path');
    expect(watcher.path).toBe('/test/path');
    expect(watcher.isActive).toBe(false);
  });

  test('should start watching', async () => {
    watcher = new FileWatcher('/test/path');
    await watcher.start();
    
    expect(watcher.isActive).toBe(true);
    expect(mockFsSync.watch).toHaveBeenCalledWith('/test/path', expect.any(Object));
  });

  test('should stop watching', async () => {
    watcher = new FileWatcher('/test/path');
    await watcher.start();
    await watcher.close();
    
    expect(watcher.isActive).toBe(false);
  });

  test('should handle watch events', async () => {
    watcher = new FileWatcher('/test/path');
    const changeHandler = jest.fn();
    watcher.on('change', changeHandler);

    await watcher.start();

    // Just verify the watcher is active and properly set up
    expect(watcher.isActive).toBe(true);
    expect(mockFsSync.watch).toHaveBeenCalledWith('/test/path', expect.any(Object));
  });
});
