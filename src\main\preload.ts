import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';
import { IPCMessageType } from '../shared/ipc/types';

// Define the comprehensive API that will be exposed to the renderer process
const electronAPI = {
  // App information
  getVersion: () => ipcRenderer.invoke('app:get-version'),
  getPlatform: () => ipc<PERSON>enderer.invoke('app:get-platform'),

  // Window controls
  window: {
    show: () => ipcRenderer.invoke(IPCMessageType.WINDOW_SHOW),
    hide: () => ipcRenderer.invoke(IPCMessageType.WINDOW_HIDE),
    minimize: () => ipcRenderer.invoke(IPCMessageType.WINDOW_MINIMIZE),
    maximize: () => ipcRenderer.invoke(IPCMessageType.WINDOW_MAXIMIZE),
    close: () => ipcRenderer.invoke(IPCMessageType.WINDOW_CLOSE),
    toggleDevTools: () => ipc<PERSON>enderer.invoke(IPCMessageType.WINDOW_TOGGLE_DEVTOOLS)
  },

  // Prompt operations
  prompts: {
    save: (prompt: any) => ipc<PERSON>enderer.invoke(IPCMessageType.PROMPT_SAVE, prompt),
    load: (id: string) => ipcRenderer.invoke(IPCMessageType.PROMPT_LOAD, id),
    loadAll: () => ipcRenderer.invoke(IPCMessageType.PROMPT_LOAD_ALL),
    delete: (id: string) => ipcRenderer.invoke(IPCMessageType.PROMPT_DELETE, id),
    enhance: (text: string) => ipcRenderer.invoke(IPCMessageType.PROMPT_ENHANCE, text),
    search: (query: string) => ipcRenderer.invoke(IPCMessageType.PROMPT_SEARCH, query),
    duplicate: (id: string) => ipcRenderer.invoke(IPCMessageType.PROMPT_DUPLICATE, id),
    export: (ids: string[]) => ipcRenderer.invoke(IPCMessageType.PROMPT_EXPORT, ids),
    import: (data: string) => ipcRenderer.invoke(IPCMessageType.PROMPT_IMPORT, data)
  },

  // Category operations
  categories: {
    save: (category: any) => ipcRenderer.invoke(IPCMessageType.CATEGORY_SAVE, category),
    loadAll: () => ipcRenderer.invoke(IPCMessageType.CATEGORY_LOAD_ALL),
    delete: (id: string) => ipcRenderer.invoke(IPCMessageType.CATEGORY_DELETE, id),
    move: (id: string, newParentId: string | null) => ipcRenderer.invoke(IPCMessageType.CATEGORY_MOVE, id, newParentId)
  },

  // Voice operations
  voice: {
    startRecording: () => ipcRenderer.invoke(IPCMessageType.VOICE_START_RECORDING),
    stopRecording: () => ipcRenderer.invoke(IPCMessageType.VOICE_STOP_RECORDING)
  },

  // Context operations
  context: {
    captureScreen: () => ipcRenderer.invoke(IPCMessageType.CONTEXT_CAPTURE_SCREEN),
    getClipboard: () => ipcRenderer.invoke(IPCMessageType.CONTEXT_GET_CLIPBOARD),
    extractText: (data: any) => ipcRenderer.invoke(IPCMessageType.CONTEXT_EXTRACT_TEXT, data),
    getActiveWindow: () => ipcRenderer.invoke(IPCMessageType.CONTEXT_GET_ACTIVE_WINDOW)
  },

  // AI operations
  ai: {
    generate: (request: any) => ipcRenderer.invoke(IPCMessageType.AI_GENERATE, request),
    enhance: (text: string) => ipcRenderer.invoke(IPCMessageType.AI_ENHANCE, text),
    analyze: (data: any) => ipcRenderer.invoke(IPCMessageType.AI_ANALYZE, data),
    getProviders: () => ipcRenderer.invoke(IPCMessageType.AI_PROVIDERS_LIST),
    getModels: (provider?: string) => ipcRenderer.invoke(IPCMessageType.AI_MODELS_LIST, provider)
  },

  // System operations
  system: {
    getInfo: () => ipcRenderer.invoke(IPCMessageType.SYSTEM_GET_INFO),
    notification: (notification: any) => ipcRenderer.invoke(IPCMessageType.SYSTEM_NOTIFICATION, notification),
    registerHotkey: (hotkey: any) => ipcRenderer.invoke(IPCMessageType.SYSTEM_HOTKEY_REGISTER, hotkey),
    unregisterHotkey: (hotkey: any) => ipcRenderer.invoke(IPCMessageType.SYSTEM_HOTKEY_UNREGISTER, hotkey)
  },

  // Settings operations
  settings: {
    get: (key: string) => ipcRenderer.invoke(IPCMessageType.SETTINGS_GET, key),
    set: (key: string, value: any) => ipcRenderer.invoke(IPCMessageType.SETTINGS_SET, key, value),
    reset: (key?: string) => ipcRenderer.invoke(IPCMessageType.SETTINGS_RESET, key),
    getAll: () => ipcRenderer.invoke(IPCMessageType.SETTINGS_GET_ALL)
  },

  // User operations
  user: {
    getCurrent: () => ipcRenderer.invoke(IPCMessageType.USER_GET_CURRENT),
    updatePreferences: (preferences: any) => ipcRenderer.invoke(IPCMessageType.USER_UPDATE_PREFERENCES, preferences),
    getStatistics: () => ipcRenderer.invoke(IPCMessageType.USER_GET_STATISTICS)
  },

  // Session operations
  session: {
    start: () => ipcRenderer.invoke(IPCMessageType.SESSION_START),
    end: () => ipcRenderer.invoke(IPCMessageType.SESSION_END),
    getCurrent: () => ipcRenderer.invoke(IPCMessageType.SESSION_GET_CURRENT)
  },

  // File operations
  files: {
    openDialog: (options: any) => ipcRenderer.invoke(IPCMessageType.FILE_DIALOG_OPEN, options),
    saveDialog: (options: any) => ipcRenderer.invoke(IPCMessageType.FILE_DIALOG_SAVE, options),
    read: (path: string) => ipcRenderer.invoke(IPCMessageType.FILE_READ, path),
    write: (path: string, content: string) => ipcRenderer.invoke(IPCMessageType.FILE_WRITE, path, content)
  },

  // Event listeners with type safety
  events: {
    // Hotkey events
    onHotkeyTriggered: (callback: (data: { hotkeyId: string }) => void) => {
      const listener = (_: any, data: { hotkeyId: string }) => callback(data);
      ipcRenderer.on(IPCMessageType.SYSTEM_HOTKEY_TRIGGERED, listener);
      return () => ipcRenderer.removeListener(IPCMessageType.SYSTEM_HOTKEY_TRIGGERED, listener);
    },

    // Voice events
    onVoiceTranscript: (callback: (data: { transcript: string }) => void) => {
      const listener = (_: any, data: { transcript: string }) => callback(data);
      ipcRenderer.on(IPCMessageType.VOICE_TRANSCRIPT, listener);
      return () => ipcRenderer.removeListener(IPCMessageType.VOICE_TRANSCRIPT, listener);
    },

    onVoiceError: (callback: (data: { error: string }) => void) => {
      const listener = (_: any, data: { error: string }) => callback(data);
      ipcRenderer.on(IPCMessageType.VOICE_ERROR, listener);
      return () => ipcRenderer.removeListener(IPCMessageType.VOICE_ERROR, listener);
    },

    // System events
    onSystemNotification: (callback: (notification: any) => void) => {
      const listener = (_: any, notification: any) => callback(notification);
      ipcRenderer.on(IPCMessageType.SYSTEM_NOTIFICATION, listener);
      return () => ipcRenderer.removeListener(IPCMessageType.SYSTEM_NOTIFICATION, listener);
    },

    // Data update events
    onPromptUpdated: (callback: (prompt: any) => void) => {
      const listener = (_: any, prompt: any) => callback(prompt);
      ipcRenderer.on(IPCMessageType.EVENT_PROMPT_UPDATED, listener);
      return () => ipcRenderer.removeListener(IPCMessageType.EVENT_PROMPT_UPDATED, listener);
    },

    onCategoryUpdated: (callback: (category: any) => void) => {
      const listener = (_: any, category: any) => callback(category);
      ipcRenderer.on(IPCMessageType.EVENT_CATEGORY_UPDATED, listener);
      return () => ipcRenderer.removeListener(IPCMessageType.EVENT_CATEGORY_UPDATED, listener);
    },

    onSettingsUpdated: (callback: (setting: any) => void) => {
      const listener = (_: any, setting: any) => callback(setting);
      ipcRenderer.on(IPCMessageType.EVENT_SETTINGS_UPDATED, listener);
      return () => ipcRenderer.removeListener(IPCMessageType.EVENT_SETTINGS_UPDATED, listener);
    },

    onUserUpdated: (callback: (user: any) => void) => {
      const listener = (_: any, user: any) => callback(user);
      ipcRenderer.on(IPCMessageType.EVENT_USER_UPDATED, listener);
      return () => ipcRenderer.removeListener(IPCMessageType.EVENT_USER_UPDATED, listener);
    },

    onErrorOccurred: (callback: (error: any) => void) => {
      const listener = (_: any, error: any) => callback(error);
      ipcRenderer.on(IPCMessageType.EVENT_ERROR_OCCURRED, listener);
      return () => ipcRenderer.removeListener(IPCMessageType.EVENT_ERROR_OCCURRED, listener);
    }
  },

  // Low-level IPC communication helpers (for advanced use)
  ipc: {
    invoke: (channel: string, ...args: any[]) => ipcRenderer.invoke(channel, ...args),
    send: (channel: string, ...args: any[]) => ipcRenderer.send(channel, ...args),
    on: (channel: string, callback: (...args: any[]) => void) => {
      ipcRenderer.on(channel, callback);
    },
    off: (channel: string, callback: (...args: any[]) => void) => {
      ipcRenderer.off(channel, callback);
    },
    removeAllListeners: (channel: string) => {
      ipcRenderer.removeAllListeners(channel);
    }
  }
};

// Expose the API to the renderer process
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// Type definitions for the exposed API
export type ElectronAPI = typeof electronAPI;
