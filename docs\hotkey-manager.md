# Hotkey Manager Module Specification

## 🎯 Purpose

The Hotkey Manager module provides global hotkey registration and management capabilities, enabling users to trigger application functions from anywhere in the system with customizable keyboard shortcuts.

## 📋 Responsibilities

### Hotkey Registration
- Global system hotkey registration
- Conflict detection and resolution
- Platform-specific key handling
- Dynamic hotkey updates

### Event Management
- Hotkey event capture and routing
- Action mapping and execution
- Event filtering and validation
- Error handling and recovery

### User Configuration
- Customizable hotkey assignments
- Hotkey validation and suggestions
- Import/export of hotkey configurations
- Reset to defaults functionality

## 🏗️ Architecture

### Core Hotkey Manager
```typescript
class HotkeyManager {
  private registeredHotkeys: Map<string, HotkeyRegistration> = new Map();
  private actionHandlers: Map<string, ActionHandler> = new Map();
  private conflictResolver: ConflictResolver;
  private platformAdapter: PlatformAdapter;
  
  async registerHotkey(hotkey: HotkeyDefinition): Promise<boolean>;
  async unregisterHotkey(accelerator: string): Promise<boolean>;
  async updateHotkey(oldAccelerator: string, newAccelerator: string): Promise<boolean>;
  async registerAction(actionId: string, handler: <PERSON>Hand<PERSON>): Promise<void>;
  async triggerAction(actionId: string, context?: ActionContext): Promise<void>;
  getRegisteredHotkeys(): HotkeyRegistration[];
  isHotkeyAvailable(accelerator: string): boolean;
}
```

### Hotkey Definitions
```typescript
interface HotkeyDefinition {
  accelerator: string;
  actionId: string;
  description: string;
  category: string;
  enabled: boolean;
  global: boolean;
  context?: string[];
}

interface HotkeyRegistration {
  id: string;
  definition: HotkeyDefinition;
  registeredAt: Date;
  lastTriggered?: Date;
  triggerCount: number;
  isActive: boolean;
}

interface ActionHandler {
  execute: (context?: ActionContext) => Promise<void>;
  canExecute?: (context?: ActionContext) => boolean;
  description: string;
}

interface ActionContext {
  windowId?: string;
  activeElement?: string;
  clipboardContent?: string;
  selectedText?: string;
  timestamp: Date;
}
```

## ⌨️ Default Hotkey Configuration

### System Hotkeys
```typescript
const DEFAULT_HOTKEYS: HotkeyDefinition[] = [
  {
    accelerator: 'Ctrl+Shift+P',
    actionId: 'show-floating-window',
    description: 'Show floating prompt window',
    category: 'Window Management',
    enabled: true,
    global: true
  },
  {
    accelerator: 'Ctrl+Shift+O',
    actionId: 'capture-screen',
    description: 'Capture screen and extract text',
    category: 'Context Extraction',
    enabled: true,
    global: true
  },
  {
    accelerator: 'Ctrl+Shift+V',
    actionId: 'start-voice-input',
    description: 'Start voice recording',
    category: 'Voice Input',
    enabled: true,
    global: true
  },
  {
    accelerator: 'Ctrl+Shift+L',
    actionId: 'show-prompt-library',
    description: 'Open prompt library',
    category: 'Prompt Management',
    enabled: true,
    global: true
  },
  {
    accelerator: 'Ctrl+Shift+S',
    actionId: 'quick-search',
    description: 'Quick search prompts',
    category: 'Search',
    enabled: true,
    global: true
  },
  {
    accelerator: 'Ctrl+Shift+E',
    actionId: 'enhance-clipboard',
    description: 'Enhance clipboard content',
    category: 'Prompt Enhancement',
    enabled: true,
    global: true
  },
  {
    accelerator: 'Ctrl+Shift+H',
    actionId: 'show-help',
    description: 'Show help and shortcuts',
    category: 'Help',
    enabled: true,
    global: true
  },
  {
    accelerator: 'Ctrl+Shift+Q',
    actionId: 'quit-application',
    description: 'Quit application',
    category: 'Application',
    enabled: true,
    global: true
  }
];
```

### Context-Specific Hotkeys
```typescript
const CONTEXT_HOTKEYS: HotkeyDefinition[] = [
  {
    accelerator: 'Escape',
    actionId: 'close-floating-window',
    description: 'Close floating window',
    category: 'Window Management',
    enabled: true,
    global: false,
    context: ['floating-window']
  },
  {
    accelerator: 'Ctrl+Enter',
    actionId: 'execute-prompt',
    description: 'Execute current prompt',
    category: 'Prompt Execution',
    enabled: true,
    global: false,
    context: ['prompt-editor']
  },
  {
    accelerator: 'Ctrl+S',
    actionId: 'save-prompt',
    description: 'Save current prompt',
    category: 'Prompt Management',
    enabled: true,
    global: false,
    context: ['prompt-editor']
  },
  {
    accelerator: 'F2',
    actionId: 'rename-prompt',
    description: 'Rename selected prompt',
    category: 'Prompt Management',
    enabled: true,
    global: false,
    context: ['prompt-library']
  }
];
```

## 🔧 Platform Adapters

### Windows Platform Adapter
```typescript
class WindowsPlatformAdapter implements PlatformAdapter {
  private registeredKeys: Map<string, number> = new Map();
  
  async registerGlobalHotkey(accelerator: string, callback: () => void): Promise<boolean> {
    try {
      const { key, modifiers } = this.parseAccelerator(accelerator);
      const hotkeyId = this.generateHotkeyId();
      
      // Use Windows API to register global hotkey
      const success = await this.registerWindowsHotkey(hotkeyId, modifiers, key);
      
      if (success) {
        this.registeredKeys.set(accelerator, hotkeyId);
        this.setupHotkeyListener(hotkeyId, callback);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error(`Failed to register hotkey ${accelerator}:`, error);
      return false;
    }
  }
  
  async unregisterGlobalHotkey(accelerator: string): Promise<boolean> {
    const hotkeyId = this.registeredKeys.get(accelerator);
    if (!hotkeyId) return false;
    
    try {
      await this.unregisterWindowsHotkey(hotkeyId);
      this.registeredKeys.delete(accelerator);
      return true;
    } catch (error) {
      console.error(`Failed to unregister hotkey ${accelerator}:`, error);
      return false;
    }
  }
  
  isHotkeyConflict(accelerator: string): Promise<boolean> {
    // Check for conflicts with system hotkeys
    return Promise.resolve(this.registeredKeys.has(accelerator));
  }
  
  private parseAccelerator(accelerator: string): { key: number; modifiers: number } {
    const parts = accelerator.split('+');
    let modifiers = 0;
    let key = 0;
    
    for (const part of parts) {
      switch (part.toLowerCase()) {
        case 'ctrl':
        case 'control':
          modifiers |= 0x0002; // MOD_CONTROL
          break;
        case 'alt':
          modifiers |= 0x0001; // MOD_ALT
          break;
        case 'shift':
          modifiers |= 0x0004; // MOD_SHIFT
          break;
        case 'win':
        case 'cmd':
          modifiers |= 0x0008; // MOD_WIN
          break;
        default:
          key = this.getVirtualKeyCode(part);
      }
    }
    
    return { key, modifiers };
  }
  
  private getVirtualKeyCode(keyName: string): number {
    const keyCodes: Record<string, number> = {
      'A': 0x41, 'B': 0x42, 'C': 0x43, 'D': 0x44, 'E': 0x45,
      'F': 0x46, 'G': 0x47, 'H': 0x48, 'I': 0x49, 'J': 0x4A,
      'K': 0x4B, 'L': 0x4C, 'M': 0x4D, 'N': 0x4E, 'O': 0x4F,
      'P': 0x50, 'Q': 0x51, 'R': 0x52, 'S': 0x53, 'T': 0x54,
      'U': 0x55, 'V': 0x56, 'W': 0x57, 'X': 0x58, 'Y': 0x59,
      'Z': 0x5A,
      'F1': 0x70, 'F2': 0x71, 'F3': 0x72, 'F4': 0x73,
      'F5': 0x74, 'F6': 0x75, 'F7': 0x76, 'F8': 0x77,
      'F9': 0x78, 'F10': 0x79, 'F11': 0x7A, 'F12': 0x7B,
      'Space': 0x20, 'Enter': 0x0D, 'Escape': 0x1B,
      'Tab': 0x09, 'Backspace': 0x08, 'Delete': 0x2E
    };
    
    return keyCodes[keyName.toUpperCase()] || 0;
  }
  
  private generateHotkeyId(): number {
    return Math.floor(Math.random() * 0xBFFF) + 0x0000;
  }
  
  private async registerWindowsHotkey(id: number, modifiers: number, key: number): Promise<boolean> {
    // Platform-specific implementation using Windows API
    // This would use native modules or FFI to call RegisterHotKey
    return true; // Placeholder
  }
  
  private async unregisterWindowsHotkey(id: number): Promise<boolean> {
    // Platform-specific implementation using Windows API
    // This would use native modules or FFI to call UnregisterHotKey
    return true; // Placeholder
  }
  
  private setupHotkeyListener(id: number, callback: () => void): void {
    // Setup Windows message loop listener for WM_HOTKEY messages
    // This would be implemented using native modules
  }
}
```

### macOS Platform Adapter
```typescript
class MacOSPlatformAdapter implements PlatformAdapter {
  private carbonHotkeys: Map<string, any> = new Map();
  
  async registerGlobalHotkey(accelerator: string, callback: () => void): Promise<boolean> {
    try {
      // Use Carbon API or Cocoa for macOS hotkey registration
      const hotkey = await this.registerCarbonHotkey(accelerator, callback);
      this.carbonHotkeys.set(accelerator, hotkey);
      return true;
    } catch (error) {
      console.error(`Failed to register macOS hotkey ${accelerator}:`, error);
      return false;
    }
  }
  
  async unregisterGlobalHotkey(accelerator: string): Promise<boolean> {
    const hotkey = this.carbonHotkeys.get(accelerator);
    if (!hotkey) return false;
    
    try {
      await this.unregisterCarbonHotkey(hotkey);
      this.carbonHotkeys.delete(accelerator);
      return true;
    } catch (error) {
      console.error(`Failed to unregister macOS hotkey ${accelerator}:`, error);
      return false;
    }
  }
  
  async isHotkeyConflict(accelerator: string): Promise<boolean> {
    // Check for conflicts with macOS system shortcuts
    return this.carbonHotkeys.has(accelerator);
  }
  
  private async registerCarbonHotkey(accelerator: string, callback: () => void): Promise<any> {
    // Implementation using Carbon API or node-global-key-listener
    return {}; // Placeholder
  }
  
  private async unregisterCarbonHotkey(hotkey: any): Promise<void> {
    // Implementation to unregister Carbon hotkey
  }
}
```

## 🔍 Conflict Resolution

### Conflict Resolver
```typescript
class ConflictResolver {
  private systemHotkeys: Set<string> = new Set();
  private applicationHotkeys: Map<string, string> = new Map();
  
  constructor() {
    this.loadSystemHotkeys();
  }
  
  async checkConflicts(accelerator: string): Promise<ConflictResult> {
    const conflicts: ConflictInfo[] = [];
    
    // Check system conflicts
    if (this.systemHotkeys.has(accelerator)) {
      conflicts.push({
        type: 'system',
        description: 'Conflicts with system hotkey',
        severity: 'high'
      });
    }
    
    // Check application conflicts
    const existingAction = this.applicationHotkeys.get(accelerator);
    if (existingAction) {
      conflicts.push({
        type: 'application',
        description: `Already assigned to: ${existingAction}`,
        severity: 'medium'
      });
    }
    
    // Check running applications
    const runningAppConflicts = await this.checkRunningApplications(accelerator);
    conflicts.push(...runningAppConflicts);
    
    return {
      hasConflicts: conflicts.length > 0,
      conflicts,
      suggestions: this.generateSuggestions(accelerator, conflicts)
    };
  }
  
  generateSuggestions(accelerator: string, conflicts: ConflictInfo[]): string[] {
    const suggestions: string[] = [];
    const parts = accelerator.split('+');
    
    // Try different modifier combinations
    const modifiers = ['Ctrl', 'Alt', 'Shift', 'Ctrl+Shift', 'Ctrl+Alt', 'Alt+Shift'];
    const key = parts[parts.length - 1];
    
    for (const modifier of modifiers) {
      const suggestion = `${modifier}+${key}`;
      if (!this.systemHotkeys.has(suggestion) && !this.applicationHotkeys.has(suggestion)) {
        suggestions.push(suggestion);
      }
    }
    
    // Try different keys with same modifiers
    const modifierPart = parts.slice(0, -1).join('+');
    const alternativeKeys = ['F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12'];
    
    for (const altKey of alternativeKeys) {
      const suggestion = modifierPart ? `${modifierPart}+${altKey}` : altKey;
      if (!this.systemHotkeys.has(suggestion) && !this.applicationHotkeys.has(suggestion)) {
        suggestions.push(suggestion);
      }
    }
    
    return suggestions.slice(0, 5); // Return top 5 suggestions
  }
  
  private loadSystemHotkeys(): void {
    // Load platform-specific system hotkeys
    if (process.platform === 'win32') {
      this.systemHotkeys = new Set([
        'Ctrl+C', 'Ctrl+V', 'Ctrl+X', 'Ctrl+Z', 'Ctrl+Y',
        'Ctrl+A', 'Ctrl+S', 'Ctrl+O', 'Ctrl+N', 'Ctrl+P',
        'Alt+Tab', 'Alt+F4', 'Win+L', 'Win+R', 'Win+D'
      ]);
    } else if (process.platform === 'darwin') {
      this.systemHotkeys = new Set([
        'Cmd+C', 'Cmd+V', 'Cmd+X', 'Cmd+Z', 'Cmd+Y',
        'Cmd+A', 'Cmd+S', 'Cmd+O', 'Cmd+N', 'Cmd+P',
        'Cmd+Tab', 'Cmd+Q', 'Cmd+W', 'Cmd+Space'
      ]);
    }
  }
  
  private async checkRunningApplications(accelerator: string): Promise<ConflictInfo[]> {
    // Check if other applications are using this hotkey
    // This would require platform-specific implementation
    return [];
  }
}

interface ConflictResult {
  hasConflicts: boolean;
  conflicts: ConflictInfo[];
  suggestions: string[];
}

interface ConflictInfo {
  type: 'system' | 'application' | 'external';
  description: string;
  severity: 'low' | 'medium' | 'high';
}
```

## 📊 Usage Analytics

### Hotkey Analytics
```typescript
class HotkeyAnalytics {
  private usageStats: Map<string, HotkeyUsageStats> = new Map();
  
  recordHotkeyUsage(accelerator: string, actionId: string): void {
    const stats = this.usageStats.get(accelerator) || {
      accelerator,
      actionId,
      totalUsage: 0,
      lastUsed: new Date(),
      usageHistory: []
    };
    
    stats.totalUsage++;
    stats.lastUsed = new Date();
    stats.usageHistory.push({
      timestamp: new Date(),
      context: this.getCurrentContext()
    });
    
    // Keep only last 100 usage records
    if (stats.usageHistory.length > 100) {
      stats.usageHistory = stats.usageHistory.slice(-100);
    }
    
    this.usageStats.set(accelerator, stats);
  }
  
  getUsageStats(accelerator: string): HotkeyUsageStats | null {
    return this.usageStats.get(accelerator) || null;
  }
  
  getMostUsedHotkeys(limit: number = 10): HotkeyUsageStats[] {
    return Array.from(this.usageStats.values())
      .sort((a, b) => b.totalUsage - a.totalUsage)
      .slice(0, limit);
  }
  
  getUnusedHotkeys(): string[] {
    return Array.from(this.usageStats.entries())
      .filter(([_, stats]) => stats.totalUsage === 0)
      .map(([accelerator]) => accelerator);
  }
  
  private getCurrentContext(): string {
    // Get current application context
    return 'general';
  }
}

interface HotkeyUsageStats {
  accelerator: string;
  actionId: string;
  totalUsage: number;
  lastUsed: Date;
  usageHistory: UsageHistoryEntry[];
}

interface UsageHistoryEntry {
  timestamp: Date;
  context: string;
}
```

## 🧪 Testing Strategy

### Unit Tests
```typescript
describe('HotkeyManager', () => {
  let hotkeyManager: HotkeyManager;
  let mockPlatformAdapter: jest.Mocked<PlatformAdapter>;
  
  beforeEach(() => {
    mockPlatformAdapter = {
      registerGlobalHotkey: jest.fn(),
      unregisterGlobalHotkey: jest.fn(),
      isHotkeyConflict: jest.fn()
    };
    
    hotkeyManager = new HotkeyManager(mockPlatformAdapter);
  });
  
  test('should register hotkey successfully', async () => {
    mockPlatformAdapter.registerGlobalHotkey.mockResolvedValue(true);
    
    const hotkey: HotkeyDefinition = {
      accelerator: 'Ctrl+Shift+T',
      actionId: 'test-action',
      description: 'Test hotkey',
      category: 'Test',
      enabled: true,
      global: true
    };
    
    const result = await hotkeyManager.registerHotkey(hotkey);
    
    expect(result).toBe(true);
    expect(mockPlatformAdapter.registerGlobalHotkey).toHaveBeenCalledWith(
      'Ctrl+Shift+T',
      expect.any(Function)
    );
  });
  
  test('should detect hotkey conflicts', async () => {
    const conflictResolver = new ConflictResolver();
    const result = await conflictResolver.checkConflicts('Ctrl+C');
    
    expect(result.hasConflicts).toBe(true);
    expect(result.conflicts).toHaveLength(1);
    expect(result.conflicts[0].type).toBe('system');
  });
});
```
