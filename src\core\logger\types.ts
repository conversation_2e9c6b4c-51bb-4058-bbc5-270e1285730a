/**
 * Logger module type definitions
 * Provides comprehensive logging capabilities with structured logging,
 * multiple output targets, and performance monitoring
 */

export enum LogLevel {
  TRACE = 0,
  DEBUG = 1,
  INFO = 2,
  WARN = 3,
  ERROR = 4,
  FATAL = 5
}

export interface LogContext {
  module?: string;
  component?: string;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  [key: string]: any;
}

export interface LogMetadata {
  tags?: string[];
  duration?: number;
  memoryUsage?: NodeJS.MemoryUsage;
  [key: string]: any;
}

export interface ErrorInfo {
  name: string;
  message: string;
  stack?: string;
  code?: string | number;
  cause?: ErrorInfo;
}

export interface PerformanceInfo {
  startTime: number;
  endTime: number;
  duration: number;
  memoryBefore?: NodeJS.MemoryUsage;
  memoryAfter?: NodeJS.MemoryUsage;
}

export interface LogEntry {
  timestamp: Date;
  level: LogLevel;
  message: string;
  context: LogContext;
  metadata?: LogMetadata;
  error?: ErrorInfo;
  performance?: PerformanceInfo;
}

export interface LogTransport {
  write(entry: LogEntry): Promise<void>;
  close?(): Promise<void>;
}

export interface LogFilter {
  shouldLog(entry: LogEntry): boolean;
}

export interface LogFormatter {
  format(entry: LogEntry): string;
}

export interface FileTransportOptions {
  filePath: string;
  maxFileSize?: number;
  maxFiles?: number;
  format?: LogFormatter;
}

export interface ConsoleTransportOptions {
  colorize?: boolean;
  formatter?: LogFormatter;
}

export interface RemoteTransportOptions {
  endpoint: string;
  apiKey: string;
  batchSize?: number;
  flushInterval?: number;
}

export interface LoggerConfig {
  level: LogLevel;
  transports: LogTransport[];
  filters: LogFilter[];
}

export interface LoggerOptions {
  level?: LogLevel;
  context?: LogContext;
  transports?: LogTransport[];
  filters?: LogFilter[];
}
