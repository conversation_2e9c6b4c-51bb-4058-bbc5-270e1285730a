/**
 * Window Text Extractor Service
 * Extracts text content from active windows and web pages
 */

import { <PERSON>rowserWindow, WebContents } from 'electron';
import { 
  IWindowTextExtractor, 
  WindowContent, 
  WindowInfo,
  ContextExtractionError,
  ContextErrorCode
} from './types';
import { logger } from '../../core/logger';

export class WindowTextExtractor implements IWindowTextExtractor {
  private readonly maxTextLength = 50000; // Limit extracted text length
  private readonly extractionTimeout = 10000; // 10 seconds timeout

  constructor() {
    logger.debug('WindowTextExtractor initialized');
  }

  /**
   * Get text from the currently active window
   */
  async getActiveWindowText(): Promise<WindowContent> {
    try {
      const activeWindow = BrowserWindow.getFocusedWindow();
      
      if (!activeWindow) {
        throw new Error('No active window found');
      }

      logger.debug('Extracting text from active window', { 
        windowId: activeWindow.id,
        title: activeWindow.getTitle() 
      });

      return await this.extractWindowContent(activeWindow);
    } catch (error) {
      logger.error('Failed to get active window text', { 
        error: error instanceof Error ? error.message : String(error) 
      });
      
      throw new ContextExtractionError(
        'Failed to extract active window text',
        ContextErrorCode.WINDOW_ACCESS_DENIED,
        'WindowTextExtractor',
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Get text from a specific window by ID
   */
  async getWindowText(windowId: number): Promise<WindowContent> {
    try {
      const window = BrowserWindow.fromId(windowId);
      
      if (!window) {
        throw new Error(`Window with ID ${windowId} not found`);
      }

      logger.debug('Extracting text from specific window', { windowId });
      return await this.extractWindowContent(window);
    } catch (error) {
      logger.error('Failed to get window text', { 
        windowId, 
        error: error instanceof Error ? error.message : String(error) 
      });
      
      throw new ContextExtractionError(
        `Failed to extract text from window ${windowId}`,
        ContextErrorCode.WINDOW_ACCESS_DENIED,
        'WindowTextExtractor',
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Get information about all available windows
   */
  async getAllWindows(): Promise<WindowInfo[]> {
    try {
      const windows = BrowserWindow.getAllWindows();
      
      return windows.map(window => ({
        title: window.getTitle(),
        processName: 'electron', // All Electron windows
        pid: process.pid,
        bounds: window.getBounds()
      }));
    } catch (error) {
      logger.error('Failed to get all windows', { 
        error: error instanceof Error ? error.message : String(error) 
      });
      return [];
    }
  }

  /**
   * Check if a window is accessible for text extraction
   */
  async isWindowAccessible(windowId: number): Promise<boolean> {
    try {
      const window = BrowserWindow.fromId(windowId);
      return window !== null && !window.isDestroyed();
    } catch (error) {
      logger.debug('Window accessibility check failed', { windowId, error });
      return false;
    }
  }

  /**
   * Extract content from a browser window
   */
  private async extractWindowContent(window: BrowserWindow): Promise<WindowContent> {
    const windowInfo: WindowInfo = {
      title: window.getTitle(),
      processName: 'electron',
      pid: process.pid,
      bounds: window.getBounds()
    };

    try {
      let extractedText = '';
      let extractionMethod: 'web' | 'native' | 'accessibility' = 'native';
      let confidence = 0.5;

      // Try to extract web content if available
      if (window.webContents && !window.webContents.isDestroyed()) {
        try {
          extractedText = await this.extractWebContent(window.webContents);
          extractionMethod = 'web';
          confidence = 0.9;
        } catch (error) {
          logger.debug('Web content extraction failed, falling back to native', { 
            windowId: window.id, 
            error 
          });
        }
      }

      // Fallback to native window text extraction
      if (!extractedText) {
        extractedText = await this.extractNativeWindowText(window);
        extractionMethod = 'native';
        confidence = 0.3;
      }

      // Limit text length
      if (extractedText.length > this.maxTextLength) {
        extractedText = extractedText.substring(0, this.maxTextLength) + '... [truncated]';
      }

      const windowContent: WindowContent = {
        info: windowInfo,
        text: extractedText,
        metadata: {
          extractionMethod,
          timestamp: new Date(),
          confidence
        }
      };

      // Try to get URL if it's a web page
      if (window.webContents && !window.webContents.isDestroyed()) {
        try {
          windowContent.url = window.webContents.getURL();
        } catch (error) {
          logger.debug('Failed to get window URL', { windowId: window.id, error });
        }
      }

      logger.debug('Window content extracted successfully', {
        windowId: window.id,
        textLength: extractedText.length,
        extractionMethod,
        confidence
      });

      return windowContent;
    } catch (error) {
      logger.error('Window content extraction failed', { 
        windowId: window.id, 
        error: error instanceof Error ? error.message : String(error) 
      });
      
      // Return minimal content with error info
      return {
        info: windowInfo,
        text: `Error extracting window content: ${error instanceof Error ? error.message : String(error)}`,
        metadata: {
          extractionMethod: 'native',
          timestamp: new Date(),
          confidence: 0
        }
      };
    }
  }

  /**
   * Extract text from web content
   */
  private async extractWebContent(webContents: WebContents): Promise<string> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Web content extraction timeout'));
      }, this.extractionTimeout);

      webContents.executeJavaScript(`
        (function() {
          try {
            // Extract visible text from the page
            function extractVisibleText() {
              const walker = document.createTreeWalker(
                document.body || document.documentElement,
                NodeFilter.SHOW_TEXT,
                {
                  acceptNode: function(node) {
                    const parent = node.parentElement;
                    if (!parent) return NodeFilter.FILTER_REJECT;
                    
                    const style = window.getComputedStyle(parent);
                    if (style.display === 'none' || 
                        style.visibility === 'hidden' ||
                        style.opacity === '0' ||
                        parent.tagName === 'SCRIPT' ||
                        parent.tagName === 'STYLE') {
                      return NodeFilter.FILTER_REJECT;
                    }
                    
                    return NodeFilter.FILTER_ACCEPT;
                  }
                }
              );
              
              let text = '';
              let node;
              while (node = walker.nextNode()) {
                const textContent = node.textContent.trim();
                if (textContent) {
                  text += textContent + ' ';
                }
              }
              
              return text.trim();
            }
            
            // Get page title and URL
            const title = document.title || '';
            const url = window.location.href || '';
            const bodyText = extractVisibleText();
            
            // Combine title and body text
            let fullText = '';
            if (title) {
              fullText += 'Title: ' + title + '\\n\\n';
            }
            if (url && url !== 'about:blank') {
              fullText += 'URL: ' + url + '\\n\\n';
            }
            if (bodyText) {
              fullText += 'Content: ' + bodyText;
            }
            
            return fullText || 'No visible text content found';
          } catch (error) {
            return 'Error extracting web content: ' + error.message;
          }
        })();
      `).then(result => {
        clearTimeout(timeout);
        resolve(result || '');
      }).catch(error => {
        clearTimeout(timeout);
        reject(error);
      });
    });
  }

  /**
   * Extract text from native window (fallback method)
   */
  private async extractNativeWindowText(window: BrowserWindow): Promise<string> {
    // For native windows, we can only get basic information
    const title = window.getTitle();
    const bounds = window.getBounds();
    
    let text = '';
    if (title) {
      text += `Window Title: ${title}\n`;
    }
    
    text += `Window Size: ${bounds.width}x${bounds.height}\n`;
    text += `Window Position: (${bounds.x}, ${bounds.y})\n`;
    
    // Check if window is visible
    if (window.isVisible()) {
      text += 'Window Status: Visible\n';
    } else {
      text += 'Window Status: Hidden\n';
    }
    
    // Check if window is focused
    if (window.isFocused()) {
      text += 'Window Focus: Active\n';
    }
    
    return text || 'No text content available from native window';
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    logger.debug('WindowTextExtractor destroyed');
  }
}
