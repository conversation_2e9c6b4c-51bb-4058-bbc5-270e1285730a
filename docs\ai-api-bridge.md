# AI API Bridge Module Specification

## 🎯 Purpose

The AI API Bridge module provides a unified interface for integrating with various AI services, offering OpenAI-compatible endpoints with extensible provider support, request/response handling, and intelligent routing capabilities.

## 📋 Responsibilities

### API Integration
- OpenAI-compatible API interface
- Multiple AI provider support
- Request/response transformation
- Error handling and retry logic

### Provider Management
- Dynamic provider switching
- Load balancing and failover
- Rate limiting and quota management
- Provider-specific optimizations

### Request Processing
- Request validation and sanitization
- Response caching and optimization
- Streaming response handling
- Context management

## 🏗️ Architecture

### Core Bridge
```typescript
class AIAPIBridge {
  private providers: Map<string, AIProvider> = new Map();
  private router: ProviderRouter;
  private cache: ResponseCache;
  private rateLimiter: RateLimiter;
  
  async complete(request: CompletionRequest): Promise<CompletionResponse>;
  async stream(request: CompletionRequest): Promise<AsyncIterable<CompletionChunk>>;
  async enhance(text: string, options?: EnhancementOptions): Promise<string>;
  async summarize(text: string, options?: SummarizationOptions): Promise<string>;
  async translate(text: string, targetLanguage: string): Promise<string>;
  
  registerProvider(name: string, provider: AIProvider): void;
  setDefaultProvider(name: string): void;
  getProviderStatus(): ProviderStatus[];
}
```

### OpenAI-Compatible Interface (Placeholder Structure)
```typescript
interface CompletionRequest {
  model: string;
  messages: ChatMessage[];
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stop?: string | string[];
  stream?: boolean;
  user?: string;
}

interface CompletionResponse {
  id: string;
  object: 'chat.completion';
  created: number;
  model: string;
  choices: CompletionChoice[];
  usage: TokenUsage;
}

interface CompletionChoice {
  index: number;
  message: ChatMessage;
  finish_reason: 'stop' | 'length' | 'content_filter' | 'tool_calls';
}

interface ChatMessage {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string;
  name?: string;
  tool_calls?: ToolCall[];
  tool_call_id?: string;
}

interface TokenUsage {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
}
```

## 🔌 Provider Interface

### AI Provider Interface
```typescript
interface AIProvider {
  name: string;
  config: ProviderConfig;
  
  complete(request: CompletionRequest): Promise<CompletionResponse>;
  stream(request: CompletionRequest): Promise<AsyncIterable<CompletionChunk>>;
  getModels(): Promise<ModelInfo[]>;
  validateConfig(): Promise<boolean>;
  getUsage(): Promise<UsageInfo>;
}

interface ProviderConfig {
  apiKey: string;
  baseURL: string;
  defaultModel: string;
  maxTokens: number;
  timeout: number;
  retryAttempts: number;
  rateLimits: {
    requestsPerMinute: number;
    tokensPerMinute: number;
  };
}

interface ModelInfo {
  id: string;
  name: string;
  description: string;
  contextLength: number;
  inputCost: number; // per 1K tokens
  outputCost: number; // per 1K tokens
  capabilities: string[];
}
```

### OpenAI Provider Implementation (Placeholder)
```typescript
class OpenAIProvider implements AIProvider {
  name = 'openai';
  config: ProviderConfig;
  
  constructor(config: ProviderConfig) {
    this.config = {
      baseURL: 'https://api.openai.com/v1',
      defaultModel: 'gpt-3.5-turbo',
      maxTokens: 4096,
      timeout: 30000,
      retryAttempts: 3,
      ...config
    };
  }
  
  async complete(request: CompletionRequest): Promise<CompletionResponse> {
    const url = `${this.config.baseURL}/chat/completions`;
    
    const payload = {
      model: request.model || this.config.defaultModel,
      messages: request.messages,
      temperature: request.temperature ?? 0.7,
      max_tokens: request.max_tokens ?? this.config.maxTokens,
      top_p: request.top_p,
      frequency_penalty: request.frequency_penalty,
      presence_penalty: request.presence_penalty,
      stop: request.stop,
      user: request.user
    };
    
    try {
      const response = await this.makeRequest(url, payload);
      return this.transformResponse(response);
    } catch (error) {
      throw new AIProviderError(`OpenAI API request failed: ${error.message}`, error);
    }
  }
  
  async *stream(request: CompletionRequest): AsyncIterable<CompletionChunk> {
    const url = `${this.config.baseURL}/chat/completions`;
    
    const payload = {
      ...request,
      stream: true,
      model: request.model || this.config.defaultModel
    };
    
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }
      
      const decoder = new TextDecoder();
      let buffer = '';
      
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') return;
              
              try {
                const chunk = JSON.parse(data);
                yield this.transformStreamChunk(chunk);
              } catch (error) {
                console.warn('Failed to parse stream chunk:', error);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      throw new AIProviderError(`OpenAI streaming failed: ${error.message}`, error);
    }
  }
  
  async getModels(): Promise<ModelInfo[]> {
    // Placeholder implementation - to be supplemented
    return [
      {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        description: 'Fast and efficient model for most tasks',
        contextLength: 4096,
        inputCost: 0.0015,
        outputCost: 0.002,
        capabilities: ['chat', 'completion']
      },
      {
        id: 'gpt-4',
        name: 'GPT-4',
        description: 'Most capable model for complex tasks',
        contextLength: 8192,
        inputCost: 0.03,
        outputCost: 0.06,
        capabilities: ['chat', 'completion', 'reasoning']
      }
    ];
  }
  
  async validateConfig(): Promise<boolean> {
    try {
      const response = await this.makeRequest(`${this.config.baseURL}/models`, null, 'GET');
      return response && Array.isArray(response.data);
    } catch {
      return false;
    }
  }
  
  async getUsage(): Promise<UsageInfo> {
    // Placeholder implementation - would fetch actual usage data
    return {
      requestsToday: 0,
      tokensToday: 0,
      costToday: 0,
      remainingQuota: 1000000
    };
  }
  
  private async makeRequest(url: string, data: any, method: string = 'POST'): Promise<any> {
    const options: RequestInit = {
      method,
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json'
      }
    };
    
    if (data && method !== 'GET') {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(url, options);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error?.message || `HTTP ${response.status}`);
    }
    
    return response.json();
  }
  
  private transformResponse(response: any): CompletionResponse {
    return {
      id: response.id,
      object: 'chat.completion',
      created: response.created,
      model: response.model,
      choices: response.choices.map((choice: any) => ({
        index: choice.index,
        message: choice.message,
        finish_reason: choice.finish_reason
      })),
      usage: response.usage
    };
  }
  
  private transformStreamChunk(chunk: any): CompletionChunk {
    return {
      id: chunk.id,
      object: 'chat.completion.chunk',
      created: chunk.created,
      model: chunk.model,
      choices: chunk.choices.map((choice: any) => ({
        index: choice.index,
        delta: choice.delta,
        finish_reason: choice.finish_reason
      }))
    };
  }
}
```

## 🔄 Provider Router

### Intelligent Routing
```typescript
class ProviderRouter {
  private providers: Map<string, AIProvider> = new Map();
  private defaultProvider: string = 'openai';
  private routingRules: RoutingRule[] = [];
  
  async route(request: CompletionRequest): Promise<string> {
    // Apply routing rules
    for (const rule of this.routingRules) {
      if (rule.condition(request)) {
        return rule.provider;
      }
    }
    
    // Fallback to default provider
    return this.defaultProvider;
  }
  
  addRoutingRule(rule: RoutingRule): void {
    this.routingRules.push(rule);
  }
  
  setLoadBalancing(providers: string[], strategy: LoadBalancingStrategy): void {
    this.routingRules.unshift({
      condition: () => true,
      provider: this.selectProvider(providers, strategy),
      priority: 0
    });
  }
  
  private selectProvider(providers: string[], strategy: LoadBalancingStrategy): string {
    switch (strategy) {
      case 'round-robin':
        return this.roundRobinSelection(providers);
      case 'least-loaded':
        return this.leastLoadedSelection(providers);
      case 'fastest':
        return this.fastestProviderSelection(providers);
      default:
        return providers[0];
    }
  }
  
  private roundRobinSelection(providers: string[]): string {
    // Implementation for round-robin selection
    return providers[0];
  }
  
  private leastLoadedSelection(providers: string[]): string {
    // Implementation for least-loaded selection
    return providers[0];
  }
  
  private fastestProviderSelection(providers: string[]): string {
    // Implementation for fastest provider selection
    return providers[0];
  }
}

interface RoutingRule {
  condition: (request: CompletionRequest) => boolean;
  provider: string;
  priority: number;
}

type LoadBalancingStrategy = 'round-robin' | 'least-loaded' | 'fastest' | 'random';
```

## 📊 Rate Limiting

### Rate Limiter
```typescript
class RateLimiter {
  private limits: Map<string, RateLimit> = new Map();
  private usage: Map<string, UsageTracker> = new Map();
  
  async checkLimit(provider: string, tokens: number): Promise<boolean> {
    const limit = this.limits.get(provider);
    if (!limit) return true;
    
    const tracker = this.getUsageTracker(provider);
    const now = Date.now();
    
    // Clean old entries
    tracker.requests = tracker.requests.filter(time => now - time < 60000);
    tracker.tokens = tracker.tokens.filter(entry => now - entry.time < 60000);
    
    // Check request limit
    if (tracker.requests.length >= limit.requestsPerMinute) {
      return false;
    }
    
    // Check token limit
    const tokenUsage = tracker.tokens.reduce((sum, entry) => sum + entry.count, 0);
    if (tokenUsage + tokens > limit.tokensPerMinute) {
      return false;
    }
    
    return true;
  }
  
  recordUsage(provider: string, tokens: number): void {
    const tracker = this.getUsageTracker(provider);
    const now = Date.now();
    
    tracker.requests.push(now);
    tracker.tokens.push({ time: now, count: tokens });
  }
  
  setLimit(provider: string, limit: RateLimit): void {
    this.limits.set(provider, limit);
  }
  
  private getUsageTracker(provider: string): UsageTracker {
    if (!this.usage.has(provider)) {
      this.usage.set(provider, { requests: [], tokens: [] });
    }
    return this.usage.get(provider)!;
  }
}

interface RateLimit {
  requestsPerMinute: number;
  tokensPerMinute: number;
}

interface UsageTracker {
  requests: number[];
  tokens: { time: number; count: number }[];
}
```

## 💾 Response Caching

### Cache Implementation
```typescript
class ResponseCache {
  private cache: Map<string, CacheEntry> = new Map();
  private maxSize = 1000;
  private defaultTTL = 3600000; // 1 hour
  
  get(key: string): CompletionResponse | null {
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.response;
  }
  
  set(key: string, response: CompletionResponse, ttl?: number): void {
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      response,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL
    });
  }
  
  generateKey(request: CompletionRequest): string {
    const keyData = {
      model: request.model,
      messages: request.messages,
      temperature: request.temperature,
      max_tokens: request.max_tokens
    };
    
    return crypto
      .createHash('sha256')
      .update(JSON.stringify(keyData))
      .digest('hex');
  }
  
  clear(): void {
    this.cache.clear();
  }
}

interface CacheEntry {
  response: CompletionResponse;
  timestamp: number;
  ttl: number;
}
```

## 🔧 Error Handling

### Error Types and Recovery
```typescript
class AIProviderError extends Error {
  constructor(
    message: string,
    public originalError?: Error,
    public provider?: string,
    public retryable: boolean = true
  ) {
    super(message);
    this.name = 'AIProviderError';
  }
}

class ErrorHandler {
  async handleError(error: AIProviderError, request: CompletionRequest): Promise<CompletionResponse> {
    if (error.retryable) {
      return this.retryWithBackoff(request, error.provider);
    }
    
    // Try fallback provider
    const fallbackProvider = this.getFallbackProvider(error.provider);
    if (fallbackProvider) {
      return this.routeToProvider(request, fallbackProvider);
    }
    
    throw error;
  }
  
  private async retryWithBackoff(
    request: CompletionRequest, 
    provider?: string, 
    attempt: number = 1
  ): Promise<CompletionResponse> {
    const maxAttempts = 3;
    const baseDelay = 1000;
    
    if (attempt > maxAttempts) {
      throw new AIProviderError('Max retry attempts exceeded');
    }
    
    const delay = baseDelay * Math.pow(2, attempt - 1);
    await new Promise(resolve => setTimeout(resolve, delay));
    
    try {
      return await this.routeToProvider(request, provider);
    } catch (error) {
      return this.retryWithBackoff(request, provider, attempt + 1);
    }
  }
  
  private getFallbackProvider(failedProvider?: string): string | null {
    const fallbackMap: Record<string, string> = {
      'openai': 'anthropic',
      'anthropic': 'openai',
      'local': 'openai'
    };
    
    return fallbackMap[failedProvider || ''] || null;
  }
  
  private async routeToProvider(request: CompletionRequest, provider?: string): Promise<CompletionResponse> {
    // Implementation would route to specific provider
    throw new Error('Not implemented');
  }
}
```

## 🧪 Testing Strategy

### Unit Tests
```typescript
describe('AIAPIBridge', () => {
  let bridge: AIAPIBridge;
  let mockProvider: jest.Mocked<AIProvider>;
  
  beforeEach(() => {
    mockProvider = {
      name: 'test-provider',
      config: {} as ProviderConfig,
      complete: jest.fn(),
      stream: jest.fn(),
      getModels: jest.fn(),
      validateConfig: jest.fn(),
      getUsage: jest.fn()
    };
    
    bridge = new AIAPIBridge();
    bridge.registerProvider('test', mockProvider);
    bridge.setDefaultProvider('test');
  });
  
  test('should complete request successfully', async () => {
    const request: CompletionRequest = {
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: 'Hello' }]
    };
    
    const expectedResponse: CompletionResponse = {
      id: 'test-id',
      object: 'chat.completion',
      created: Date.now(),
      model: 'gpt-3.5-turbo',
      choices: [{
        index: 0,
        message: { role: 'assistant', content: 'Hello!' },
        finish_reason: 'stop'
      }],
      usage: { prompt_tokens: 5, completion_tokens: 3, total_tokens: 8 }
    };
    
    mockProvider.complete.mockResolvedValue(expectedResponse);
    
    const result = await bridge.complete(request);
    
    expect(result).toEqual(expectedResponse);
    expect(mockProvider.complete).toHaveBeenCalledWith(request);
  });
});
```
