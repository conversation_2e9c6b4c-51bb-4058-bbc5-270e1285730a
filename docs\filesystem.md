# File System Module Specification

## 🎯 Purpose

The File System module provides cross-platform file operations, path management, and file monitoring capabilities for the PromptPilot Desktop application with security, performance, and reliability considerations.

## 📋 Responsibilities

### File Operations
- Cross-platform file and directory operations
- Atomic file operations and transactions
- File metadata management
- Temporary file handling

### Path Management
- Platform-agnostic path resolution
- User directory detection
- Application data directory management
- Relative and absolute path handling

### File Monitoring
- File system change detection
- Directory watching and notifications
- Debounced change events
- Recursive monitoring support

## 🏗️ Architecture

### Core File System Manager
```typescript
class FileSystemManager {
  private watchers: Map<string, FileWatcher> = new Map();
  private tempFiles: Set<string> = new Set();
  private pathResolver: PathResolver;
  
  async readFile(filePath: string, options?: ReadOptions): Promise<string | Buffer>;
  async writeFile(filePath: string, data: string | Buffer, options?: WriteOptions): Promise<void>;
  async appendFile(filePath: string, data: string | Buffer): Promise<void>;
  async deleteFile(filePath: string, secure?: boolean): Promise<void>;
  async copyFile(source: string, destination: string): Promise<void>;
  async moveFile(source: string, destination: string): Promise<void>;
  
  async createDirectory(dirPath: string, recursive?: boolean): Promise<void>;
  async deleteDirectory(dirPath: string, recursive?: boolean): Promise<void>;
  async listDirectory(dirPath: string, options?: ListOptions): Promise<FileInfo[]>;
  
  async exists(path: string): Promise<boolean>;
  async getStats(path: string): Promise<FileStats>;
  async getPermissions(path: string): Promise<FilePermissions>;
  async setPermissions(path: string, permissions: FilePermissions): Promise<void>;
  
  watchFile(filePath: string, callback: FileChangeCallback): FileWatcher;
  watchDirectory(dirPath: string, callback: DirectoryChangeCallback, recursive?: boolean): FileWatcher;
  
  createTempFile(prefix?: string, suffix?: string): Promise<string>;
  createTempDirectory(prefix?: string): Promise<string>;
  cleanupTempFiles(): Promise<void>;
}
```

### File System Types
```typescript
interface ReadOptions {
  encoding?: BufferEncoding;
  flag?: string;
}

interface WriteOptions {
  encoding?: BufferEncoding;
  mode?: number;
  flag?: string;
  atomic?: boolean;
}

interface ListOptions {
  recursive?: boolean;
  includeHidden?: boolean;
  filter?: (file: FileInfo) => boolean;
  sort?: 'name' | 'size' | 'modified' | 'created';
  order?: 'asc' | 'desc';
}

interface FileInfo {
  name: string;
  path: string;
  size: number;
  isDirectory: boolean;
  isFile: boolean;
  isSymlink: boolean;
  permissions: FilePermissions;
  created: Date;
  modified: Date;
  accessed: Date;
}

interface FileStats {
  size: number;
  blocks: number;
  blockSize: number;
  isDirectory: boolean;
  isFile: boolean;
  isSymlink: boolean;
  created: Date;
  modified: Date;
  accessed: Date;
  permissions: FilePermissions;
}

interface FilePermissions {
  readable: boolean;
  writable: boolean;
  executable: boolean;
  mode: number;
}
```

## 📁 Cross-Platform Operations

### Platform-Agnostic File Operations
```typescript
class CrossPlatformFileOperations {
  async readFile(filePath: string, options: ReadOptions = {}): Promise<string | Buffer> {
    try {
      const resolvedPath = this.resolvePath(filePath);
      
      if (options.encoding) {
        return await fs.readFile(resolvedPath, options.encoding);
      } else {
        return await fs.readFile(resolvedPath);
      }
    } catch (error) {
      throw new FileSystemError(`Failed to read file: ${filePath}`, error);
    }
  }
  
  async writeFile(filePath: string, data: string | Buffer, options: WriteOptions = {}): Promise<void> {
    try {
      const resolvedPath = this.resolvePath(filePath);
      
      // Ensure directory exists
      await this.ensureDirectoryExists(path.dirname(resolvedPath));
      
      if (options.atomic) {
        await this.atomicWrite(resolvedPath, data, options);
      } else {
        await fs.writeFile(resolvedPath, data, options);
      }
    } catch (error) {
      throw new FileSystemError(`Failed to write file: ${filePath}`, error);
    }
  }
  
  async atomicWrite(filePath: string, data: string | Buffer, options: WriteOptions = {}): Promise<void> {
    const tempPath = `${filePath}.tmp.${Date.now()}`;
    
    try {
      // Write to temporary file first
      await fs.writeFile(tempPath, data, options);
      
      // Atomic rename
      await fs.rename(tempPath, filePath);
    } catch (error) {
      // Clean up temporary file on error
      await fs.unlink(tempPath).catch(() => {});
      throw error;
    }
  }
  
  async deleteFile(filePath: string, secure: boolean = false): Promise<void> {
    try {
      const resolvedPath = this.resolvePath(filePath);
      
      if (secure) {
        await this.secureDelete(resolvedPath);
      } else {
        await fs.unlink(resolvedPath);
      }
    } catch (error) {
      if (error.code !== 'ENOENT') {
        throw new FileSystemError(`Failed to delete file: ${filePath}`, error);
      }
    }
  }
  
  async secureDelete(filePath: string): Promise<void> {
    try {
      const stats = await fs.stat(filePath);
      
      // Overwrite file with random data multiple times
      const passes = 3;
      for (let i = 0; i < passes; i++) {
        const randomData = crypto.randomBytes(stats.size);
        await fs.writeFile(filePath, randomData);
        await fs.fsync(await fs.open(filePath, 'r+'));
      }
      
      // Finally delete the file
      await fs.unlink(filePath);
    } catch (error) {
      throw new FileSystemError(`Secure delete failed: ${filePath}`, error);
    }
  }
  
  async copyFile(source: string, destination: string): Promise<void> {
    try {
      const resolvedSource = this.resolvePath(source);
      const resolvedDestination = this.resolvePath(destination);
      
      await this.ensureDirectoryExists(path.dirname(resolvedDestination));
      await fs.copyFile(resolvedSource, resolvedDestination);
    } catch (error) {
      throw new FileSystemError(`Failed to copy file: ${source} -> ${destination}`, error);
    }
  }
  
  async moveFile(source: string, destination: string): Promise<void> {
    try {
      const resolvedSource = this.resolvePath(source);
      const resolvedDestination = this.resolvePath(destination);
      
      await this.ensureDirectoryExists(path.dirname(resolvedDestination));
      
      try {
        // Try atomic rename first (same filesystem)
        await fs.rename(resolvedSource, resolvedDestination);
      } catch (error) {
        if (error.code === 'EXDEV') {
          // Cross-filesystem move: copy then delete
          await this.copyFile(source, destination);
          await this.deleteFile(source);
        } else {
          throw error;
        }
      }
    } catch (error) {
      throw new FileSystemError(`Failed to move file: ${source} -> ${destination}`, error);
    }
  }
  
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await fs.mkdir(dirPath, { recursive: true });
    } catch (error) {
      if (error.code !== 'EEXIST') {
        throw error;
      }
    }
  }
  
  private resolvePath(filePath: string): string {
    // Handle platform-specific path resolution
    if (path.isAbsolute(filePath)) {
      return path.normalize(filePath);
    }
    
    // Resolve relative paths from application directory
    return path.resolve(process.cwd(), filePath);
  }
}
```

## 🗂️ Directory Operations

### Directory Management
```typescript
class DirectoryManager {
  async createDirectory(dirPath: string, recursive: boolean = true): Promise<void> {
    try {
      await fs.mkdir(dirPath, { recursive });
    } catch (error) {
      if (error.code !== 'EEXIST') {
        throw new FileSystemError(`Failed to create directory: ${dirPath}`, error);
      }
    }
  }
  
  async deleteDirectory(dirPath: string, recursive: boolean = false): Promise<void> {
    try {
      if (recursive) {
        await fs.rm(dirPath, { recursive: true, force: true });
      } else {
        await fs.rmdir(dirPath);
      }
    } catch (error) {
      if (error.code !== 'ENOENT') {
        throw new FileSystemError(`Failed to delete directory: ${dirPath}`, error);
      }
    }
  }
  
  async listDirectory(dirPath: string, options: ListOptions = {}): Promise<FileInfo[]> {
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      const fileInfos: FileInfo[] = [];
      
      for (const entry of entries) {
        if (!options.includeHidden && entry.name.startsWith('.')) {
          continue;
        }
        
        const fullPath = path.join(dirPath, entry.name);
        const stats = await fs.stat(fullPath);
        
        const fileInfo: FileInfo = {
          name: entry.name,
          path: fullPath,
          size: stats.size,
          isDirectory: entry.isDirectory(),
          isFile: entry.isFile(),
          isSymlink: entry.isSymbolicLink(),
          permissions: this.getPermissions(stats),
          created: stats.birthtime,
          modified: stats.mtime,
          accessed: stats.atime
        };
        
        if (!options.filter || options.filter(fileInfo)) {
          fileInfos.push(fileInfo);
        }
        
        // Recursive listing
        if (options.recursive && entry.isDirectory()) {
          const subFiles = await this.listDirectory(fullPath, options);
          fileInfos.push(...subFiles);
        }
      }
      
      // Sort results
      if (options.sort) {
        this.sortFileInfos(fileInfos, options.sort, options.order || 'asc');
      }
      
      return fileInfos;
    } catch (error) {
      throw new FileSystemError(`Failed to list directory: ${dirPath}`, error);
    }
  }
  
  async getDirectorySize(dirPath: string): Promise<number> {
    let totalSize = 0;
    
    const files = await this.listDirectory(dirPath, { recursive: true });
    for (const file of files) {
      if (file.isFile) {
        totalSize += file.size;
      }
    }
    
    return totalSize;
  }
  
  async isDirectoryEmpty(dirPath: string): Promise<boolean> {
    try {
      const entries = await fs.readdir(dirPath);
      return entries.length === 0;
    } catch (error) {
      if (error.code === 'ENOENT') {
        return true;
      }
      throw new FileSystemError(`Failed to check directory: ${dirPath}`, error);
    }
  }
  
  private getPermissions(stats: fs.Stats): FilePermissions {
    const mode = stats.mode;
    return {
      readable: !!(mode & parseInt('400', 8)),
      writable: !!(mode & parseInt('200', 8)),
      executable: !!(mode & parseInt('100', 8)),
      mode: mode & parseInt('777', 8)
    };
  }
  
  private sortFileInfos(files: FileInfo[], sortBy: string, order: 'asc' | 'desc'): void {
    files.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'size':
          comparison = a.size - b.size;
          break;
        case 'modified':
          comparison = a.modified.getTime() - b.modified.getTime();
          break;
        case 'created':
          comparison = a.created.getTime() - b.created.getTime();
          break;
      }
      
      return order === 'desc' ? -comparison : comparison;
    });
  }
}
```

## 👁️ File Watching

### File System Watcher
```typescript
class FileSystemWatcher {
  private watchers: Map<string, fs.FSWatcher> = new Map();
  private debounceTimers: Map<string, NodeJS.Timeout> = new Map();
  private debounceDelay = 100; // ms
  
  watchFile(filePath: string, callback: FileChangeCallback): FileWatcher {
    const watcherId = this.generateWatcherId();
    
    try {
      const watcher = fs.watch(filePath, (eventType, filename) => {
        this.handleFileChange(watcherId, filePath, eventType, filename, callback);
      });
      
      this.watchers.set(watcherId, watcher);
      
      return {
        id: watcherId,
        path: filePath,
        stop: () => this.stopWatcher(watcherId)
      };
    } catch (error) {
      throw new FileSystemError(`Failed to watch file: ${filePath}`, error);
    }
  }
  
  watchDirectory(
    dirPath: string, 
    callback: DirectoryChangeCallback, 
    recursive: boolean = false
  ): FileWatcher {
    const watcherId = this.generateWatcherId();
    
    try {
      const watcher = fs.watch(dirPath, { recursive }, (eventType, filename) => {
        this.handleDirectoryChange(watcherId, dirPath, eventType, filename, callback);
      });
      
      this.watchers.set(watcherId, watcher);
      
      return {
        id: watcherId,
        path: dirPath,
        stop: () => this.stopWatcher(watcherId)
      };
    } catch (error) {
      throw new FileSystemError(`Failed to watch directory: ${dirPath}`, error);
    }
  }
  
  private handleFileChange(
    watcherId: string,
    filePath: string,
    eventType: string,
    filename: string | null,
    callback: FileChangeCallback
  ): void {
    // Debounce rapid file changes
    const debounceKey = `${watcherId}:${filename}`;
    
    if (this.debounceTimers.has(debounceKey)) {
      clearTimeout(this.debounceTimers.get(debounceKey)!);
    }
    
    const timer = setTimeout(async () => {
      this.debounceTimers.delete(debounceKey);
      
      try {
        const changeType = await this.determineChangeType(filePath, eventType);
        const event: FileChangeEvent = {
          type: changeType,
          path: filePath,
          filename: filename || path.basename(filePath),
          timestamp: new Date()
        };
        
        callback(event);
      } catch (error) {
        console.error('Error handling file change:', error);
      }
    }, this.debounceDelay);
    
    this.debounceTimers.set(debounceKey, timer);
  }
  
  private handleDirectoryChange(
    watcherId: string,
    dirPath: string,
    eventType: string,
    filename: string | null,
    callback: DirectoryChangeCallback
  ): void {
    if (!filename) return;
    
    const fullPath = path.join(dirPath, filename);
    const debounceKey = `${watcherId}:${filename}`;
    
    if (this.debounceTimers.has(debounceKey)) {
      clearTimeout(this.debounceTimers.get(debounceKey)!);
    }
    
    const timer = setTimeout(async () => {
      this.debounceTimers.delete(debounceKey);
      
      try {
        const changeType = await this.determineChangeType(fullPath, eventType);
        const event: DirectoryChangeEvent = {
          type: changeType,
          path: fullPath,
          filename,
          isDirectory: await this.isDirectory(fullPath),
          timestamp: new Date()
        };
        
        callback(event);
      } catch (error) {
        console.error('Error handling directory change:', error);
      }
    }, this.debounceDelay);
    
    this.debounceTimers.set(debounceKey, timer);
  }
  
  private async determineChangeType(filePath: string, eventType: string): Promise<ChangeType> {
    try {
      await fs.access(filePath);
      return eventType === 'rename' ? 'created' : 'modified';
    } catch {
      return 'deleted';
    }
  }
  
  private async isDirectory(filePath: string): Promise<boolean> {
    try {
      const stats = await fs.stat(filePath);
      return stats.isDirectory();
    } catch {
      return false;
    }
  }
  
  private stopWatcher(watcherId: string): void {
    const watcher = this.watchers.get(watcherId);
    if (watcher) {
      watcher.close();
      this.watchers.delete(watcherId);
    }
    
    // Clean up any pending debounce timers
    for (const [key, timer] of this.debounceTimers.entries()) {
      if (key.startsWith(`${watcherId}:`)) {
        clearTimeout(timer);
        this.debounceTimers.delete(key);
      }
    }
  }
  
  private generateWatcherId(): string {
    return crypto.randomUUID();
  }
  
  stopAllWatchers(): void {
    for (const watcherId of this.watchers.keys()) {
      this.stopWatcher(watcherId);
    }
  }
}

type ChangeType = 'created' | 'modified' | 'deleted';

interface FileChangeEvent {
  type: ChangeType;
  path: string;
  filename: string;
  timestamp: Date;
}

interface DirectoryChangeEvent {
  type: ChangeType;
  path: string;
  filename: string;
  isDirectory: boolean;
  timestamp: Date;
}

interface FileWatcher {
  id: string;
  path: string;
  stop(): void;
}

type FileChangeCallback = (event: FileChangeEvent) => void;
type DirectoryChangeCallback = (event: DirectoryChangeEvent) => void;
```

## 🗂️ Path Resolution

### Path Resolver
```typescript
class PathResolver {
  private appDataDir: string;
  private userHomeDir: string;
  private tempDir: string;
  
  constructor() {
    this.appDataDir = this.getAppDataDirectory();
    this.userHomeDir = os.homedir();
    this.tempDir = os.tmpdir();
  }
  
  resolveAppDataPath(relativePath: string): string {
    return path.join(this.appDataDir, relativePath);
  }
  
  resolveUserPath(relativePath: string): string {
    return path.join(this.userHomeDir, relativePath);
  }
  
  resolveTempPath(relativePath: string): string {
    return path.join(this.tempDir, relativePath);
  }
  
  getAppDataDirectory(): string {
    const appName = 'PromptPilot';
    
    switch (process.platform) {
      case 'win32':
        return path.join(os.homedir(), 'AppData', 'Roaming', appName);
      case 'darwin':
        return path.join(os.homedir(), 'Library', 'Application Support', appName);
      case 'linux':
        return path.join(os.homedir(), '.config', appName);
      default:
        return path.join(os.homedir(), `.${appName.toLowerCase()}`);
    }
  }
  
  getDocumentsDirectory(): string {
    switch (process.platform) {
      case 'win32':
        return path.join(os.homedir(), 'Documents');
      case 'darwin':
        return path.join(os.homedir(), 'Documents');
      case 'linux':
        return path.join(os.homedir(), 'Documents');
      default:
        return os.homedir();
    }
  }
  
  getDesktopDirectory(): string {
    switch (process.platform) {
      case 'win32':
        return path.join(os.homedir(), 'Desktop');
      case 'darwin':
        return path.join(os.homedir(), 'Desktop');
      case 'linux':
        return path.join(os.homedir(), 'Desktop');
      default:
        return os.homedir();
    }
  }
  
  normalizePath(filePath: string): string {
    return path.normalize(filePath);
  }
  
  relativePath(from: string, to: string): string {
    return path.relative(from, to);
  }
  
  isSubPath(parent: string, child: string): boolean {
    const relative = path.relative(parent, child);
    return !relative.startsWith('..') && !path.isAbsolute(relative);
  }
}
```

## 🧪 Testing Strategy

### Unit Tests
```typescript
describe('FileSystemManager', () => {
  let fsManager: FileSystemManager;
  let tempDir: string;
  
  beforeEach(async () => {
    fsManager = new FileSystemManager();
    tempDir = await fsManager.createTempDirectory('test');
  });
  
  afterEach(async () => {
    await fsManager.deleteDirectory(tempDir, true);
  });
  
  test('should read and write files', async () => {
    const testFile = path.join(tempDir, 'test.txt');
    const testContent = 'Hello, World!';
    
    await fsManager.writeFile(testFile, testContent);
    const content = await fsManager.readFile(testFile, { encoding: 'utf8' });
    
    expect(content).toBe(testContent);
  });
  
  test('should watch file changes', async () => {
    const testFile = path.join(tempDir, 'watched.txt');
    await fsManager.writeFile(testFile, 'initial content');
    
    const changes: FileChangeEvent[] = [];
    const watcher = fsManager.watchFile(testFile, (event) => {
      changes.push(event);
    });
    
    await fsManager.writeFile(testFile, 'updated content');
    
    // Wait for debounced event
    await new Promise(resolve => setTimeout(resolve, 200));
    
    expect(changes).toHaveLength(1);
    expect(changes[0].type).toBe('modified');
    
    watcher.stop();
  });
});
```
