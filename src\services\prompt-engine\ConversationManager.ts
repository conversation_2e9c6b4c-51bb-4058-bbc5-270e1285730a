/**
 * Conversation Manager implementation
 * Manages conversation context and history for multi-turn interactions
 */

import { v4 as uuidv4 } from 'uuid';
import {
  ConversationContext,
  ConversationOptions,
  ChatMessage,
  PromptEngineError
} from './types';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('ConversationManager');

/**
 * Conversation Manager implementation
 */
export class ConversationManager {
  private conversations: Map<string, ConversationContext> = new Map();
  private activeConversationId: string | null = null;
  private maxConversations: number = 100;
  private maxMessagesPerConversation: number = 1000;

  /**
   * Create a new conversation
   */
  createConversation(title?: string): string {
    const id = uuidv4();
    const conversation: ConversationContext = {
      id,
      messages: [],
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date(),
        title: title || `Conversation ${new Date().toLocaleString()}`,
        tags: []
      }
    };

    this.conversations.set(id, conversation);
    this.activeConversationId = id;

    // Clean up old conversations if at limit
    if (this.conversations.size > this.maxConversations) {
      this.cleanupOldConversations();
    }

    logger.info('Conversation created', { id, title });
    return id;
  }

  /**
   * Get conversation by ID
   */
  getConversation(id: string): ConversationContext | null {
    return this.conversations.get(id) || null;
  }

  /**
   * Get active conversation
   */
  getActiveConversation(): ConversationContext | null {
    if (!this.activeConversationId) {
      return null;
    }
    return this.getConversation(this.activeConversationId);
  }

  /**
   * Set active conversation
   */
  setActiveConversation(id: string): void {
    if (this.conversations.has(id)) {
      this.activeConversationId = id;
      logger.debug('Active conversation changed', { id });
    } else {
      throw new PromptEngineError(`Conversation not found: ${id}`);
    }
  }

  /**
   * Add message to conversation
   */
  addMessage(conversationId: string, message: ChatMessage): void {
    const conversation = this.conversations.get(conversationId);
    if (!conversation) {
      throw new PromptEngineError(`Conversation not found: ${conversationId}`);
    }

    // Add timestamp if not present
    const messageWithTimestamp: ChatMessage = {
      ...message,
      timestamp: message.timestamp || new Date()
    };

    conversation.messages.push(messageWithTimestamp);
    conversation.metadata.updatedAt = new Date();

    // Limit message count
    if (conversation.messages.length > this.maxMessagesPerConversation) {
      const toRemove = conversation.messages.length - this.maxMessagesPerConversation;
      conversation.messages.splice(0, toRemove);
      logger.debug('Trimmed old messages from conversation', { 
        conversationId, 
        removed: toRemove 
      });
    }

    logger.debug('Message added to conversation', { 
      conversationId, 
      role: message.role,
      contentLength: message.content.length
    });
  }

  /**
   * Add message to active conversation
   */
  addMessageToActive(message: ChatMessage): void {
    if (!this.activeConversationId) {
      // Create new conversation if none active
      this.createConversation();
    }
    
    this.addMessage(this.activeConversationId!, message);
  }

  /**
   * Get conversation messages with options
   */
  getMessages(conversationId: string, options: ConversationOptions = {}): ChatMessage[] {
    const conversation = this.conversations.get(conversationId);
    if (!conversation) {
      throw new PromptEngineError(`Conversation not found: ${conversationId}`);
    }

    let messages = [...conversation.messages];

    // Filter system messages if requested
    if (options.includeSystemMessages === false) {
      messages = messages.filter(msg => msg.role !== 'system');
    }

    // Limit message count
    if (options.maxMessages && messages.length > options.maxMessages) {
      messages = messages.slice(-options.maxMessages);
    }

    return messages;
  }

  /**
   * Get conversation context for AI
   */
  getConversationContext(conversationId: string, options: ConversationOptions = {}): ChatMessage[] {
    const messages = this.getMessages(conversationId, options);

    // If summarization is enabled and we have many messages
    if (options.summarizeOldMessages && messages.length > 10) {
      return this.summarizeOldMessages(messages, options.maxMessages || 10);
    }

    return messages;
  }

  /**
   * Update conversation metadata
   */
  updateConversation(id: string, updates: Partial<ConversationContext['metadata']>): void {
    const conversation = this.conversations.get(id);
    if (!conversation) {
      throw new PromptEngineError(`Conversation not found: ${id}`);
    }

    conversation.metadata = {
      ...conversation.metadata,
      ...updates,
      updatedAt: new Date()
    };

    logger.debug('Conversation updated', { id, updates: Object.keys(updates) });
  }

  /**
   * Delete conversation
   */
  deleteConversation(id: string): void {
    const deleted = this.conversations.delete(id);
    
    if (!deleted) {
      throw new PromptEngineError(`Conversation not found: ${id}`);
    }

    // Update active conversation if deleted
    if (this.activeConversationId === id) {
      this.activeConversationId = null;
    }

    logger.info('Conversation deleted', { id });
  }

  /**
   * List all conversations
   */
  listConversations(): ConversationContext[] {
    return Array.from(this.conversations.values())
      .sort((a, b) => b.metadata.updatedAt.getTime() - a.metadata.updatedAt.getTime());
  }

  /**
   * Search conversations
   */
  searchConversations(query: string): ConversationContext[] {
    const lowerQuery = query.toLowerCase();
    
    return this.listConversations().filter(conversation => {
      // Search in title
      if (conversation.metadata.title?.toLowerCase().includes(lowerQuery)) {
        return true;
      }

      // Search in tags
      if (conversation.metadata.tags?.some(tag => tag.toLowerCase().includes(lowerQuery))) {
        return true;
      }

      // Search in message content
      return conversation.messages.some(message => 
        message.content.toLowerCase().includes(lowerQuery)
      );
    });
  }

  /**
   * Export conversation
   */
  exportConversation(id: string): any {
    const conversation = this.conversations.get(id);
    if (!conversation) {
      throw new PromptEngineError(`Conversation not found: ${id}`);
    }

    return {
      ...conversation,
      exportedAt: new Date().toISOString()
    };
  }

  /**
   * Import conversation
   */
  importConversation(data: any): string {
    try {
      const id = data.id || uuidv4();
      
      const conversation: ConversationContext = {
        id,
        messages: data.messages || [],
        metadata: {
          createdAt: new Date(data.metadata?.createdAt || Date.now()),
          updatedAt: new Date(data.metadata?.updatedAt || Date.now()),
          title: data.metadata?.title || 'Imported Conversation',
          tags: data.metadata?.tags || []
        }
      };

      this.conversations.set(id, conversation);
      
      logger.info('Conversation imported', { id, messageCount: conversation.messages.length });
      return id;
    } catch (error) {
      logger.error('Failed to import conversation', { error });
      throw new PromptEngineError('Failed to import conversation', error);
    }
  }

  /**
   * Clear all conversations
   */
  clearAll(): void {
    this.conversations.clear();
    this.activeConversationId = null;
    logger.info('All conversations cleared');
  }

  /**
   * Get conversation statistics
   */
  getStats() {
    const conversations = Array.from(this.conversations.values());
    const totalMessages = conversations.reduce((sum, conv) => sum + conv.messages.length, 0);
    const averageMessages = conversations.length > 0 ? totalMessages / conversations.length : 0;

    return {
      totalConversations: conversations.length,
      totalMessages,
      averageMessages,
      activeConversationId: this.activeConversationId,
      oldestConversation: conversations.length > 0 ? 
        Math.min(...conversations.map(c => c.metadata.createdAt.getTime())) : null,
      newestConversation: conversations.length > 0 ? 
        Math.max(...conversations.map(c => c.metadata.updatedAt.getTime())) : null
    };
  }

  /**
   * Summarize old messages to save context
   */
  private summarizeOldMessages(messages: ChatMessage[], keepRecent: number): ChatMessage[] {
    if (messages.length <= keepRecent) {
      return messages;
    }

    const oldMessages = messages.slice(0, -keepRecent);
    const recentMessages = messages.slice(-keepRecent);

    // Create a summary of old messages
    const summary = this.createMessageSummary(oldMessages);
    
    const summaryMessage: ChatMessage = {
      role: 'system',
      content: `Previous conversation summary: ${summary}`,
      timestamp: new Date()
    };

    return [summaryMessage, ...recentMessages];
  }

  /**
   * Create summary of messages
   */
  private createMessageSummary(messages: ChatMessage[]): string {
    const userMessages = messages.filter(msg => msg.role === 'user');
    const assistantMessages = messages.filter(msg => msg.role === 'assistant');

    const topics = this.extractTopics(messages);
    
    return `Discussed ${topics.join(', ')}. User asked ${userMessages.length} questions, assistant provided ${assistantMessages.length} responses.`;
  }

  /**
   * Extract topics from messages
   */
  private extractTopics(messages: ChatMessage[]): string[] {
    // Simple topic extraction - in a real implementation, this could use NLP
    const topics = new Set<string>();
    
    for (const message of messages) {
      const words = message.content.toLowerCase().split(/\s+/);
      
      // Look for common topic indicators
      const topicWords = words.filter(word => 
        word.length > 4 && 
        !['about', 'could', 'would', 'should', 'please', 'thank'].includes(word)
      );
      
      topicWords.slice(0, 3).forEach(word => topics.add(word));
    }

    return Array.from(topics).slice(0, 5);
  }

  /**
   * Clean up old conversations
   */
  private cleanupOldConversations(): void {
    const conversations = this.listConversations();
    const toRemove = conversations.slice(this.maxConversations);

    for (const conversation of toRemove) {
      this.conversations.delete(conversation.id);
    }

    logger.debug('Cleaned up old conversations', { removed: toRemove.length });
  }
}
