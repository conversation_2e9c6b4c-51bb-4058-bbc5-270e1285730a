/**
 * File-based configuration storage implementation
 * Handles persistence, backup, and file watching for configuration data
 */

import * as fs from 'fs/promises';
import * as fsSync from 'fs';
import * as path from 'path';
import { ConfigurationStore } from './types';
import { getDefaultConfig } from './ConfigurationSchema';

export class FileConfigurationStore implements ConfigurationStore {
  private configPath: string;
  private data: any = {};
  private watcher: fsSync.FSWatcher | null = null;
  private watchCallback: ((event: any) => void) | null = null;

  constructor(configPath: string) {
    this.configPath = configPath;
  }

  async load(): Promise<any> {
    try {
      if (await this.exists()) {
        const content = await fs.readFile(this.configPath, 'utf-8');
        this.data = JSON.parse(content);
      } else {
        this.data = getDefaultConfig();
        await this.save(this.data);
      }
      
      return this.data;
    } catch (error) {
      console.error('Failed to load configuration:', error);
      this.data = getDefaultConfig();
      return this.data;
    }
  }

  async save(data: any): Promise<void> {
    try {
      const dir = path.dirname(this.configPath);
      await fs.mkdir(dir, { recursive: true });
      
      // Create backup before saving
      if (await this.exists()) {
        await this.backup();
      }
      
      this.data = { ...data };
      const content = JSON.stringify(this.data, null, 2);
      await fs.writeFile(this.configPath, content, 'utf-8');
    } catch (error) {
      console.error('Failed to save configuration:', error);
      throw new Error(`Failed to save configuration: ${error}`);
    }
  }

  get(key: string): any {
    const keys = key.split('.');
    let current = this.data;
    
    for (const k of keys) {
      if (current && typeof current === 'object' && k in current) {
        current = current[k];
      } else {
        return undefined;
      }
    }
    
    return current;
  }

  async set(key: string, value: any): Promise<void> {
    const keys = key.split('.');
    let current = this.data;
    
    // Navigate to the parent object
    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i];
      if (!current[k] || typeof current[k] !== 'object') {
        current[k] = {};
      }
      current = current[k];
    }
    
    // Set the value
    const lastKey = keys[keys.length - 1];
    current[lastKey] = value;
    
    await this.save(this.data);
  }

  async delete(key: string): Promise<void> {
    const keys = key.split('.');
    let current = this.data;
    
    // Navigate to the parent object
    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i];
      if (!current[k] || typeof current[k] !== 'object') {
        return; // Key doesn't exist
      }
      current = current[k];
    }
    
    // Delete the key
    const lastKey = keys[keys.length - 1];
    delete current[lastKey];
    
    await this.save(this.data);
  }

  async exists(): Promise<boolean> {
    try {
      await fs.access(this.configPath);
      return true;
    } catch {
      return false;
    }
  }

  async backup(): Promise<string> {
    if (!(await this.exists())) {
      throw new Error('Configuration file does not exist');
    }
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `${this.configPath}.backup.${timestamp}`;
    
    await fs.copyFile(this.configPath, backupPath);
    
    // Clean up old backups (keep only last 5)
    await this.cleanupBackups();
    
    return backupPath;
  }

  async restore(backupPath: string): Promise<void> {
    try {
      await fs.access(backupPath);
      await fs.copyFile(backupPath, this.configPath);
      await this.load(); // Reload the restored configuration
    } catch (error) {
      throw new Error(`Failed to restore backup: ${error}`);
    }
  }

  watch(callback: (event: any) => void): void {
    this.watchCallback = callback;
    
    if (this.watcher) {
      this.unwatch();
    }
    
    try {
      this.watcher = fsSync.watch(this.configPath, (eventType, filename) => {
        if (eventType === 'change' && this.watchCallback) {
          this.watchCallback({
            type: 'file-changed',
            path: this.configPath,
            filename,
            timestamp: new Date()
          });
        }
      });
    } catch (error) {
      console.error('Failed to watch configuration file:', error);
    }
  }

  unwatch(): void {
    if (this.watcher) {
      this.watcher.close();
      this.watcher = null;
    }
    this.watchCallback = null;
  }

  private async cleanupBackups(): Promise<void> {
    try {
      const dir = path.dirname(this.configPath);
      const basename = path.basename(this.configPath);
      const files = await fs.readdir(dir);
      
      const backupFiles = [];
      
      for (const file of files) {
        if (file.startsWith(`${basename}.backup.`)) {
          const filePath = path.join(dir, file);
          const stat = await fs.stat(filePath);
          backupFiles.push({
            name: file,
            path: filePath,
            mtime: stat.mtime
          });
        }
      }
      
      // Sort by modification time (newest first)
      backupFiles.sort((a, b) => b.mtime.getTime() - a.mtime.getTime());
      
      // Delete files beyond the maximum count (keep 5)
      const filesToDelete = backupFiles.slice(5);
      
      for (const file of filesToDelete) {
        await fs.unlink(file.path);
      }
    } catch (error) {
      console.error('Failed to cleanup backup files:', error);
    }
  }
}
