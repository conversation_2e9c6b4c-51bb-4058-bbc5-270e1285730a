# Speech-to-Text Bridge Module Specification

## 🎯 Purpose

The Speech-to-Text (STT) Bridge module provides a unified interface for speech recognition services, offering real-time transcription, multiple language support, and extensible provider integration with intelligent audio processing capabilities.

## 📋 Responsibilities

### Speech Recognition
- Real-time audio transcription
- Batch audio file processing
- Multiple language detection and support
- Confidence scoring and validation

### Provider Management
- Multiple STT service integration
- Provider failover and load balancing
- Service-specific optimizations
- Cost and performance monitoring

### Audio Processing
- Audio format conversion and optimization
- Noise reduction and enhancement
- Voice activity detection
- Audio segmentation and chunking

## 🏗️ Architecture

### Core STT Bridge
```typescript
class STTBridge {
  private providers: Map<string, STTProvider> = new Map();
  private router: STTRouter;
  private audioProcessor: AudioProcessor;
  private cache: TranscriptionCache;
  
  async transcribe(audio: AudioInput, options?: TranscriptionOptions): Promise<TranscriptionResult>;
  async transcribeStream(audioStream: ReadableStream, options?: TranscriptionOptions): Promise<AsyncIterable<TranscriptionChunk>>;
  async transcribeFile(filePath: string, options?: TranscriptionOptions): Promise<TranscriptionResult>;
  async detectLanguage(audio: AudioInput): Promise<LanguageDetectionResult>;
  
  registerProvider(name: string, provider: STTProvider): void;
  setDefaultProvider(name: string): void;
  getProviderStatus(): STTProviderStatus[];
}
```

### STT Interface (Placeholder Structure)
```typescript
interface TranscriptionOptions {
  language?: string;
  model?: string;
  prompt?: string;
  response_format?: 'json' | 'text' | 'srt' | 'verbose_json' | 'vtt';
  temperature?: number;
  timestamp_granularities?: ('word' | 'segment')[];
  word_timestamps?: boolean;
  speaker_labels?: boolean;
  max_speakers?: number;
  punctuation?: boolean;
  profanity_filter?: boolean;
  diarization?: boolean;
}

interface TranscriptionResult {
  text: string;
  language: string;
  confidence: number;
  duration: number;
  segments?: TranscriptionSegment[];
  words?: WordTimestamp[];
  speakers?: SpeakerInfo[];
  metadata: TranscriptionMetadata;
}

interface TranscriptionSegment {
  id: number;
  seek: number;
  start: number;
  end: number;
  text: string;
  tokens: number[];
  temperature: number;
  avg_logprob: number;
  compression_ratio: number;
  no_speech_prob: number;
  speaker?: string;
}

interface WordTimestamp {
  word: string;
  start: number;
  end: number;
  confidence: number;
  speaker?: string;
}

interface SpeakerInfo {
  id: string;
  name?: string;
  segments: number[];
  total_duration: number;
}

interface TranscriptionMetadata {
  provider: string;
  model: string;
  processing_time: number;
  audio_duration: number;
  file_size?: number;
  sample_rate: number;
  channels: number;
  format: string;
}
```

## 🔌 Provider Interface

### STT Provider Interface
```typescript
interface STTProvider {
  name: string;
  config: STTProviderConfig;
  
  transcribe(audio: AudioInput, options?: TranscriptionOptions): Promise<TranscriptionResult>;
  transcribeStream(audioStream: ReadableStream, options?: TranscriptionOptions): Promise<AsyncIterable<TranscriptionChunk>>;
  getSupportedLanguages(): string[];
  getSupportedFormats(): string[];
  getModels(): Promise<STTModelInfo[]>;
  validateConfig(): Promise<boolean>;
  getUsage(): Promise<STTUsageInfo>;
}

interface STTProviderConfig {
  apiKey?: string;
  baseURL?: string;
  defaultModel?: string;
  timeout: number;
  retryAttempts: number;
  maxFileSize: number; // bytes
  supportedFormats: string[];
  rateLimits: {
    requestsPerMinute: number;
    minutesPerMonth: number;
  };
}

interface STTModelInfo {
  id: string;
  name: string;
  description: string;
  languages: string[];
  maxDuration: number; // seconds
  accuracy: number; // 0-1
  speed: number; // relative speed factor
  cost: number; // per minute
}

interface AudioInput {
  data: Buffer | Blob | ArrayBuffer;
  format: string;
  sampleRate?: number;
  channels?: number;
  duration?: number;
}
```

### OpenAI Whisper Provider Implementation (Placeholder)
```typescript
class OpenAIWhisperProvider implements STTProvider {
  name = 'openai-whisper';
  config: STTProviderConfig;
  
  constructor(config: Partial<STTProviderConfig>) {
    this.config = {
      baseURL: 'https://api.openai.com/v1',
      defaultModel: 'whisper-1',
      timeout: 60000,
      retryAttempts: 3,
      maxFileSize: 25 * 1024 * 1024, // 25MB
      supportedFormats: ['mp3', 'mp4', 'mpeg', 'mpga', 'm4a', 'wav', 'webm'],
      rateLimits: {
        requestsPerMinute: 50,
        minutesPerMonth: 1000
      },
      ...config
    };
  }
  
  async transcribe(audio: AudioInput, options: TranscriptionOptions = {}): Promise<TranscriptionResult> {
    const formData = new FormData();
    
    // Convert audio input to blob
    const audioBlob = this.convertToBlob(audio);
    formData.append('file', audioBlob, `audio.${audio.format}`);
    formData.append('model', options.model || this.config.defaultModel!);
    
    if (options.language) {
      formData.append('language', options.language);
    }
    
    if (options.prompt) {
      formData.append('prompt', options.prompt);
    }
    
    if (options.response_format) {
      formData.append('response_format', options.response_format);
    }
    
    if (options.temperature !== undefined) {
      formData.append('temperature', options.temperature.toString());
    }
    
    if (options.timestamp_granularities) {
      formData.append('timestamp_granularities[]', options.timestamp_granularities.join(','));
    }
    
    try {
      const response = await this.makeRequest('/audio/transcriptions', formData);
      return this.transformResponse(response, audio);
    } catch (error) {
      throw new STTProviderError(`OpenAI Whisper transcription failed: ${error.message}`, error);
    }
  }
  
  async *transcribeStream(
    audioStream: ReadableStream, 
    options: TranscriptionOptions = {}
  ): AsyncIterable<TranscriptionChunk> {
    // Placeholder for streaming implementation
    // This would implement real-time streaming transcription
    const reader = audioStream.getReader();
    let buffer = new Uint8Array();
    const chunkSize = 16000 * 2; // 1 second of 16kHz 16-bit audio
    
    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        // Accumulate audio data
        const newBuffer = new Uint8Array(buffer.length + value.length);
        newBuffer.set(buffer);
        newBuffer.set(value, buffer.length);
        buffer = newBuffer;
        
        // Process chunks when we have enough data
        if (buffer.length >= chunkSize) {
          const chunk = buffer.slice(0, chunkSize);
          buffer = buffer.slice(chunkSize);
          
          try {
            const audioInput: AudioInput = {
              data: chunk.buffer,
              format: 'wav',
              sampleRate: 16000,
              channels: 1
            };
            
            const result = await this.transcribe(audioInput, options);
            
            yield {
              text: result.text,
              confidence: result.confidence,
              is_final: false,
              start_time: 0, // Would calculate based on chunk position
              end_time: 1000 // Would calculate based on chunk position
            };
          } catch (error) {
            console.warn('Failed to transcribe audio chunk:', error);
          }
        }
      }
      
      // Process remaining buffer
      if (buffer.length > 0) {
        const audioInput: AudioInput = {
          data: buffer.buffer,
          format: 'wav',
          sampleRate: 16000,
          channels: 1
        };
        
        const result = await this.transcribe(audioInput, options);
        
        yield {
          text: result.text,
          confidence: result.confidence,
          is_final: true,
          start_time: 0,
          end_time: buffer.length / (16000 * 2) * 1000
        };
      }
    } finally {
      reader.releaseLock();
    }
  }
  
  getSupportedLanguages(): string[] {
    return [
      'en', 'zh', 'de', 'es', 'ru', 'ko', 'fr', 'ja', 'pt', 'tr', 'pl', 'ca', 'nl',
      'ar', 'sv', 'it', 'id', 'hi', 'fi', 'vi', 'he', 'uk', 'el', 'ms', 'cs', 'ro',
      'da', 'hu', 'ta', 'no', 'th', 'ur', 'hr', 'bg', 'lt', 'la', 'mi', 'ml', 'cy',
      'sk', 'te', 'fa', 'lv', 'bn', 'sr', 'az', 'sl', 'kn', 'et', 'mk', 'br', 'eu',
      'is', 'hy', 'ne', 'mn', 'bs', 'kk', 'sq', 'sw', 'gl', 'mr', 'pa', 'si', 'km',
      'sn', 'yo', 'so', 'af', 'oc', 'ka', 'be', 'tg', 'sd', 'gu', 'am', 'yi', 'lo',
      'uz', 'fo', 'ht', 'ps', 'tk', 'nn', 'mt', 'sa', 'lb', 'my', 'bo', 'tl', 'mg',
      'as', 'tt', 'haw', 'ln', 'ha', 'ba', 'jw', 'su'
    ];
  }
  
  getSupportedFormats(): string[] {
    return this.config.supportedFormats;
  }
  
  async getModels(): Promise<STTModelInfo[]> {
    return [
      {
        id: 'whisper-1',
        name: 'Whisper',
        description: 'OpenAI Whisper speech recognition model',
        languages: this.getSupportedLanguages(),
        maxDuration: 600, // 10 minutes
        accuracy: 0.95,
        speed: 1.0,
        cost: 0.006 // $0.006 per minute
      }
    ];
  }
  
  async validateConfig(): Promise<boolean> {
    try {
      // Test with a minimal request
      const testAudio = this.generateSilentAudio(1000); // 1 second of silence
      await this.transcribe(testAudio);
      return true;
    } catch {
      return false;
    }
  }
  
  async getUsage(): Promise<STTUsageInfo> {
    // Placeholder implementation - would fetch actual usage data
    return {
      minutesUsedToday: 0,
      minutesUsedThisMonth: 0,
      requestsToday: 0,
      costToday: 0,
      remainingQuota: 1000
    };
  }
  
  private async makeRequest(endpoint: string, data: FormData): Promise<any> {
    const response = await fetch(`${this.config.baseURL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: data
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error?.message || `HTTP ${response.status}`);
    }
    
    return response.json();
  }
  
  private convertToBlob(audio: AudioInput): Blob {
    if (audio.data instanceof Blob) {
      return audio.data;
    } else if (audio.data instanceof ArrayBuffer) {
      return new Blob([audio.data], { type: `audio/${audio.format}` });
    } else if (Buffer.isBuffer(audio.data)) {
      return new Blob([audio.data], { type: `audio/${audio.format}` });
    } else {
      throw new Error('Unsupported audio data type');
    }
  }
  
  private transformResponse(response: any, audio: AudioInput): TranscriptionResult {
    return {
      text: response.text,
      language: response.language || 'en',
      confidence: 0.9, // Whisper doesn't provide confidence scores
      duration: audio.duration || 0,
      segments: response.segments,
      words: response.words,
      speakers: response.speakers,
      metadata: {
        provider: this.name,
        model: response.model || this.config.defaultModel!,
        processing_time: 0,
        audio_duration: audio.duration || 0,
        sample_rate: audio.sampleRate || 16000,
        channels: audio.channels || 1,
        format: audio.format
      }
    };
  }
  
  private generateSilentAudio(durationMs: number): AudioInput {
    const sampleRate = 16000;
    const samples = Math.floor(sampleRate * durationMs / 1000);
    const buffer = new ArrayBuffer(samples * 2); // 16-bit samples
    
    return {
      data: buffer,
      format: 'wav',
      sampleRate,
      channels: 1,
      duration: durationMs
    };
  }
}
```

## 🎵 Audio Processing

### Audio Processor
```typescript
class AudioProcessor {
  async convertFormat(
    audio: AudioInput, 
    targetFormat: string, 
    options?: AudioConversionOptions
  ): Promise<AudioInput> {
    const audioContext = new AudioContext();
    
    try {
      // Decode audio data
      const arrayBuffer = this.toArrayBuffer(audio.data);
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
      
      // Apply processing options
      let processedBuffer = audioBuffer;
      
      if (options?.sampleRate && options.sampleRate !== audioBuffer.sampleRate) {
        processedBuffer = await this.resample(audioBuffer, options.sampleRate);
      }
      
      if (options?.channels && options.channels !== audioBuffer.numberOfChannels) {
        processedBuffer = this.convertChannels(processedBuffer, options.channels);
      }
      
      if (options?.noiseReduction) {
        processedBuffer = await this.reduceNoise(processedBuffer);
      }
      
      // Encode to target format
      const encodedData = await this.encodeAudio(processedBuffer, targetFormat);
      
      return {
        data: encodedData,
        format: targetFormat,
        sampleRate: processedBuffer.sampleRate,
        channels: processedBuffer.numberOfChannels,
        duration: processedBuffer.duration * 1000
      };
    } finally {
      await audioContext.close();
    }
  }
  
  async detectVoiceActivity(audio: AudioInput): Promise<VoiceActivityResult[]> {
    const audioContext = new AudioContext();
    
    try {
      const arrayBuffer = this.toArrayBuffer(audio.data);
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
      const channelData = audioBuffer.getChannelData(0);
      
      const frameSize = 1024;
      const hopSize = 512;
      const threshold = 0.01;
      const activities: VoiceActivityResult[] = [];
      
      for (let i = 0; i < channelData.length - frameSize; i += hopSize) {
        const frame = channelData.slice(i, i + frameSize);
        const energy = this.calculateEnergy(frame);
        const isVoice = energy > threshold;
        
        const startTime = (i / audioBuffer.sampleRate) * 1000;
        const endTime = ((i + frameSize) / audioBuffer.sampleRate) * 1000;
        
        activities.push({
          startTime,
          endTime,
          isVoice,
          confidence: Math.min(energy / threshold, 1.0)
        });
      }
      
      return this.mergeConsecutiveActivities(activities);
    } finally {
      await audioContext.close();
    }
  }
  
  async segmentAudio(audio: AudioInput, maxSegmentDuration: number): Promise<AudioInput[]> {
    const segments: AudioInput[] = [];
    const maxBytes = this.calculateMaxBytes(audio, maxSegmentDuration);
    
    if (audio.data instanceof ArrayBuffer) {
      const buffer = new Uint8Array(audio.data);
      
      for (let i = 0; i < buffer.length; i += maxBytes) {
        const segmentData = buffer.slice(i, Math.min(i + maxBytes, buffer.length));
        const segmentDuration = (segmentData.length / buffer.length) * (audio.duration || 0);
        
        segments.push({
          data: segmentData.buffer,
          format: audio.format,
          sampleRate: audio.sampleRate,
          channels: audio.channels,
          duration: segmentDuration
        });
      }
    }
    
    return segments;
  }
  
  private toArrayBuffer(data: Buffer | Blob | ArrayBuffer): ArrayBuffer {
    if (data instanceof ArrayBuffer) {
      return data;
    } else if (Buffer.isBuffer(data)) {
      return data.buffer.slice(data.byteOffset, data.byteOffset + data.byteLength);
    } else if (data instanceof Blob) {
      // This would need to be async in real implementation
      throw new Error('Blob conversion not implemented in this example');
    } else {
      throw new Error('Unsupported data type');
    }
  }
  
  private calculateEnergy(frame: Float32Array): number {
    let energy = 0;
    for (let i = 0; i < frame.length; i++) {
      energy += frame[i] * frame[i];
    }
    return Math.sqrt(energy / frame.length);
  }
  
  private mergeConsecutiveActivities(activities: VoiceActivityResult[]): VoiceActivityResult[] {
    const merged: VoiceActivityResult[] = [];
    let current: VoiceActivityResult | null = null;
    
    for (const activity of activities) {
      if (!current || current.isVoice !== activity.isVoice) {
        if (current) merged.push(current);
        current = { ...activity };
      } else {
        current.endTime = activity.endTime;
        current.confidence = Math.max(current.confidence, activity.confidence);
      }
    }
    
    if (current) merged.push(current);
    return merged;
  }
  
  private calculateMaxBytes(audio: AudioInput, maxDurationMs: number): number {
    const sampleRate = audio.sampleRate || 16000;
    const channels = audio.channels || 1;
    const bytesPerSample = 2; // 16-bit
    
    return Math.floor((maxDurationMs / 1000) * sampleRate * channels * bytesPerSample);
  }
  
  private async resample(audioBuffer: AudioBuffer, targetSampleRate: number): Promise<AudioBuffer> {
    // Placeholder for resampling implementation
    return audioBuffer;
  }
  
  private convertChannels(audioBuffer: AudioBuffer, targetChannels: number): AudioBuffer {
    // Placeholder for channel conversion implementation
    return audioBuffer;
  }
  
  private async reduceNoise(audioBuffer: AudioBuffer): Promise<AudioBuffer> {
    // Placeholder for noise reduction implementation
    return audioBuffer;
  }
  
  private async encodeAudio(audioBuffer: AudioBuffer, format: string): Promise<ArrayBuffer> {
    // Placeholder for audio encoding implementation
    return new ArrayBuffer(0);
  }
}

interface AudioConversionOptions {
  sampleRate?: number;
  channels?: number;
  bitDepth?: number;
  noiseReduction?: boolean;
  normalize?: boolean;
}

interface VoiceActivityResult {
  startTime: number;
  endTime: number;
  isVoice: boolean;
  confidence: number;
}
```

## 🧪 Testing Strategy

### Unit Tests
```typescript
describe('STTBridge', () => {
  let sttBridge: STTBridge;
  let mockProvider: jest.Mocked<STTProvider>;
  
  beforeEach(() => {
    mockProvider = {
      name: 'test-stt',
      config: {} as STTProviderConfig,
      transcribe: jest.fn(),
      transcribeStream: jest.fn(),
      getSupportedLanguages: jest.fn(),
      getSupportedFormats: jest.fn(),
      getModels: jest.fn(),
      validateConfig: jest.fn(),
      getUsage: jest.fn()
    };
    
    sttBridge = new STTBridge();
    sttBridge.registerProvider('test', mockProvider);
    sttBridge.setDefaultProvider('test');
  });
  
  test('should transcribe audio successfully', async () => {
    const audioInput: AudioInput = {
      data: new ArrayBuffer(1024),
      format: 'wav',
      sampleRate: 16000,
      channels: 1,
      duration: 1000
    };
    
    const expectedResult: TranscriptionResult = {
      text: 'Hello world',
      language: 'en',
      confidence: 0.95,
      duration: 1000,
      metadata: {
        provider: 'test-stt',
        model: 'test-model',
        processing_time: 100,
        audio_duration: 1000,
        sample_rate: 16000,
        channels: 1,
        format: 'wav'
      }
    };
    
    mockProvider.transcribe.mockResolvedValue(expectedResult);
    
    const result = await sttBridge.transcribe(audioInput);
    
    expect(result).toEqual(expectedResult);
    expect(mockProvider.transcribe).toHaveBeenCalledWith(audioInput, undefined);
  });
});
```
