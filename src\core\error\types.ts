/**
 * Error handling type definitions
 * Comprehensive error classification and handling types
 */

// Error severity levels
export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

// Error categories
export type ErrorCategory = 
  | 'validation'
  | 'network'
  | 'filesystem'
  | 'database'
  | 'ai_provider'
  | 'stt_provider'
  | 'permission'
  | 'configuration'
  | 'authentication'
  | 'authorization'
  | 'business_logic'
  | 'system'
  | 'unknown';

// Error recovery strategies
export type RecoveryStrategy = 
  | 'retry'
  | 'fallback'
  | 'ignore'
  | 'user_intervention'
  | 'restart_component'
  | 'restart_application'
  | 'none';

// Error context
export interface ErrorContext {
  readonly userId?: string;
  readonly sessionId?: string;
  readonly requestId?: string;
  readonly component: string;
  readonly operation: string;
  readonly timestamp: Date;
  readonly userAgent?: string;
  readonly url?: string;
  readonly metadata?: Readonly<Record<string, unknown>>;
}

// Error details
export interface ErrorDetails {
  readonly code: string;
  readonly message: string;
  readonly category: ErrorCategory;
  readonly severity: ErrorSeverity;
  readonly recoverable: boolean;
  readonly userMessage?: string;
  readonly technicalMessage?: string;
  readonly suggestions?: readonly string[];
  readonly documentation?: string;
}

// Application error
export interface AppError extends Error {
  readonly id: string;
  readonly code: string;
  readonly category: ErrorCategory;
  readonly severity: ErrorSeverity;
  readonly context: ErrorContext;
  readonly details: ErrorDetails;
  readonly cause?: Error;
  readonly recoverable: boolean;
  readonly timestamp: Date;
  readonly stackTrace?: string;
  toJSON(): any;
}

// Error recovery options
export interface RecoveryOptions {
  readonly strategy: RecoveryStrategy;
  readonly maxRetries?: number;
  readonly retryDelay?: number;
  readonly fallbackAction?: () => Promise<void>;
  readonly userPrompt?: string;
  readonly autoRecover?: boolean;
}

// Error handler configuration
export interface ErrorHandlerConfig {
  readonly enableReporting: boolean;
  readonly enableRecovery: boolean;
  readonly maxRetries: number;
  readonly retryDelay: number;
  readonly reportingEndpoint?: string;
  readonly enableUserNotification: boolean;
  readonly enableAnalytics: boolean;
  readonly logLevel: 'error' | 'warn' | 'info' | 'debug';
}

// Error boundary state
export interface ErrorBoundaryState {
  readonly hasError: boolean;
  readonly error?: AppError;
  readonly errorInfo?: any;
  readonly retryCount: number;
  readonly lastRetry?: Date;
}

// Error reporter interface
export interface ErrorReporter {
  report(error: AppError): Promise<void>;
  reportBatch(errors: AppError[]): Promise<void>;
  configure(config: any): void;
}

// Error recovery handler
export interface RecoveryHandler {
  canRecover(error: AppError): boolean;
  recover(error: AppError, options?: RecoveryOptions): Promise<boolean>;
}

// Error analytics
export interface ErrorAnalytics {
  readonly errorId: string;
  readonly frequency: number;
  readonly firstOccurrence: Date;
  readonly lastOccurrence: Date;
  readonly affectedUsers: number;
  readonly resolutionRate: number;
  readonly averageRecoveryTime: number;
  readonly commonPatterns: readonly string[];
}

// Error notification
export interface ErrorNotification {
  readonly id: string;
  readonly title: string;
  readonly message: string;
  readonly severity: ErrorSeverity;
  readonly actions?: readonly ErrorAction[];
  readonly dismissible: boolean;
  readonly autoHide?: number;
}

// Error action
export interface ErrorAction {
  readonly id: string;
  readonly label: string;
  readonly action: () => Promise<void>;
  readonly primary?: boolean;
}

// Error handler interface
export interface IErrorHandler {
  handle(error: Error | AppError, context?: Partial<ErrorContext>): Promise<void>;
  createError(details: Partial<ErrorDetails>, context?: Partial<ErrorContext>, cause?: Error): AppError;
  registerRecoveryHandler(category: ErrorCategory, handler: RecoveryHandler): void;
  setReporter(reporter: ErrorReporter): void;
  configure(config: Partial<ErrorHandlerConfig>): void;
  getAnalytics(timeRange?: { start: Date; end: Date }): Promise<ErrorAnalytics[]>;
}

// Error boundary interface
export interface IErrorBoundary {
  readonly state: ErrorBoundaryState;
  catch(error: Error, errorInfo?: any): void;
  retry(): Promise<void>;
  reset(): void;
  configure(options: any): void;
}

// Predefined error codes
export const ErrorCodes = {
  // Validation errors
  VALIDATION_FAILED: 'VALIDATION_FAILED',
  INVALID_INPUT: 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  
  // Network errors
  NETWORK_ERROR: 'NETWORK_ERROR',
  CONNECTION_TIMEOUT: 'CONNECTION_TIMEOUT',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  
  // File system errors
  FILE_NOT_FOUND: 'FILE_NOT_FOUND',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  DISK_FULL: 'DISK_FULL',
  
  // Database errors
  DATABASE_CONNECTION_FAILED: 'DATABASE_CONNECTION_FAILED',
  QUERY_FAILED: 'QUERY_FAILED',
  TRANSACTION_FAILED: 'TRANSACTION_FAILED',
  
  // AI Provider errors
  AI_API_ERROR: 'AI_API_ERROR',
  AI_QUOTA_EXCEEDED: 'AI_QUOTA_EXCEEDED',
  AI_INVALID_RESPONSE: 'AI_INVALID_RESPONSE',
  
  // STT Provider errors
  STT_API_ERROR: 'STT_API_ERROR',
  STT_AUDIO_FORMAT_ERROR: 'STT_AUDIO_FORMAT_ERROR',
  STT_LANGUAGE_NOT_SUPPORTED: 'STT_LANGUAGE_NOT_SUPPORTED',
  
  // Configuration errors
  CONFIG_INVALID: 'CONFIG_INVALID',
  CONFIG_MISSING: 'CONFIG_MISSING',
  CONFIG_PARSE_ERROR: 'CONFIG_PARSE_ERROR',
  
  // Authentication/Authorization errors
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  
  // Business logic errors
  BUSINESS_RULE_VIOLATION: 'BUSINESS_RULE_VIOLATION',
  OPERATION_NOT_ALLOWED: 'OPERATION_NOT_ALLOWED',
  RESOURCE_CONFLICT: 'RESOURCE_CONFLICT',
  
  // System errors
  SYSTEM_ERROR: 'SYSTEM_ERROR',
  OUT_OF_MEMORY: 'OUT_OF_MEMORY',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
} as const;

export type ErrorCode = typeof ErrorCodes[keyof typeof ErrorCodes];
