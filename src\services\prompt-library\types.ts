/**
 * Prompt Library module type definitions
 * Comprehensive prompt management with organization, search, and versioning
 */

// Core prompt interfaces
export interface Prompt {
  id: string;
  title: string;
  content: string;
  description?: string;
  category: string;
  tags: string[];
  variables: PromptVariable[];
  metadata: PromptMetadata;
  version: number;
  parentId?: string; // For versioning
  isTemplate: boolean;
  isFavorite: boolean;
  isArchived: boolean;
  usage: PromptUsage;
  createdAt: Date;
  updatedAt: Date;
}

export interface PromptMetadata {
  author: string;
  source: 'user' | 'system' | 'imported';
  language: string;
  estimatedTokens: number;
  complexity: 'simple' | 'medium' | 'complex' | 'expert';
  keywords: string[];
  relatedPrompts: string[];
  customFields: Record<string, unknown>;
}

export interface PromptUsage {
  count: number;
  lastUsed: Date;
  averageRating?: number;
  successRate?: number;
  totalTokens: number;
  totalCost: number;
  averageExecutionTime: number;
  contexts: Array<{
    type: 'manual' | 'voice' | 'hotkey' | 'api' | 'scheduled';
    timestamp: Date;
    metadata?: Record<string, unknown>;
  }>;
}

export interface PromptVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  required: boolean;
  defaultValue?: any;
  description: string;
  validation?: ValidationRule[];
}

export interface ValidationRule {
  type: 'minLength' | 'maxLength' | 'pattern' | 'range' | 'custom';
  value: any;
  message: string;
}

// Category management
export interface Category {
  id: string;
  name: string;
  description: string;
  parentId?: string;
  color?: string;
  icon?: string;
  sortOrder: number;
  metadata: {
    createdAt: Date;
    promptCount: number;
  };
}

export interface CategoryNode {
  category: Category;
  children: CategoryNode[];
  promptCount: number;
}

// Search and filtering
export interface SearchQuery {
  text?: string;
  tags?: string[];
  category?: string;
  author?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  isTemplate?: boolean;
  isFavorite?: boolean;
  minRating?: number;
  sortBy?: 'relevance' | 'date' | 'usage' | 'rating' | 'title';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export interface SearchResult {
  prompt: Prompt;
  score: number;
  highlights: SearchHighlight[];
}

export interface SearchHighlight {
  field: 'title' | 'content' | 'description' | 'tags';
  text: string;
  startIndex: number;
  endIndex: number;
}

export interface SearchIndex {
  promptId: string;
  tokens: string[];
  fields: Record<string, string>;
}

// Version management
export interface PromptVersion {
  id: string;
  promptId: string;
  version: number;
  content: string;
  title: string;
  changes: string;
  createdAt: Date;
  createdBy: string;
}

export interface VersionDiff {
  additions: DiffChunk[];
  deletions: DiffChunk[];
  modifications: DiffChunk[];
}

export interface DiffChunk {
  type: 'addition' | 'deletion' | 'modification';
  content: string;
  lineNumber: number;
}

// Import/Export
export interface ExportOptions {
  format: 'json' | 'csv' | 'markdown';
  includeMetadata: boolean;
  includeVersions: boolean;
  categories?: string[];
  tags?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export interface ImportOptions {
  mergeStrategy: 'skip' | 'overwrite' | 'create_new';
  defaultCategory?: string;
  preserveIds: boolean;
  validateContent: boolean;
}

export interface ImportResult {
  imported: number;
  skipped: number;
  errors: ImportError[];
  warnings: string[];
}

export interface ImportError {
  line?: number;
  prompt?: Partial<Prompt>;
  error: string;
}

// Tag management
export interface Tag {
  name: string;
  color?: string;
  description?: string;
  usage: number;
  createdAt: Date;
}

// Analytics
export interface PromptAnalytics {
  promptId: string;
  totalUsage: number;
  recentUsage: number; // Last 30 days
  averageRating: number;
  successRate: number;
  usageHistory: UsageHistoryEntry[];
  popularVariables: VariableUsage[];
}

export interface UsageHistoryEntry {
  date: Date;
  count: number;
  rating?: number;
  success?: boolean;
}

export interface VariableUsage {
  name: string;
  usage: number;
  popularValues: string[];
}

export interface UsageTrend {
  date: Date;
  usage: number;
  newPrompts: number;
}

export interface ReportOptions {
  period: 'week' | 'month' | 'year';
  includeCharts: boolean;
  format: 'json' | 'html' | 'pdf';
}

export interface AnalyticsReport {
  period: string;
  totalPrompts: number;
  totalUsage: number;
  topPrompts: Prompt[];
  trends: UsageTrend[];
  categories: CategoryAnalytics[];
}

export interface CategoryAnalytics {
  category: Category;
  promptCount: number;
  usage: number;
  growth: number;
}

// Configuration
export interface PromptLibraryConfig {
  storage: {
    provider: 'sqlite' | 'file' | 'memory';
    path?: string;
    encryption?: boolean;
  };
  search: {
    indexingEnabled: boolean;
    maxResults: number;
    fuzzySearch: boolean;
  };
  versioning: {
    enabled: boolean;
    maxVersions: number;
    autoVersion: boolean;
  };
  analytics: {
    enabled: boolean;
    trackUsage: boolean;
    trackRatings: boolean;
  };
}

// Main interface
export interface IPromptLibrary {
  // CRUD operations
  save(prompt: Omit<Prompt, 'id'> | Prompt): Promise<string>;
  load(id: string): Promise<Prompt | null>;
  loadAll(filter?: Partial<SearchQuery>): Promise<Prompt[]>;
  update(id: string, updates: Partial<Prompt>): Promise<void>;
  delete(id: string): Promise<void>;
  
  // Search and filtering
  search(query: SearchQuery): Promise<SearchResult[]>;
  
  // Category management
  createCategory(category: Omit<Category, 'id'>): Promise<Category>;
  updateCategory(id: string, updates: Partial<Category>): Promise<Category>;
  deleteCategory(id: string, movePromptsTo?: string): Promise<boolean>;
  getCategoryTree(): Promise<CategoryNode[]>;
  getPromptsInCategory(categoryId: string, includeSubcategories?: boolean): Promise<Prompt[]>;
  
  // Tag management
  createTag(name: string, options?: Partial<Tag>): Promise<void>;
  deleteTag(name: string): Promise<void>;
  renameTag(oldName: string, newName: string): Promise<void>;
  getPopularTags(limit?: number): Promise<Tag[]>;
  getTagSuggestions(partial: string): Promise<string[]>;
  
  // Version management
  createVersion(promptId: string, changes: string): Promise<string>;
  getVersionHistory(promptId: string): Promise<PromptVersion[]>;
  restoreVersion(promptId: string, versionId: string): Promise<void>;
  
  // Import/Export
  exportPrompts(options: ExportOptions): Promise<string>;
  exportToFile(filePath: string, options: ExportOptions): Promise<void>;
  importFromJSON(jsonData: string, options: ImportOptions): Promise<ImportResult>;
  importFromFile(filePath: string, options: ImportOptions): Promise<ImportResult>;
  
  // Analytics
  recordUsage(promptId: string, rating?: number, success?: boolean): Promise<void>;
  getPromptAnalytics(promptId: string): Promise<PromptAnalytics>;
  getTopPrompts(period: 'week' | 'month' | 'year', limit: number): Promise<Prompt[]>;
  generateReport(options: ReportOptions): Promise<AnalyticsReport>;
}

// Error types
export class PromptLibraryError extends Error {
  constructor(message: string, public readonly cause?: Error) {
    super(message);
    this.name = 'PromptLibraryError';
  }
}

export class PromptNotFoundError extends PromptLibraryError {
  constructor(id: string) {
    super(`Prompt not found: ${id}`);
    this.name = 'PromptNotFoundError';
  }
}

export class CategoryNotFoundError extends PromptLibraryError {
  constructor(id: string) {
    super(`Category not found: ${id}`);
    this.name = 'CategoryNotFoundError';
  }
}

export class ValidationError extends PromptLibraryError {
  constructor(field: string, message: string) {
    super(`Validation error in ${field}: ${message}`);
    this.name = 'ValidationError';
  }
}
