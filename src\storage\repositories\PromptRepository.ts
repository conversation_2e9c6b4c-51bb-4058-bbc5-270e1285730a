/**
 * Prompt repository implementation
 * Handles CRUD operations for prompts with specialized queries
 */

import { BaseRepository } from '../BaseRepository';
import { DatabaseConnection, PromptEntity } from '../types';
import { Prompt } from '../../types/models/Prompt';

/**
 * Repository for prompt entities
 */
export class PromptRepository extends BaseRepository<Prompt> {
  constructor(db: DatabaseConnection) {
    super(db, 'prompts');
  }

  /**
   * Find prompts by category
   */
  async findByCategory(categoryId: string): Promise<Prompt[]> {
    return this.findAll({ categoryId });
  }

  /**
   * Find prompts by tags
   */
  async findByTags(tags: string[]): Promise<Prompt[]> {
    const placeholders = tags.map(() => 'JSON_EXTRACT(tags, "$") LIKE ?').join(' OR ');
    const params = tags.map(tag => `%"${tag}"%`);

    const sql = `SELECT * FROM ${this.tableName} WHERE ${placeholders} ORDER BY usage_count DESC`;
    const rows = await this.query(sql, params);
    return rows.map(row => this.mapRowToEntity(row));
  }

  /**
   * Find favorite prompts
   */
  async findFavorites(): Promise<Prompt[]> {
    return this.findAll({ 
      isFavorite: true,
      orderBy: 'last_used_at',
      orderDirection: 'DESC'
    });
  }

  /**
   * Find template prompts
   */
  async findTemplates(): Promise<Prompt[]> {
    return this.findAll({ 
      isTemplate: true,
      orderBy: 'created_at',
      orderDirection: 'DESC'
    });
  }

  /**
   * Find recently used prompts
   */
  async findRecentlyUsed(limit: number = 10): Promise<Prompt[]> {
    return this.findAll({
      orderBy: 'last_used_at',
      orderDirection: 'DESC',
      limit
    });
  }

  /**
   * Find most used prompts
   */
  async findMostUsed(limit: number = 10): Promise<Prompt[]> {
    return this.findAll({
      orderBy: 'usage_count',
      orderDirection: 'DESC',
      limit
    });
  }

  /**
   * Search prompts by text
   */
  async searchByText(query: string): Promise<Prompt[]> {
    const searchTerm = `%${query}%`;
    const sql = `
      SELECT *, 
             (CASE 
                WHEN title LIKE ? THEN 3
                WHEN content LIKE ? THEN 2
                WHEN description LIKE ? THEN 1
                ELSE 0
              END) as relevance_score
      FROM ${this.tableName}
      WHERE title LIKE ? OR content LIKE ? OR description LIKE ?
      ORDER BY relevance_score DESC, usage_count DESC
    `;

    const params = [searchTerm, searchTerm, searchTerm, searchTerm, searchTerm, searchTerm];
    const rows = await this.query(sql, params);
    return rows.map(row => this.mapRowToEntity(row));
  }

  /**
   * Find prompts by parent ID (versions)
   */
  async findVersions(parentId: string): Promise<Prompt[]> {
    return this.findAll({
      parentId,
      orderBy: 'version',
      orderDirection: 'DESC'
    });
  }

  /**
   * Find archived prompts
   */
  async findArchived(): Promise<Prompt[]> {
    return this.findAll({
      isArchived: true,
      orderBy: 'updated_at',
      orderDirection: 'DESC'
    });
  }

  /**
   * Update usage statistics
   */
  async updateUsage(id: string): Promise<void> {
    const sql = `
      UPDATE ${this.tableName} 
      SET usage_count = usage_count + 1, 
          last_used_at = CURRENT_TIMESTAMP,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    await this.db.run(sql, [id]);
  }

  /**
   * Toggle favorite status
   */
  async toggleFavorite(id: string): Promise<boolean> {
    const prompt = await this.findById(id);
    if (!prompt) {
      throw new Error(`Prompt with ID ${id} not found`);
    }

    const newFavoriteStatus = !prompt.isFavorite;
    await this.update(id, { isFavorite: newFavoriteStatus });
    return newFavoriteStatus;
  }

  /**
   * Archive/unarchive prompt
   */
  async toggleArchive(id: string): Promise<boolean> {
    const prompt = await this.findById(id);
    if (!prompt) {
      throw new Error(`Prompt with ID ${id} not found`);
    }

    const newArchivedStatus = !prompt.isArchived;
    await this.update(id, { isArchived: newArchivedStatus });
    return newArchivedStatus;
  }

  /**
   * Get prompts statistics
   */
  async getStatistics(): Promise<{
    total: number;
    favorites: number;
    templates: number;
    archived: number;
    byCategory: { categoryId: string; count: number }[];
  }> {
    const [total, favorites, templates, archived] = await Promise.all([
      this.count(),
      this.count({ isFavorite: true }),
      this.count({ isTemplate: true }),
      this.count({ isArchived: true })
    ]);

    const byCategoryRows = await this.query<{ category_id: string; count: number }>(
      'SELECT category_id, COUNT(*) as count FROM prompts GROUP BY category_id'
    );

    const byCategory = byCategoryRows.map(row => ({
      categoryId: row.category_id,
      count: row.count
    }));

    return {
      total,
      favorites,
      templates,
      archived,
      byCategory
    };
  }

  /**
   * Duplicate a prompt
   */
  async duplicate(id: string, newTitle?: string): Promise<string> {
    const original = await this.findById(id);
    if (!original) {
      throw new Error(`Prompt with ID ${id} not found`);
    }

    const duplicate: Prompt = {
      ...original,
      title: newTitle || `${original.title} (Copy)`,
      parentId: id,
      version: 1,
      isFavorite: false,
      usage: {
        count: 0,
        lastUsed: new Date(),
        totalTokens: 0,
        totalCost: 0,
        averageExecutionTime: 0,
        contexts: []
      }
    };

    // Remove ID to generate new one
    delete (duplicate as any).id;

    return this.save(duplicate);
  }

  /**
   * Map database row to Prompt entity
   */
  protected mapRowToEntity(row: any): Prompt {
    return {
      id: row.id,
      title: row.title,
      content: row.content,
      description: row.description,
      categoryId: row.category_id,
      tags: this.deserializeValue(row.tags, 'json') || [],
      variables: this.deserializeValue(row.variables, 'json') || [],
      metadata: this.deserializeValue(row.metadata, 'json') || {},
      version: row.version,
      parentId: row.parent_id,
      isTemplate: this.deserializeValue(row.is_template, 'boolean'),
      isFavorite: this.deserializeValue(row.is_favorite, 'boolean'),
      isArchived: this.deserializeValue(row.is_archived, 'boolean'),
      usage: {
        count: row.usage_count || 0,
        lastUsed: row.last_used_at ? new Date(row.last_used_at) : new Date(),
        totalTokens: row.total_tokens || 0,
        totalCost: row.total_cost || 0,
        averageExecutionTime: row.avg_execution_time || 0,
        contexts: []
      },
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      estimatedTokens: row.estimated_tokens || 0,
      complexity: row.complexity || 'simple',
      ...(row.last_used_at && { lastUsed: new Date(row.last_used_at) })
    };
  }

  /**
   * Override save to handle prompt-specific fields
   */
  async save(entity: Prompt): Promise<string> {
    const promptEntity: PromptEntity = {
      id: (entity as any).id || this.generateId(),
      title: entity.title,
      content: entity.content,
      categoryId: entity.categoryId,
      tags: JSON.stringify(entity.tags || []),
      variables: JSON.stringify(entity.variables || []),
      metadata: JSON.stringify(entity.metadata || {}),
      version: entity.version || 1,
      isTemplate: entity.isTemplate || false,
      isFavorite: entity.isFavorite || false,
      isArchived: entity.isArchived || false,
      usageCount: entity.usage?.count || 0,
      createdAt: (entity as any).createdAt || new Date(),
      updatedAt: entity.updatedAt || new Date(),
      createdBy: 'user', // Default value since Prompt interface doesn't have this field
      ...(entity.description && { description: entity.description }),
      ...(entity.parentId && { parentId: entity.parentId }),
      ...(entity.usage?.lastUsed && { lastUsedAt: entity.usage.lastUsed })
    };

    const columns = Object.keys(promptEntity).filter(key => promptEntity[key as keyof PromptEntity] !== undefined);
    const placeholders = columns.map(() => '?').join(', ');
    const values = columns.map(col => this.serializeValue(promptEntity[col as keyof PromptEntity]));

    const sql = `INSERT INTO ${this.tableName} (${columns.map(col => this.camelToSnake(col)).join(', ')}) VALUES (${placeholders})`;

    await this.db.run(sql, values);
    return promptEntity.id;
  }
}
