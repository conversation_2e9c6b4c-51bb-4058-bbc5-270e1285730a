/**
 * Base repository implementation
 * Provides common CRUD operations for database entities
 */

import { v4 as uuidv4 } from 'uuid';
import { DatabaseConnection, Repository, QueryFilter } from './types';
import { LoggerFactory } from '../core/logger';

const logger = LoggerFactory.getInstance().getLogger('BaseRepository');

/**
 * Abstract base repository class
 */
export abstract class BaseRepository<T> implements Repository<T> {
  protected readonly tableName: string;
  protected readonly db: DatabaseConnection;

  constructor(db: DatabaseConnection, tableName: string) {
    this.db = db;
    this.tableName = tableName;
  }

  /**
   * Save a new entity
   */
  async save(entity: T): Promise<string> {
    const id = this.generateId();
    const entityWithId = { ...entity, id } as any;
    
    // Add timestamps if not present
    const now = new Date().toISOString();
    if (!entityWithId.createdAt) {
      entityWithId.createdAt = now;
    }
    if (!entityWithId.updatedAt) {
      entityWithId.updatedAt = now;
    }

    const columns = Object.keys(entityWithId);
    const placeholders = columns.map(() => '?').join(', ');
    const values = columns.map(col => this.serializeValue(entityWithId[col]));

    const sql = `INSERT INTO ${this.tableName} (${columns.join(', ')}) VALUES (${placeholders})`;

    try {
      await this.db.run(sql, values);
      logger.debug('Entity saved', { tableName: this.tableName, id });
      return id;
    } catch (error) {
      logger.error('Failed to save entity', { tableName: this.tableName, error });
      throw error;
    }
  }

  /**
   * Find entity by ID
   */
  async findById(id: string): Promise<T | null> {
    const sql = `SELECT * FROM ${this.tableName} WHERE id = ?`;
    
    try {
      const row = await this.db.get(sql, [id]);
      if (!row) {
        return null;
      }
      
      const entity = this.mapRowToEntity(row);
      logger.debug('Entity found by ID', { tableName: this.tableName, id });
      return entity;
    } catch (error) {
      logger.error('Failed to find entity by ID', { tableName: this.tableName, id, error });
      throw error;
    }
  }

  /**
   * Find one entity with optional filtering
   */
  async findOne(filter?: QueryFilter): Promise<T | null> {
    const results = await this.findAll({ ...filter, limit: 1 });
    return results[0] || null;
  }

  /**
   * Find all entities with optional filtering
   */
  async findAll(filter?: QueryFilter): Promise<T[]> {
    let sql = `SELECT * FROM ${this.tableName}`;
    const params: any[] = [];

    if (filter) {
      const { whereClause, whereParams } = this.buildWhereClause(filter);
      if (whereClause) {
        sql += ` WHERE ${whereClause}`;
        params.push(...whereParams);
      }

      if (filter.orderBy) {
        sql += ` ORDER BY ${filter.orderBy} ${filter.orderDirection || 'ASC'}`;
      }

      if (filter.limit) {
        sql += ` LIMIT ${filter.limit}`;
        if (filter.offset) {
          sql += ` OFFSET ${filter.offset}`;
        }
      }
    }

    try {
      const rows = await this.db.all(sql, params);
      const entities = rows.map(row => this.mapRowToEntity(row));
      logger.debug('Entities found', { tableName: this.tableName, count: entities.length });
      return entities;
    } catch (error) {
      logger.error('Failed to find entities', { tableName: this.tableName, error });
      throw error;
    }
  }

  /**
   * Update an entity
   */
  async update(id: string, updates: Partial<T>): Promise<void> {
    const updatesWithTimestamp = { 
      ...updates, 
      updatedAt: new Date().toISOString() 
    } as any;

    const columns = Object.keys(updatesWithTimestamp);
    const setClause = columns.map(col => `${this.camelToSnake(col)} = ?`).join(', ');
    const values = columns.map(col => this.serializeValue(updatesWithTimestamp[col]));

    const sql = `UPDATE ${this.tableName} SET ${setClause} WHERE id = ?`;

    try {
      const result = await this.db.run(sql, [...values, id]);
      if (result.changes === 0) {
        throw new Error(`Entity with ID ${id} not found`);
      }
      logger.debug('Entity updated', { tableName: this.tableName, id, changes: result.changes });
    } catch (error) {
      logger.error('Failed to update entity', { tableName: this.tableName, id, error });
      throw error;
    }
  }

  /**
   * Delete an entity
   */
  async delete(id: string): Promise<void> {
    const sql = `DELETE FROM ${this.tableName} WHERE id = ?`;

    try {
      const result = await this.db.run(sql, [id]);
      if (result.changes === 0) {
        throw new Error(`Entity with ID ${id} not found`);
      }
      logger.debug('Entity deleted', { tableName: this.tableName, id, changes: result.changes });
    } catch (error) {
      logger.error('Failed to delete entity', { tableName: this.tableName, id, error });
      throw error;
    }
  }

  /**
   * Count entities with optional filtering
   */
  async count(filter?: QueryFilter): Promise<number> {
    let sql = `SELECT COUNT(*) as count FROM ${this.tableName}`;
    const params: any[] = [];

    if (filter) {
      const { whereClause, whereParams } = this.buildWhereClause(filter);
      if (whereClause) {
        sql += ` WHERE ${whereClause}`;
        params.push(...whereParams);
      }
    }

    try {
      const result = await this.db.get<{ count: number }>(sql, params);
      const count = result?.count || 0;
      logger.debug('Entity count', { tableName: this.tableName, count });
      return count;
    } catch (error) {
      logger.error('Failed to count entities', { tableName: this.tableName, error });
      throw error;
    }
  }

  /**
   * Check if entity exists
   */
  async exists(id: string): Promise<boolean> {
    const sql = `SELECT 1 FROM ${this.tableName} WHERE id = ? LIMIT 1`;

    try {
      const result = await this.db.get(sql, [id]);
      const exists = !!result;
      logger.debug('Entity existence check', { tableName: this.tableName, id, exists });
      return exists;
    } catch (error) {
      logger.error('Failed to check entity existence', { tableName: this.tableName, id, error });
      throw error;
    }
  }

  /**
   * Execute custom query
   */
  protected async query<R = any>(sql: string, params?: any[]): Promise<R[]> {
    try {
      const results = await this.db.all<R>(sql, params);
      logger.debug('Custom query executed', { sql, params, resultCount: results.length });
      return results;
    } catch (error) {
      logger.error('Custom query failed', { sql, params, error });
      throw error;
    }
  }

  /**
   * Execute custom query returning single result
   */
  protected async queryOne<R = any>(sql: string, params?: any[]): Promise<R | null> {
    try {
      const result = await this.db.get<R>(sql, params);
      logger.debug('Custom query one executed', { sql, params, hasResult: !!result });
      return result || null;
    } catch (error) {
      logger.error('Custom query one failed', { sql, params, error });
      throw error;
    }
  }

  /**
   * Generate unique ID for new entities
   */
  protected generateId(): string {
    return uuidv4();
  }

  /**
   * Map database row to entity
   */
  protected abstract mapRowToEntity(row: any): T;

  /**
   * Build WHERE clause from filter
   */
  protected buildWhereClause(filter: QueryFilter): { whereClause: string; whereParams: any[] } {
    const conditions: string[] = [];
    const params: any[] = [];

    Object.entries(filter).forEach(([key, value]) => {
      if (value !== undefined && !this.isQueryOption(key)) {
        const columnName = this.camelToSnake(key);
        
        if (Array.isArray(value)) {
          // Handle IN clause
          const placeholders = value.map(() => '?').join(', ');
          conditions.push(`${columnName} IN (${placeholders})`);
          params.push(...value);
        } else if (typeof value === 'string' && value.includes('%')) {
          // Handle LIKE clause
          conditions.push(`${columnName} LIKE ?`);
          params.push(value);
        } else {
          // Handle equality
          conditions.push(`${columnName} = ?`);
          params.push(this.serializeValue(value));
        }
      }
    });

    return {
      whereClause: conditions.join(' AND '),
      whereParams: params
    };
  }

  /**
   * Check if key is a query option (not a filter field)
   */
  private isQueryOption(key: string): boolean {
    return ['orderBy', 'orderDirection', 'limit', 'offset'].includes(key);
  }

  /**
   * Convert camelCase to snake_case
   */
  protected camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }

  /**
   * Convert snake_case to camelCase
   */
  protected snakeToCamel(str: string): string {
    return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  }

  /**
   * Serialize value for database storage
   */
  protected serializeValue(value: any): any {
    if (value === null || value === undefined) {
      return null;
    }
    
    if (typeof value === 'object' && !(value instanceof Date)) {
      return JSON.stringify(value);
    }
    
    if (value instanceof Date) {
      return value.toISOString();
    }
    
    if (typeof value === 'boolean') {
      return value ? 1 : 0;
    }
    
    return value;
  }

  /**
   * Deserialize value from database
   */
  protected deserializeValue(value: any, type?: string): any {
    if (value === null || value === undefined) {
      return null;
    }
    
    if (type === 'json' && typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    }
    
    if (type === 'boolean') {
      return Boolean(value);
    }
    
    if (type === 'date' && typeof value === 'string') {
      return new Date(value);
    }
    
    return value;
  }
}
