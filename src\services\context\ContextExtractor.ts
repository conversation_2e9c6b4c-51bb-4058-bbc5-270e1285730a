/**
 * Context Extractor Service
 * Main service that orchestrates all context extraction capabilities
 */

import { 
  IContextExtractor,
  ContextExtractionOptions,
  ExtractedContext,
  ScreenRegion,
  ContextExtractorConfig,
  ContextExtractionError,
  ContextErrorCode
} from './types';
import { TesseractOCREngine } from './OCREngine';
import { ScreenCaptureService } from './ScreenCapture';
import { ClipboardMonitor } from './ClipboardMonitor';
import { WindowTextExtractor } from './WindowTextExtractor';
import { FileContentReader } from './FileContentReader';
import { logger } from '../../core/logger';

export class ContextExtractor implements IContextExtractor {
  private ocrEngine!: TesseractOCREngine;
  private screenCapture!: ScreenCaptureService;
  private clipboardMonitor!: ClipboardMonitor;
  private windowExtractor!: WindowTextExtractor;
  private fileReader!: FileContentReader;
  private config: ContextExtractorConfig;

  constructor(config?: Partial<ContextExtractorConfig>) {
    this.config = this.createDefaultConfig();
    if (config) {
      this.configure(config);
    }

    this.initializeServices();
    logger.info('ContextExtractor initialized', { config: this.config });
  }

  /**
   * Extract comprehensive context from multiple sources
   */
  async extractContext(options: ContextExtractionOptions = {}): Promise<ExtractedContext> {
    const startTime = Date.now();
    logger.debug('Starting context extraction', { options });

    try {
      const context: ExtractedContext = {
        timestamp: new Date(),
        metadata: {
          source: [],
          totalLength: 0
        }
      };

      const extractionPromises: Promise<void>[] = [];

      // Extract clipboard content
      if (options.includeClipboard !== false) {
        extractionPromises.push(this.extractClipboardContext(context));
      }

      // Extract active window content
      if (options.includeActiveWindow !== false) {
        extractionPromises.push(this.extractActiveWindowContext(context));
      }

      // Extract screen capture with OCR
      if (options.includeScreenCapture) {
        extractionPromises.push(this.extractScreenCaptureContext(context, options.screenCaptureOptions));
      }

      // Execute all extractions in parallel
      await Promise.allSettled(extractionPromises);

      // Calculate total length and detect language
      this.finalizeContext(context, options);

      const duration = Date.now() - startTime;
      logger.info('Context extraction completed', {
        duration,
        sources: context.metadata.source,
        totalLength: context.metadata.totalLength,
        language: context.metadata.language
      });

      return context;
    } catch (error) {
      logger.error('Context extraction failed', {
        error: error instanceof Error ? error.message : String(error),
        duration: Date.now() - startTime
      });

      throw new ContextExtractionError(
        'Context extraction failed',
        ContextErrorCode.INITIALIZATION_FAILED,
        'ContextExtractor',
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Capture screen and extract text via OCR
   */
  async captureScreen(region?: ScreenRegion): Promise<string> {
    try {
      logger.debug('Capturing screen for text extraction', { region });

      const captureOptions = {
        includeOCR: true,
        ocrLanguage: this.config.ocr.defaultLanguage,
        format: this.config.screenCapture.defaultFormat,
        quality: this.config.screenCapture.defaultQuality,
        ...(region && { region })
      } as const;

      const result = region 
        ? await this.screenCapture.captureRegion(region, captureOptions)
        : await this.screenCapture.captureFullScreen(captureOptions);

      return result.extractedText || '';
    } catch (error) {
      logger.error('Screen capture failed', {
        region,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Get current clipboard text
   */
  async getClipboardText(): Promise<string> {
    try {
      const content = this.clipboardMonitor.getCurrentContent();
      return content.text;
    } catch (error) {
      logger.error('Clipboard access failed', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Get active window text
   */
  async getActiveWindowText(): Promise<string> {
    try {
      const windowContent = await this.windowExtractor.getActiveWindowText();
      return windowContent.text;
    } catch (error) {
      logger.error('Active window text extraction failed', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Extract text from file
   */
  async extractFromFile(filePath: string): Promise<string> {
    try {
      const fileContent = await this.fileReader.readFile(filePath, {
        maxSize: this.config.fileReader.maxFileSize,
        preview: true,
        previewLines: this.config.fileReader.previewLines
      });
      return fileContent.content;
    } catch (error) {
      logger.error('File extraction failed', {
        filePath,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Extract text from URL (placeholder implementation)
   */
  async extractFromUrl(url: string): Promise<string> {
    // This would require a web scraping implementation
    // For now, return a placeholder
    logger.warn('URL extraction not implemented', { url });
    return `URL extraction not yet implemented for: ${url}`;
  }

  /**
   * Configure the context extractor
   */
  configure(config: Partial<ContextExtractorConfig>): void {
    this.config = { ...this.config, ...config };
    logger.debug('ContextExtractor configuration updated', { config });
  }

  /**
   * Get current configuration
   */
  getConfig(): ContextExtractorConfig {
    return { ...this.config };
  }

  /**
   * Initialize all services
   */
  private initializeServices(): void {
    this.ocrEngine = new TesseractOCREngine();
    this.screenCapture = new ScreenCaptureService(
      this.ocrEngine,
      this.config.screenCapture.tempDirectory
    );
    this.clipboardMonitor = new ClipboardMonitor(
      this.config.clipboard.maxHistorySize,
      this.config.clipboard.monitoringInterval
    );
    this.windowExtractor = new WindowTextExtractor();
    this.fileReader = new FileContentReader();

    // Start clipboard monitoring if enabled
    if (this.config.clipboard.monitoringEnabled) {
      this.clipboardMonitor.startMonitoring();
    }
  }

  /**
   * Extract clipboard context
   */
  private async extractClipboardContext(context: ExtractedContext): Promise<void> {
    try {
      const clipboardContent = this.clipboardMonitor.getCurrentContent();
      if (clipboardContent.text.trim()) {
        context.clipboard = clipboardContent.text;
        context.metadata.source.push('clipboard');
      }
    } catch (error) {
      logger.warn('Clipboard extraction failed', { error });
    }
  }

  /**
   * Extract active window context
   */
  private async extractActiveWindowContext(context: ExtractedContext): Promise<void> {
    try {
      const windowContent = await this.windowExtractor.getActiveWindowText();
      if (windowContent.text.trim()) {
        context.activeWindow = {
          title: windowContent.info.title,
          content: windowContent.text,
          processName: windowContent.info.processName
        };
        context.metadata.source.push('activeWindow');
      }
    } catch (error) {
      logger.warn('Active window extraction failed', { error });
    }
  }

  /**
   * Extract screen capture context
   */
  private async extractScreenCaptureContext(
    context: ExtractedContext, 
    options?: any
  ): Promise<void> {
    try {
      const captureOptions = {
        includeOCR: true,
        ocrLanguage: this.config.ocr.defaultLanguage,
        format: this.config.screenCapture.defaultFormat,
        quality: this.config.screenCapture.defaultQuality,
        ...options
      };

      const result = await this.screenCapture.captureFullScreen(captureOptions);
      context.screenCapture = result;
      context.metadata.source.push('screenCapture');
    } catch (error) {
      logger.warn('Screen capture extraction failed', { error });
    }
  }

  /**
   * Finalize context extraction
   */
  private finalizeContext(context: ExtractedContext, options: ContextExtractionOptions): void {
    // Calculate total text length
    let totalLength = 0;
    let allText = '';

    if (context.clipboard) {
      totalLength += context.clipboard.length;
      allText += context.clipboard + ' ';
    }

    if (context.activeWindow) {
      totalLength += context.activeWindow.content.length;
      allText += context.activeWindow.content + ' ';
    }

    if (context.screenCapture?.extractedText) {
      totalLength += context.screenCapture.extractedText.length;
      allText += context.screenCapture.extractedText + ' ';
    }

    // Apply max length limit
    if (options.maxTextLength && totalLength > options.maxTextLength) {
      // Truncate each source proportionally
      const ratio = options.maxTextLength / totalLength;
      
      if (context.clipboard) {
        context.clipboard = context.clipboard.substring(0, Math.floor(context.clipboard.length * ratio));
      }
      
      if (context.activeWindow) {
        context.activeWindow.content = context.activeWindow.content.substring(
          0, Math.floor(context.activeWindow.content.length * ratio)
        );
      }
      
      if (context.screenCapture?.extractedText) {
        context.screenCapture.extractedText = context.screenCapture.extractedText.substring(
          0, Math.floor(context.screenCapture.extractedText.length * ratio)
        );
      }
      
      totalLength = options.maxTextLength;
    }

    context.metadata.totalLength = totalLength;

    // Simple language detection
    if (allText.trim()) {
      context.metadata.language = this.detectLanguage(allText);
    }
  }

  /**
   * Simple language detection
   */
  private detectLanguage(text: string): string {
    // Basic language detection - could be enhanced
    if (/[\u4e00-\u9fff]/.test(text)) return 'zh';
    if (/[\u3040-\u309f\u30a0-\u30ff]/.test(text)) return 'ja';
    if (/[\uac00-\ud7af]/.test(text)) return 'ko';
    if (/[àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]/i.test(text)) return 'fr';
    if (/[äöüß]/i.test(text)) return 'de';
    if (/[ñáéíóúü]/i.test(text)) return 'es';
    return 'en';
  }

  /**
   * Create default configuration
   */
  private createDefaultConfig(): ContextExtractorConfig {
    return {
      ocr: {
        enabled: true,
        defaultLanguage: 'eng',
        supportedLanguages: ['eng', 'spa', 'fra', 'deu', 'chi_sim'],
        confidence: 0.7
      },
      clipboard: {
        monitoringEnabled: true,
        monitoringInterval: 1000,
        maxHistorySize: 50
      },
      screenCapture: {
        defaultFormat: 'png',
        defaultQuality: 90,
        tempDirectory: '',
        cleanupInterval: 3600000 // 1 hour
      },
      fileReader: {
        maxFileSize: 10 * 1024 * 1024, // 10MB
        supportedExtensions: ['.txt', '.md', '.json', '.js', '.ts', '.py'],
        previewLines: 50
      },
      security: {
        requirePermissions: true,
        allowedSources: ['clipboard', 'activeWindow', 'screenCapture'],
        sanitizeContent: true
      }
    };
  }

  /**
   * Cleanup resources
   */
  async destroy(): Promise<void> {
    await Promise.all([
      this.ocrEngine.destroy(),
      this.screenCapture.destroy(),
      this.windowExtractor.destroy(),
      this.fileReader.destroy()
    ]);
    
    this.clipboardMonitor.destroy();
    
    logger.info('ContextExtractor destroyed');
  }
}
