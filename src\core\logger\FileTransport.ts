/**
 * File transport implementation with log rotation
 * Handles file-based logging with automatic rotation and cleanup
 */

import * as fs from 'fs/promises';
import * as fsSync from 'fs';
import * as path from 'path';
import { createWriteStream, WriteStream } from 'fs';
import {
  LogTransport,
  LogEntry,
  LogLevel,
  FileTransportOptions,
  LogFormatter
} from './types';
import { DefaultFormatter } from './formatters';

export class FileTransport implements LogTransport {
  private filePath: string;
  private maxFileSize: number;
  private maxFiles: number;
  private currentSize: number = 0;
  private writeStream: WriteStream | null = null;
  private formatter: LogFormatter;
  private isClosing: boolean = false;

  constructor(options: FileTransportOptions) {
    this.filePath = options.filePath;
    this.maxFileSize = options.maxFileSize || 10 * 1024 * 1024; // 10MB default
    this.maxFiles = options.maxFiles || 5;
    this.formatter = options.format || new DefaultFormatter();
    this.initializeStream();
  }

  async write(entry: LogEntry): Promise<void> {
    if (this.isClosing || !this.writeStream) {
      return;
    }

    const formatted = this.formatter.format(entry);
    const size = Buffer.byteLength(formatted, 'utf8');

    // Check if rotation is needed
    if (this.currentSize + size > this.maxFileSize) {
      await this.rotate();
    }

    return new Promise((resolve, reject) => {
      if (!this.writeStream) {
        reject(new Error('Write stream not available'));
        return;
      }

      this.writeStream.write(formatted + '\n', (error) => {
        if (error) {
          reject(error);
        } else {
          this.currentSize += size;
          resolve();
        }
      });
    });
  }

  async close(): Promise<void> {
    this.isClosing = true;
    
    if (this.writeStream) {
      await this.closeStream();
    }
  }

  private async rotate(): Promise<void> {
    try {
      await this.closeStream();
      await this.archiveCurrentFile();
      await this.cleanupOldFiles();
      this.initializeStream();
    } catch (error) {
      console.error('Failed to rotate log file:', error);
      // Try to reinitialize stream even if rotation failed
      this.initializeStream();
    }
  }

  private async archiveCurrentFile(): Promise<void> {
    if (!fsSync.existsSync(this.filePath)) {
      return;
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const archivePath = `${this.filePath}.${timestamp}`;
    
    await fs.rename(this.filePath, archivePath);
  }

  private async cleanupOldFiles(): Promise<void> {
    try {
      const dir = path.dirname(this.filePath);
      const basename = path.basename(this.filePath);
      const files = await fs.readdir(dir);

      const logFiles = [];
      
      for (const file of files) {
        if (file.startsWith(basename) && file !== basename) {
          const filePath = path.join(dir, file);
          const stat = await fs.stat(filePath);
          logFiles.push({
            name: file,
            path: filePath,
            mtime: stat.mtime
          });
        }
      }

      // Sort by modification time (newest first)
      logFiles.sort((a, b) => b.mtime.getTime() - a.mtime.getTime());

      // Delete files beyond the maximum count
      const filesToDelete = logFiles.slice(this.maxFiles - 1);

      for (const file of filesToDelete) {
        await fs.unlink(file.path);
      }
    } catch (error) {
      console.error('Failed to cleanup old log files:', error);
    }
  }

  private initializeStream(): void {
    try {
      const dir = path.dirname(this.filePath);
      
      // Create directory if it doesn't exist
      if (!fsSync.existsSync(dir)) {
        fsSync.mkdirSync(dir, { recursive: true });
      }

      // Get current file size
      this.currentSize = fsSync.existsSync(this.filePath) 
        ? fsSync.statSync(this.filePath).size 
        : 0;

      // Create write stream
      this.writeStream = createWriteStream(this.filePath, { flags: 'a' });

      // Handle stream errors
      this.writeStream.on('error', (error) => {
        console.error('Log file write stream error:', error);
      });

    } catch (error) {
      console.error('Failed to initialize log file stream:', error);
    }
  }

  private async closeStream(): Promise<void> {
    if (!this.writeStream) {
      return;
    }

    return new Promise((resolve) => {
      this.writeStream!.end(() => {
        this.writeStream = null;
        resolve();
      });
    });
  }
}
