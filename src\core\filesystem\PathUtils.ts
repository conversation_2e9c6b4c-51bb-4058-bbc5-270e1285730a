/**
 * Cross-platform path utilities
 * Secure path handling and validation for file system operations
 */

import * as path from 'path';
import * as os from 'os';
import { PathValidationResult, SecurityContext } from './types';

export class PathUtils {
  private static readonly DANGEROUS_PATTERNS = [
    /\.\./,           // Directory traversal
    /^\/dev\//,       // Device files (Unix)
    /^\/proc\//,      // Process files (Unix)
    /^\/sys\//,       // System files (Unix)
    /^[A-Z]:\\Windows\\/i, // Windows system directory
    /^[A-Z]:\\Program Files/i, // Program files
    /\0/,             // Null bytes
  ];

  private static readonly RESERVED_NAMES = new Set([
    'CON', 'PRN', 'AUX', 'NUL',
    'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
    'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
  ]);

  /**
   * Normalize a path for cross-platform compatibility
   */
  static normalize(inputPath: string): string {
    if (!inputPath || typeof inputPath !== 'string') {
      throw new Error('Path must be a non-empty string');
    }

    // Convert to forward slashes first
    let normalized = inputPath.replace(/\\/g, '/');

    // Normalize using Node.js path utilities
    normalized = path.normalize(normalized);

    // Convert back to forward slashes for consistency
    normalized = normalized.replace(/\\/g, '/');

    // Remove trailing slash except for root
    if (normalized.length > 1 && normalized.endsWith('/')) {
      normalized = normalized.slice(0, -1);
    }

    return normalized;
  }

  /**
   * Resolve a path to an absolute path
   */
  static resolve(...paths: string[]): string {
    return path.resolve(...paths);
  }

  /**
   * Join path segments
   */
  static join(...paths: string[]): string {
    return path.join(...paths);
  }

  /**
   * Get the directory name of a path
   */
  static dirname(filePath: string): string {
    return path.dirname(filePath);
  }

  /**
   * Get the base name of a path
   */
  static basename(filePath: string, ext?: string): string {
    return path.basename(filePath, ext);
  }

  /**
   * Get the extension of a path
   */
  static extname(filePath: string): string {
    return path.extname(filePath);
  }

  /**
   * Check if a path is absolute
   */
  static isAbsolute(filePath: string): boolean {
    return path.isAbsolute(filePath);
  }

  /**
   * Get relative path from one path to another
   */
  static relative(from: string, to: string): string {
    return path.relative(from, to);
  }

  /**
   * Validate a path for security and correctness
   */
  static validatePath(inputPath: string, context?: SecurityContext): PathValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Basic validation
      if (!inputPath || typeof inputPath !== 'string') {
        errors.push('Path must be a non-empty string');
        return { valid: false, normalized: '', errors, warnings };
      }

      // Normalize the path
      const normalized = this.normalize(inputPath);

      // Check for dangerous patterns
      for (const pattern of this.DANGEROUS_PATTERNS) {
        if (pattern.test(normalized)) {
          errors.push(`Path contains dangerous pattern: ${pattern.source}`);
        }
      }

      // Check for reserved names (Windows)
      if (os.platform() === 'win32') {
        const basename = this.basename(normalized).toUpperCase();
        const nameWithoutExt = basename.split('.')[0];
        if (this.RESERVED_NAMES.has(nameWithoutExt)) {
          errors.push(`Path uses reserved name: ${nameWithoutExt}`);
        }
      }

      // Check path length
      if (normalized.length > 260 && os.platform() === 'win32') {
        warnings.push('Path may be too long for Windows (>260 characters)');
      }

      // Security context validation
      if (context) {
        this.validateSecurityContext(normalized, context, errors, warnings);
      }

      return {
        valid: errors.length === 0,
        normalized,
        errors,
        warnings
      };
    } catch (error) {
      errors.push(`Path validation failed: ${error instanceof Error ? error.message : String(error)}`);
      return { valid: false, normalized: '', errors, warnings };
    }
  }

  /**
   * Check if a path is within allowed boundaries
   */
  static isPathWithinBounds(targetPath: string, allowedPaths: string[]): boolean {
    const normalizedTarget = this.normalize(this.resolve(targetPath));
    
    return allowedPaths.some(allowedPath => {
      const normalizedAllowed = this.normalize(this.resolve(allowedPath));
      return normalizedTarget.startsWith(normalizedAllowed + path.sep) || 
             normalizedTarget === normalizedAllowed;
    });
  }

  /**
   * Sanitize a filename for safe use
   */
  static sanitizeFilename(filename: string): string {
    if (typeof filename !== 'string') {
      throw new Error('Filename must be a string');
    }

    if (!filename) {
      throw new Error('Filename must be a non-empty string');
    }

    // Remove or replace invalid characters
    let sanitized = filename
      .replace(/[<>:"|?*\x00-\x1f]/g, '_')  // Replace invalid chars
      .replace(/^\.+/, '_')                  // Remove leading dots
      .replace(/\.+$/, '_')                  // Remove trailing dots
      .replace(/\s+/g, '_')                  // Replace spaces with underscores
      .substring(0, 255);                    // Limit length

    // Handle reserved names
    if (os.platform() === 'win32') {
      const nameWithoutExt = sanitized.split('.')[0].toUpperCase();
      if (this.RESERVED_NAMES.has(nameWithoutExt)) {
        sanitized = `_${sanitized}`;
      }
    }

    // Ensure not empty
    if (!sanitized) {
      sanitized = 'unnamed_file';
    }

    return sanitized;
  }

  /**
   * Get a safe temporary path
   */
  static getTempPath(prefix?: string): string {
    const tempDir = os.tmpdir();
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2);
    const filename = `${prefix || 'temp'}_${timestamp}_${random}`;
    
    return this.join(tempDir, this.sanitizeFilename(filename));
  }

  /**
   * Get user home directory
   */
  static getHomePath(): string {
    return os.homedir();
  }

  /**
   * Get application data directory
   */
  static getAppDataPath(appName: string): string {
    const platform = os.platform();
    const home = this.getHomePath();

    switch (platform) {
      case 'win32':
        return this.join(process.env.APPDATA || this.join(home, 'AppData', 'Roaming'), appName);
      case 'darwin':
        return this.join(home, 'Library', 'Application Support', appName);
      default:
        return this.join(process.env.XDG_CONFIG_HOME || this.join(home, '.config'), appName);
    }
  }

  /**
   * Check if two paths are the same
   */
  static isSamePath(path1: string, path2: string): boolean {
    try {
      const resolved1 = this.resolve(path1);
      const resolved2 = this.resolve(path2);
      
      // Case-insensitive comparison on Windows
      if (os.platform() === 'win32') {
        return resolved1.toLowerCase() === resolved2.toLowerCase();
      }
      
      return resolved1 === resolved2;
    } catch {
      return false;
    }
  }

  /**
   * Get file extension without dot
   */
  static getExtension(filePath: string): string {
    const ext = this.extname(filePath);
    return ext.startsWith('.') ? ext.substring(1) : ext;
  }

  /**
   * Change file extension
   */
  static changeExtension(filePath: string, newExt: string): string {
    const dir = this.dirname(filePath);
    const name = this.basename(filePath, this.extname(filePath));
    const ext = newExt.startsWith('.') ? newExt : `.${newExt}`;
    
    return this.join(dir, name + ext);
  }

  /**
   * Generate unique filename if file exists
   */
  static generateUniqueFilename(basePath: string, existsCheck: (path: string) => boolean): string {
    if (!existsCheck(basePath)) {
      return basePath;
    }

    const dir = this.dirname(basePath);
    const ext = this.extname(basePath);
    const name = this.basename(basePath, ext);

    let counter = 1;
    let uniquePath: string;

    do {
      uniquePath = this.join(dir, `${name}_${counter}${ext}`);
      counter++;
    } while (existsCheck(uniquePath) && counter < 1000);

    if (counter >= 1000) {
      throw new Error('Unable to generate unique filename after 1000 attempts');
    }

    return uniquePath;
  }

  private static validateSecurityContext(
    normalizedPath: string,
    context: SecurityContext,
    errors: string[],
    warnings: string[]
  ): void {
    // Check allowed paths
    if (context.allowedPaths && context.allowedPaths.length > 0) {
      if (!this.isPathWithinBounds(normalizedPath, [...context.allowedPaths])) {
        errors.push('Path is not within allowed boundaries');
      }
    }

    // Check denied paths
    if (context.deniedPaths && context.deniedPaths.length > 0) {
      if (this.isPathWithinBounds(normalizedPath, [...context.deniedPaths])) {
        errors.push('Path is within denied boundaries');
      }
    }

    // Check file extension
    const extension = this.getExtension(normalizedPath).toLowerCase();
    
    if (context.allowedExtensions && context.allowedExtensions.length > 0) {
      if (!context.allowedExtensions.includes(extension)) {
        errors.push(`File extension '${extension}' is not allowed`);
      }
    }

    if (context.deniedExtensions && context.deniedExtensions.length > 0) {
      if (context.deniedExtensions.includes(extension)) {
        errors.push(`File extension '${extension}' is denied`);
      }
    }
  }
}
