/**
 * Platform Adapter Factory
 * Creates platform-specific hotkey adapters
 */

import { IPlatformAdapter } from './types';
import { WindowsPlatformAdapter } from './WindowsPlatformAdapter';
import { MacOSPlatformAdapter } from './MacOSPlatformAdapter';
import { LinuxPlatformAdapter } from './LinuxPlatformAdapter';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('PlatformAdapterFactory');

/**
 * Platform Adapter Factory
 */
export class PlatformAdapterFactory {
  /**
   * Create platform-specific adapter
   */
  static create(platform: string): IPlatformAdapter {
    switch (platform) {
      case 'win32':
        logger.info('Creating Windows platform adapter');
        return new WindowsPlatformAdapter();
        
      case 'darwin':
        logger.info('Creating macOS platform adapter');
        return new MacOSPlatformAdapter();
        
      case 'linux':
        logger.info('Creating Linux platform adapter');
        return new LinuxPlatformAdapter();
        
      default:
        logger.warn('Unknown platform, falling back to Windows adapter', { platform });
        return new WindowsPlatformAdapter();
    }
  }

  /**
   * Get supported platforms
   */
  static getSupportedPlatforms(): string[] {
    return ['win32', 'darwin', 'linux'];
  }

  /**
   * Check if platform is supported
   */
  static isPlatformSupported(platform: string): boolean {
    return this.getSupportedPlatforms().includes(platform);
  }
}
