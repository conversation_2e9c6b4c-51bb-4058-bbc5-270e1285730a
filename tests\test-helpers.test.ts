/**
 * Test file to validate the centralized test helper utilities
 */

import {
  createTestPrompt,
  createTestCategory,
  createTestUser,
  createTestPromptMetadata,
  createTestPromptUsage,
  createTestPromptVariable,
  createTestMainPrompt,
  createTestMainCategory,
  createTestMainPromptMetadata,
  createTestMainPromptUsage,
  createMockPromptLibrary,
  createMockStorageManager,
  createMockAIService,
  createTestConfig,
  expectValidPrompt,
  expectValidCategory
} from './setup';

describe('Test Helper Utilities', () => {
  describe('Prompt Library Types', () => {
    it('should create valid test prompt', () => {
      const prompt = createTestPrompt();
      expectValidPrompt(prompt);
      expect(prompt.title).toBe('Test Prompt');
      expect(prompt.content).toBe('Test content with {{variable}}');
      expect(prompt.metadata).toBeDefined();
      expect(prompt.usage).toBeDefined();
      expect(prompt.variables).toHaveLength(1);
    });

    it('should create valid test category', () => {
      const category = createTestCategory();
      expectValidCategory(category);
      expect(category.name).toBe('Test Category');
      expect(category.metadata).toBeDefined();
      expect(category.metadata.promptCount).toBe(0);
    });

    it('should create valid test prompt metadata', () => {
      const metadata = createTestPromptMetadata();
      expect(metadata.author).toBe('test-user');
      expect(metadata.source).toBe('user');
      expect(metadata.complexity).toBe('simple');
      expect(metadata.keywords).toEqual(['test', 'example']);
      expect(metadata.relatedPrompts).toEqual([]);
      expect(metadata.customFields).toEqual({});
    });

    it('should create valid test prompt usage', () => {
      const usage = createTestPromptUsage();
      expect(usage.count).toBe(0);
      expect(usage.totalTokens).toBe(1000);
      expect(usage.totalCost).toBe(0.01);
      expect(usage.averageExecutionTime).toBe(150);
      expect(usage.contexts).toHaveLength(1);
      expect(usage.contexts[0].type).toBe('manual');
    });

    it('should create valid test prompt variable', () => {
      const variable = createTestPromptVariable();
      expect(variable.name).toBe('testVariable');
      expect(variable.type).toBe('string');
      expect(variable.required).toBe(false);
      expect(variable.defaultValue).toBe('default');
    });
  });

  describe('Main Types', () => {
    it('should create valid main test prompt', () => {
      const prompt = createTestMainPrompt();
      expect(prompt.id).toBe('test-main-prompt-id');
      expect(prompt.title).toBe('Test Prompt');
      expect(prompt.categoryId).toBe('test-category-id');
      expect(prompt.metadata).toBeDefined();
      expect(prompt.usage).toBeDefined();
      expect(prompt.estimatedTokens).toBe(100);
      expect(prompt.complexity).toBe('simple');
    });

    it('should create valid main test category', () => {
      const category = createTestMainCategory();
      expect(category.id).toBe('test-main-category-id');
      expect(category.name).toBe('Test Category');
      expect(category.promptCount).toBe(0);
      expect(category.isSystem).toBe(false);
      expect(category.path).toEqual(['Test Category']);
      expect(category.depth).toBe(0);
      expect(category.hasChildren).toBe(false);
    });

    it('should create valid test user', () => {
      const user = createTestUser();
      expect(user.id).toBe('test-user-id');
      expect(user.username).toBe('testuser');
      expect(user.email).toBe('<EMAIL>');
      expect(user.preferences).toBeDefined();
      expect(user.preferences.appearance.theme).toBe('light');
      expect(user.preferences.ai.defaultProvider).toBe('openai');
      expect(user.isActive).toBe(true);
    });
  });

  describe('Mock Services', () => {
    it('should create mock prompt library with correct methods', () => {
      const mockLibrary = createMockPromptLibrary();
      expect(mockLibrary.initialize).toBeDefined();
      expect(mockLibrary.createPrompt).toBeDefined();
      expect(mockLibrary.getAllPrompts).toBeDefined();
      expect(mockLibrary.createCategory).toBeDefined();
      expect(mockLibrary.getAllCategories).toBeDefined();
      expect(mockLibrary.exportPrompts).toBeDefined();
      expect(mockLibrary.importPrompts).toBeDefined();
    });

    it('should create mock storage manager with correct methods', () => {
      const mockStorage = createMockStorageManager();
      expect(mockStorage.initialize).toBeDefined();
      expect(mockStorage.close).toBeDefined();
      expect(mockStorage.transaction).toBeDefined();
      expect(mockStorage.backup).toBeDefined();
      expect(mockStorage.restore).toBeDefined();
    });

    it('should create mock AI service with correct methods', () => {
      const mockAI = createMockAIService();
      expect(mockAI.initialize).toBeDefined();
      expect(mockAI.complete).toBeDefined();
      expect(mockAI.stream).toBeDefined();
      expect(mockAI.getAvailableProviders).toBeDefined();
      expect(mockAI.estimateTokens).toBeDefined();
    });
  });

  describe('Configuration', () => {
    it('should create valid test configuration', () => {
      const config = createTestConfig();
      expect(config.database.path).toBe(':memory:');
      expect(config.logging.level).toBe('error');
      expect(config.ui.theme).toBe('light');
      expect(config.ai.defaultProvider).toBe('openai');
      expect(config.hotkeys.enableGlobalHotkeys).toBe(false);
    });

    it('should allow configuration overrides', () => {
      const config = createTestConfig({
        database: { path: './test.db', enableWAL: true, enableForeignKeys: false },
        logging: { level: 'debug', enableFileLogging: true, logDirectory: './debug-logs' }
      });
      expect(config.database.path).toBe('./test.db');
      expect(config.database.enableWAL).toBe(true);
      expect(config.logging.level).toBe('debug');
      expect(config.logging.enableFileLogging).toBe(true);
    });
  });

  describe('Overrides', () => {
    it('should allow prompt overrides', () => {
      const prompt = createTestPrompt({
        title: 'Custom Title',
        content: 'Custom content',
        category: 'custom-category'
      });
      expect(prompt.title).toBe('Custom Title');
      expect(prompt.content).toBe('Custom content');
      expect(prompt.category).toBe('custom-category');
    });

    it('should allow category overrides', () => {
      const category = createTestCategory({
        name: 'Custom Category',
        description: 'Custom description',
        color: '#00FF00'
      });
      expect(category.name).toBe('Custom Category');
      expect(category.description).toBe('Custom description');
      expect(category.color).toBe('#00FF00');
    });
  });
});
