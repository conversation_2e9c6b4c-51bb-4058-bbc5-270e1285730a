/**
 * System repository implementation
 * Handles system configuration, error logging, and analytics
 */

import { BaseRepository } from '../BaseRepository';
import { DatabaseConnection, ConfigEntity, ErrorEntity, AnalyticsEntity } from '../types';
import { 
  ConfigValue, 
  AppError, 
  AnalyticsEvent, 
  ConfigSettingType, 
  ConfigSource,
  ErrorType,

  EventCategory
} from '../../types/models/System';

/**
 * Repository for system configuration
 */
export class SystemRepository extends BaseRepository<ConfigValue> {
  constructor(db: DatabaseConnection) {
    super(db, 'config');
  }

  /**
   * Get configuration value by key
   */
  async getConfigValue(key: string): Promise<ConfigValue | null> {
    return this.findById(key);
  }

  /**
   * Set configuration value
   */
  async setConfigValue(
    key: string, 
    value: unknown, 
    type: ConfigSettingType = 'string',
    source: ConfigSource = 'user',
    encrypted: boolean = false
  ): Promise<void> {
    const configValue: ConfigValue = {
      key,
      value,
      type,
      source,
      timestamp: new Date(),
      valid: true,
      encrypted
    };

    await this.saveConfig(configValue);
  }

  /**
   * Get configuration values by source
   */
  async getConfigBySource(source: ConfigSource): Promise<ConfigValue[]> {
    const sql = `SELECT * FROM ${this.tableName} WHERE source = ? ORDER BY key`;
    const rows = await this.query(sql, [source]);
    return rows.map(row => this.mapConfigRowToEntity(row));
  }

  /**
   * Get all configuration as key-value map
   */
  async getAllConfig(): Promise<Record<string, unknown>> {
    const configs = await this.findAll();
    const result: Record<string, unknown> = {};
    
    for (const config of configs) {
      result[config.key] = config.value;
    }
    
    return result;
  }

  /**
   * Reset configuration to defaults
   */
  async resetConfig(keys?: string[]): Promise<void> {
    if (keys) {
      await this.db.transaction(async () => {
        for (const key of keys) {
          await this.delete(key);
        }
      });
    } else {
      await this.db.exec(`DELETE FROM ${this.tableName} WHERE source = 'user'`);
    }
  }

  /**
   * Log application error
   */
  async logError(error: AppError): Promise<string> {
    const errorEntity: ErrorEntity = {
      id: error.id,
      type: error.type,
      code: error.code,
      message: error.message,
      context: JSON.stringify(error.context),
      metadata: JSON.stringify(error.metadata),
      severity: error.severity,
      resolved: error.resolved,
      timestamp: error.timestamp,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...(error.stack && { stack: error.stack })
    };

    const sql = `
      INSERT INTO error_logs (
        id, type, code, message, stack, context, metadata, 
        severity, resolved, timestamp, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      errorEntity.id,
      errorEntity.type,
      errorEntity.code,
      errorEntity.message,
      errorEntity.stack,
      errorEntity.context,
      errorEntity.metadata,
      errorEntity.severity,
      errorEntity.resolved,
      errorEntity.timestamp,
      errorEntity.createdAt,
      errorEntity.updatedAt
    ];

    await this.db.run(sql, values);
    return errorEntity.id;
  }

  /**
   * Get recent errors
   */
  async getRecentErrors(limit: number = 50): Promise<AppError[]> {
    const sql = `
      SELECT * FROM error_logs 
      ORDER BY timestamp DESC 
      LIMIT ?
    `;

    const rows = await this.query(sql, [limit]);
    return rows.map(row => this.mapErrorRowToEntity(row));
  }

  /**
   * Get errors by type
   */
  async getErrorsByType(type: ErrorType): Promise<AppError[]> {
    const sql = `
      SELECT * FROM error_logs 
      WHERE type = ? 
      ORDER BY timestamp DESC
    `;

    const rows = await this.query(sql, [type]);
    return rows.map(row => this.mapErrorRowToEntity(row));
  }

  /**
   * Mark error as resolved
   */
  async resolveError(errorId: string): Promise<void> {
    const sql = `
      UPDATE error_logs 
      SET resolved = TRUE, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `;

    await this.db.run(sql, [errorId]);
  }

  /**
   * Log analytics event
   */
  async logAnalyticsEvent(event: AnalyticsEvent): Promise<string> {
    const analyticsEntity: AnalyticsEntity = {
      id: event.id,
      name: event.name,
      category: event.category,
      properties: JSON.stringify(event.properties),
      context: JSON.stringify(event.context),
      timestamp: event.timestamp,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...(event.userId && { userId: event.userId }),
      ...(event.sessionId && { sessionId: event.sessionId }),
      ...(event.anonymousId && { anonymousId: event.anonymousId })
    };

    const sql = `
      INSERT INTO analytics_events (
        id, name, category, properties, user_id, session_id, 
        anonymous_id, context, timestamp, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      analyticsEntity.id,
      analyticsEntity.name,
      analyticsEntity.category,
      analyticsEntity.properties,
      analyticsEntity.userId,
      analyticsEntity.sessionId,
      analyticsEntity.anonymousId,
      analyticsEntity.context,
      analyticsEntity.timestamp,
      analyticsEntity.createdAt,
      analyticsEntity.updatedAt
    ];

    await this.db.run(sql, values);
    return analyticsEntity.id;
  }

  /**
   * Get analytics events by category
   */
  async getAnalyticsByCategory(category: EventCategory, limit: number = 100): Promise<AnalyticsEvent[]> {
    const sql = `
      SELECT * FROM analytics_events 
      WHERE category = ? 
      ORDER BY timestamp DESC 
      LIMIT ?
    `;

    const rows = await this.query(sql, [category, limit]);
    return rows.map(row => this.mapAnalyticsRowToEntity(row));
  }

  /**
   * Get system statistics
   */
  async getSystemStatistics(): Promise<{
    totalConfigs: number;
    totalErrors: number;
    unresolvedErrors: number;
    totalEvents: number;
    errorsByType: Record<string, number>;
    eventsByCategory: Record<string, number>;
  }> {
    const [
      totalConfigs,
      totalErrors,
      unresolvedErrors,
      totalEvents
    ] = await Promise.all([
      this.count(),
      this.query<{ count: number }>('SELECT COUNT(*) as count FROM error_logs').then(r => r[0]?.count || 0),
      this.query<{ count: number }>('SELECT COUNT(*) as count FROM error_logs WHERE resolved = FALSE').then(r => r[0]?.count || 0),
      this.query<{ count: number }>('SELECT COUNT(*) as count FROM analytics_events').then(r => r[0]?.count || 0)
    ]);

    const errorsByTypeRows = await this.query<{ type: string; count: number }>(
      'SELECT type, COUNT(*) as count FROM error_logs GROUP BY type'
    );

    const eventsByCategoryRows = await this.query<{ category: string; count: number }>(
      'SELECT category, COUNT(*) as count FROM analytics_events GROUP BY category'
    );

    const errorsByType: Record<string, number> = {};
    errorsByTypeRows.forEach(row => {
      errorsByType[row.type] = row.count;
    });

    const eventsByCategory: Record<string, number> = {};
    eventsByCategoryRows.forEach(row => {
      eventsByCategory[row.category] = row.count;
    });

    return {
      totalConfigs,
      totalErrors,
      unresolvedErrors,
      totalEvents,
      errorsByType,
      eventsByCategory
    };
  }

  /**
   * Map database row to ConfigValue entity
   */
  protected mapRowToEntity(row: any): ConfigValue {
    return this.mapConfigRowToEntity(row);
  }

  /**
   * Map config row to ConfigValue
   */
  private mapConfigRowToEntity(row: any): ConfigValue {
    return {
      key: row.key,
      value: this.deserializeValue(row.value, row.type),
      type: row.type,
      source: row.source,
      timestamp: new Date(row.timestamp || row.updated_at),
      valid: this.deserializeValue(row.valid, 'boolean'),
      encrypted: this.deserializeValue(row.encrypted, 'boolean')
    };
  }

  /**
   * Map error row to AppError
   */
  private mapErrorRowToEntity(row: any): AppError {
    return {
      id: row.id,
      type: row.type,
      code: row.code,
      message: row.message,
      stack: row.stack,
      context: this.deserializeValue(row.context, 'json'),
      metadata: this.deserializeValue(row.metadata, 'json'),
      severity: row.severity,
      resolved: this.deserializeValue(row.resolved, 'boolean'),
      timestamp: new Date(row.timestamp)
    };
  }

  /**
   * Map analytics row to AnalyticsEvent
   */
  private mapAnalyticsRowToEntity(row: any): AnalyticsEvent {
    return {
      id: row.id,
      name: row.name,
      category: row.category,
      properties: this.deserializeValue(row.properties, 'json'),
      userId: row.user_id,
      sessionId: row.session_id,
      anonymousId: row.anonymous_id,
      context: this.deserializeValue(row.context, 'json'),
      timestamp: new Date(row.timestamp)
    };
  }

  /**
   * Save configuration value
   */
  private async saveConfig(configValue: ConfigValue): Promise<string> {
    const configEntity: ConfigEntity = {
      id: configValue.key,
      key: configValue.key,
      value: JSON.stringify(configValue.value),
      type: configValue.type,
      source: configValue.source,
      encrypted: configValue.encrypted,
      valid: configValue.valid,
      createdAt: new Date(),
      updatedAt: configValue.timestamp
    };

    const sql = `
      INSERT OR REPLACE INTO ${this.tableName} (
        id, key, value, type, source, encrypted, valid, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      configEntity.id,
      configEntity.key,
      configEntity.value,
      configEntity.type,
      configEntity.source,
      configEntity.encrypted,
      configEntity.valid,
      configEntity.createdAt,
      configEntity.updatedAt
    ];

    await this.db.run(sql, values);
    return configEntity.id;
  }
}
