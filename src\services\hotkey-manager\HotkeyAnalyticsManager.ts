/**
 * Hotkey Analytics Manager
 * Tracks hotkey usage and provides analytics
 */

import { HotkeyUsageStats, UsageRecord, HotkeyAnalytics } from './types';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('HotkeyAnalyticsManager');

/**
 * Hotkey Analytics Manager implementation
 */
export class HotkeyAnalyticsManager {
  private enabled: boolean;
  private usageStats: Map<string, HotkeyUsageStats> = new Map();
  private actionStats: Map<string, { totalExecutions: number; totalTime: number; errors: number }> = new Map();
  private startTime: Date = new Date();

  constructor(enabled: boolean = true) {
    this.enabled = enabled;
  }

  /**
   * Record hotkey usage
   */
  recordHotkeyUsage(accelerator: string, actionId: string, context?: string): void {
    if (!this.enabled) return;

    try {
      let stats = this.usageStats.get(accelerator);
      
      if (!stats) {
        stats = {
          accelerator,
          actionId,
          totalUsage: 0,
          lastUsed: new Date(),
          usageHistory: [],
          errorCount: 0
        };
        this.usageStats.set(accelerator, stats);
      }

      // Update stats
      stats.totalUsage++;
      stats.lastUsed = new Date();
      
      // Add usage record
      const record: UsageRecord = {
        timestamp: new Date(),
        context,
        success: true
      };
      
      stats.usageHistory.push(record);
      
      // Keep only last 100 records
      if (stats.usageHistory.length > 100) {
        stats.usageHistory = stats.usageHistory.slice(-100);
      }

      logger.debug('Hotkey usage recorded', { accelerator, actionId, totalUsage: stats.totalUsage });
    } catch (error) {
      logger.error('Failed to record hotkey usage', { error, accelerator, actionId });
    }
  }

  /**
   * Record action execution
   */
  recordActionExecution(actionId: string, responseTime: number, success: boolean): void {
    if (!this.enabled) return;

    try {
      let stats = this.actionStats.get(actionId);
      
      if (!stats) {
        stats = {
          totalExecutions: 0,
          totalTime: 0,
          errors: 0
        };
        this.actionStats.set(actionId, stats);
      }

      stats.totalExecutions++;
      stats.totalTime += responseTime;
      
      if (!success) {
        stats.errors++;
      }

      // Update hotkey stats with response time
      for (const [accelerator, hotkeyStats] of Array.from(this.usageStats.entries())) {
        if (hotkeyStats.actionId === actionId && hotkeyStats.usageHistory.length > 0) {
          const lastRecord = hotkeyStats.usageHistory[hotkeyStats.usageHistory.length - 1];
          lastRecord.responseTime = responseTime;
          lastRecord.success = success;
          
          if (!success) {
            hotkeyStats.errorCount = (hotkeyStats.errorCount || 0) + 1;
          }

          // Calculate average response time
          const responseTimes = hotkeyStats.usageHistory
            .filter(r => r.responseTime !== undefined)
            .map(r => r.responseTime!);
          
          if (responseTimes.length > 0) {
            hotkeyStats.averageResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
          }
        }
      }

      logger.debug('Action execution recorded', { actionId, responseTime, success });
    } catch (error) {
      logger.error('Failed to record action execution', { error, actionId });
    }
  }

  /**
   * Get analytics data
   */
  getAnalytics(): HotkeyAnalytics {
    const allStats = Array.from(this.usageStats.values());
    const totalTriggers = allStats.reduce((sum, stats) => sum + stats.totalUsage, 0);
    
    // Calculate average response time
    const responseTimes = allStats
      .filter(stats => stats.averageResponseTime !== undefined)
      .map(stats => stats.averageResponseTime!);
    
    const averageResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
      : 0;

    // Calculate error rate
    const totalErrors = allStats.reduce((sum, stats) => sum + (stats.errorCount || 0), 0);
    const errorRate = totalTriggers > 0 ? totalErrors / totalTriggers : 0;

    // Get most and least used hotkeys
    const sortedByUsage = [...allStats].sort((a, b) => b.totalUsage - a.totalUsage);
    const mostUsedHotkeys = sortedByUsage.slice(0, 10);
    const leastUsedHotkeys = sortedByUsage.slice(-10).reverse();

    return {
      totalHotkeys: this.usageStats.size,
      activeHotkeys: allStats.filter(stats => stats.totalUsage > 0).length,
      totalTriggers,
      mostUsedHotkeys,
      leastUsedHotkeys,
      averageResponseTime,
      errorRate,
      lastResetTime: this.startTime
    };
  }

  /**
   * Get usage stats for specific hotkey
   */
  getHotkeyStats(accelerator: string): HotkeyUsageStats | null {
    return this.usageStats.get(accelerator) || null;
  }

  /**
   * Get top hotkeys by usage
   */
  getTopHotkeys(limit: number = 10): HotkeyUsageStats[] {
    return Array.from(this.usageStats.values())
      .sort((a, b) => b.totalUsage - a.totalUsage)
      .slice(0, limit);
  }

  /**
   * Get recent usage
   */
  getRecentUsage(hours: number = 24): UsageRecord[] {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    const recentRecords: UsageRecord[] = [];

    for (const stats of Array.from(this.usageStats.values())) {
      const recent = stats.usageHistory.filter(record => record.timestamp >= cutoff);
      recentRecords.push(...recent);
    }

    return recentRecords.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * Get usage trends
   */
  getUsageTrends(days: number = 7): Array<{ date: string; usage: number }> {
    const trends: Array<{ date: string; usage: number }> = [];
    const now = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      date.setHours(0, 0, 0, 0);
      
      const nextDate = new Date(date);
      nextDate.setDate(nextDate.getDate() + 1);

      let dayUsage = 0;
      for (const stats of Array.from(this.usageStats.values())) {
        const dayRecords = stats.usageHistory.filter(record => 
          record.timestamp >= date && record.timestamp < nextDate
        );
        dayUsage += dayRecords.length;
      }

      trends.push({
        date: date.toISOString().split('T')[0],
        usage: dayUsage
      });
    }

    return trends;
  }

  /**
   * Reset analytics
   */
  reset(): void {
    this.usageStats.clear();
    this.actionStats.clear();
    this.startTime = new Date();
    
    logger.info('Hotkey analytics reset');
  }

  /**
   * Enable/disable analytics
   */
  setEnabled(enabled: boolean): void {
    this.enabled = enabled;
    logger.info('Hotkey analytics enabled state changed', { enabled });
  }

  /**
   * Export analytics data
   */
  export(): any {
    return {
      enabled: this.enabled,
      startTime: this.startTime,
      usageStats: Array.from(this.usageStats.entries()),
      actionStats: Array.from(this.actionStats.entries()),
      analytics: this.getAnalytics(),
      exportedAt: new Date()
    };
  }

  /**
   * Import analytics data
   */
  import(data: any): void {
    try {
      if (data.usageStats && Array.isArray(data.usageStats)) {
        this.usageStats.clear();
        for (const [key, stats] of data.usageStats) {
          this.usageStats.set(key, stats);
        }
      }

      if (data.actionStats && Array.isArray(data.actionStats)) {
        this.actionStats.clear();
        for (const [key, stats] of data.actionStats) {
          this.actionStats.set(key, stats);
        }
      }

      if (data.startTime) {
        this.startTime = new Date(data.startTime);
      }

      logger.info('Hotkey analytics data imported');
    } catch (error) {
      logger.error('Failed to import analytics data', { error });
    }
  }
}
