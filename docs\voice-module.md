# Voice Module Specification

## 🎯 Purpose

The Voice Module provides comprehensive speech-to-text capabilities, enabling users to input prompts and commands through voice recognition with real-time transcription and audio processing features.

## 📋 Responsibilities

### Audio Capture
- Microphone input management
- Real-time audio recording
- Audio format conversion
- Noise reduction and filtering

### Speech Recognition
- Real-time speech-to-text conversion
- Multiple language support
- Confidence scoring
- Punctuation and formatting

### Voice Commands
- Hotkey-triggered recording
- Push-to-talk functionality
- Continuous listening mode
- Voice command recognition

## 🏗️ Architecture

### Core Voice Module
```typescript
class VoiceModule {
  private audioRecorder: AudioRecorder;
  private sttService: STTService;
  private audioProcessor: AudioProcessor;
  private commandProcessor: VoiceCommandProcessor;
  
  async startRecording(options?: RecordingOptions): Promise<void>;
  async stopRecording(): Promise<string>;
  async processAudioFile(filePath: string): Promise<string>;
  async startContinuousListening(): Promise<void>;
  async stopContinuousListening(): Promise<void>;
}
```

### Recording Options
```typescript
interface RecordingOptions {
  language?: string;
  maxDuration?: number; // seconds
  autoStop?: boolean;
  noiseReduction?: boolean;
  format?: 'wav' | 'mp3' | 'ogg';
  sampleRate?: number;
  channels?: number;
  bitDepth?: number;
}

interface RecordingState {
  isRecording: boolean;
  duration: number;
  audioLevel: number;
  language: string;
  confidence?: number;
}
```

## 🎤 Audio Recording Implementation

### Audio Recorder
```typescript
class AudioRecorder {
  private mediaRecorder: MediaRecorder | null = null;
  private audioStream: MediaStream | null = null;
  private audioChunks: Blob[] = [];
  private recordingState: RecordingState;
  
  async initialize(): Promise<void> {
    try {
      this.audioStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 16000,
          channelCount: 1
        }
      });
    } catch (error) {
      throw new Error(`Failed to initialize audio: ${error.message}`);
    }
  }
  
  async startRecording(options: RecordingOptions = {}): Promise<void> {
    if (!this.audioStream) {
      await this.initialize();
    }
    
    this.audioChunks = [];
    this.recordingState = {
      isRecording: true,
      duration: 0,
      audioLevel: 0,
      language: options.language || 'en-US'
    };
    
    const mimeType = this.getSupportedMimeType(options.format);
    this.mediaRecorder = new MediaRecorder(this.audioStream!, { mimeType });
    
    this.mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        this.audioChunks.push(event.data);
      }
    };
    
    this.mediaRecorder.start(100); // Collect data every 100ms
    
    // Auto-stop after max duration
    if (options.maxDuration) {
      setTimeout(() => {
        if (this.recordingState.isRecording) {
          this.stopRecording();
        }
      }, options.maxDuration * 1000);
    }
    
    // Start audio level monitoring
    this.startAudioLevelMonitoring();
  }
  
  async stopRecording(): Promise<Blob> {
    return new Promise((resolve, reject) => {
      if (!this.mediaRecorder || !this.recordingState.isRecording) {
        reject(new Error('No active recording'));
        return;
      }
      
      this.mediaRecorder.onstop = () => {
        const audioBlob = new Blob(this.audioChunks, { 
          type: this.mediaRecorder!.mimeType 
        });
        this.recordingState.isRecording = false;
        resolve(audioBlob);
      };
      
      this.mediaRecorder.stop();
      this.stopAudioLevelMonitoring();
    });
  }
  
  getRecordingState(): RecordingState {
    return { ...this.recordingState };
  }
  
  private getSupportedMimeType(format?: string): string {
    const formats = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/wav'
    ];
    
    for (const fmt of formats) {
      if (MediaRecorder.isTypeSupported(fmt)) {
        return fmt;
      }
    }
    
    return 'audio/webm';
  }
  
  private startAudioLevelMonitoring(): void {
    if (!this.audioStream) return;
    
    const audioContext = new AudioContext();
    const analyser = audioContext.createAnalyser();
    const microphone = audioContext.createMediaStreamSource(this.audioStream);
    
    microphone.connect(analyser);
    analyser.fftSize = 256;
    
    const dataArray = new Uint8Array(analyser.frequencyBinCount);
    
    const updateLevel = () => {
      if (!this.recordingState.isRecording) return;
      
      analyser.getByteFrequencyData(dataArray);
      const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
      this.recordingState.audioLevel = average / 255;
      
      requestAnimationFrame(updateLevel);
    };
    
    updateLevel();
  }
  
  private stopAudioLevelMonitoring(): void {
    this.recordingState.audioLevel = 0;
  }
}
```

## 🗣️ Speech-to-Text Service

### STT Service Interface
```typescript
interface STTService {
  transcribe(audioBlob: Blob, options?: STTOptions): Promise<TranscriptionResult>;
  transcribeFile(filePath: string, options?: STTOptions): Promise<TranscriptionResult>;
  transcribeStream(audioStream: ReadableStream, options?: STTOptions): Promise<AsyncIterable<TranscriptionChunk>>;
  getSupportedLanguages(): string[];
  detectLanguage(audioBlob: Blob): Promise<string>;
}

interface STTOptions {
  language?: string;
  model?: string;
  punctuation?: boolean;
  profanityFilter?: boolean;
  speakerDiarization?: boolean;
  wordTimestamps?: boolean;
}

interface TranscriptionResult {
  text: string;
  confidence: number;
  language: string;
  duration: number;
  words?: WordTimestamp[];
  speakers?: SpeakerSegment[];
}

interface WordTimestamp {
  word: string;
  startTime: number;
  endTime: number;
  confidence: number;
}
```

### STT Bridge Implementation (Placeholder)
```typescript
class STTBridgeService implements STTService {
  private apiKey: string;
  private baseURL: string;
  private defaultModel: string;
  
  constructor(config: STTServiceConfig) {
    this.apiKey = config.apiKey;
    this.baseURL = config.baseURL || 'https://api.openai.com/v1';
    this.defaultModel = config.defaultModel || 'whisper-1';
  }
  
  async transcribe(audioBlob: Blob, options: STTOptions = {}): Promise<TranscriptionResult> {
    const formData = new FormData();
    formData.append('file', audioBlob, 'audio.wav');
    formData.append('model', options.model || this.defaultModel);
    
    if (options.language) {
      formData.append('language', options.language);
    }
    
    if (options.punctuation) {
      formData.append('response_format', 'verbose_json');
    }
    
    const response = await this.makeRequest('/audio/transcriptions', formData);
    
    return {
      text: response.text,
      confidence: response.confidence || 0.9,
      language: response.language || options.language || 'en',
      duration: response.duration || 0,
      words: response.words || []
    };
  }
  
  async transcribeFile(filePath: string, options: STTOptions = {}): Promise<TranscriptionResult> {
    const audioBuffer = await fs.readFile(filePath);
    const audioBlob = new Blob([audioBuffer]);
    return this.transcribe(audioBlob, options);
  }
  
  async *transcribeStream(
    audioStream: ReadableStream, 
    options: STTOptions = {}
  ): AsyncIterable<TranscriptionChunk> {
    // Placeholder for streaming transcription
    // This would implement real-time streaming STT
    const reader = audioStream.getReader();
    
    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        // Process audio chunk and yield transcription
        yield {
          text: "Streaming transcription chunk",
          isFinal: false,
          confidence: 0.8
        };
      }
    } finally {
      reader.releaseLock();
    }
  }
  
  getSupportedLanguages(): string[] {
    return [
      'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh',
      'ar', 'hi', 'tr', 'pl', 'nl', 'sv', 'da', 'no', 'fi'
    ];
  }
  
  async detectLanguage(audioBlob: Blob): Promise<string> {
    // Placeholder implementation
    // Would use language detection API or model
    return 'en';
  }
  
  private async makeRequest(endpoint: string, data: FormData): Promise<any> {
    // Placeholder implementation - to be supplemented
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: data
    });
    
    if (!response.ok) {
      throw new Error(`STT API request failed: ${response.statusText}`);
    }
    
    return response.json();
  }
}
```

## 🎛️ Voice Command Processing

### Voice Command Processor
```typescript
class VoiceCommandProcessor {
  private commands: Map<string, VoiceCommand> = new Map();
  private isListening = false;
  
  registerCommand(command: VoiceCommand): void {
    this.commands.set(command.trigger.toLowerCase(), command);
  }
  
  async processTranscription(text: string): Promise<VoiceCommandResult | null> {
    const normalizedText = text.toLowerCase().trim();
    
    for (const [trigger, command] of this.commands) {
      if (this.matchesCommand(normalizedText, trigger)) {
        try {
          const result = await command.execute(text);
          return {
            command: command.name,
            success: true,
            result
          };
        } catch (error) {
          return {
            command: command.name,
            success: false,
            error: error.message
          };
        }
      }
    }
    
    return null;
  }
  
  private matchesCommand(text: string, trigger: string): boolean {
    // Simple matching - could be enhanced with fuzzy matching
    return text.includes(trigger) || text.startsWith(trigger);
  }
}

interface VoiceCommand {
  name: string;
  trigger: string;
  description: string;
  execute: (fullText: string) => Promise<any>;
}

interface VoiceCommandResult {
  command: string;
  success: boolean;
  result?: any;
  error?: string;
}
```

### Built-in Voice Commands
```typescript
const BUILT_IN_COMMANDS: VoiceCommand[] = [
  {
    name: 'enhance-prompt',
    trigger: 'enhance this',
    description: 'Enhance the current prompt',
    execute: async (text: string) => {
      const promptText = text.replace(/^enhance this\s*/i, '');
      return await promptEngine.enhance(promptText);
    }
  },
  
  {
    name: 'save-prompt',
    trigger: 'save prompt',
    description: 'Save the current prompt',
    execute: async (text: string) => {
      const promptText = text.replace(/^save prompt\s*/i, '');
      const prompt = {
        title: `Voice Prompt ${Date.now()}`,
        content: promptText,
        tags: ['voice'],
        createdAt: new Date()
      };
      return await promptLibrary.save(prompt);
    }
  },
  
  {
    name: 'capture-screen',
    trigger: 'capture screen',
    description: 'Capture screen and extract text',
    execute: async () => {
      return await contextExtractor.captureScreen();
    }
  },
  
  {
    name: 'get-clipboard',
    trigger: 'get clipboard',
    description: 'Get clipboard content',
    execute: async () => {
      return await contextExtractor.getClipboardText();
    }
  }
];
```

## 🔧 Audio Processing

### Audio Processor
```typescript
class AudioProcessor {
  async convertFormat(
    inputBlob: Blob, 
    outputFormat: 'wav' | 'mp3' | 'ogg'
  ): Promise<Blob> {
    // Use Web Audio API for format conversion
    const arrayBuffer = await inputBlob.arrayBuffer();
    const audioContext = new AudioContext();
    const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
    
    // Convert to desired format
    switch (outputFormat) {
      case 'wav':
        return this.encodeWAV(audioBuffer);
      case 'mp3':
        return this.encodeMP3(audioBuffer);
      default:
        return inputBlob;
    }
  }
  
  async reduceNoise(audioBlob: Blob): Promise<Blob> {
    // Placeholder for noise reduction
    // Would implement noise reduction algorithms
    return audioBlob;
  }
  
  async normalizeVolume(audioBlob: Blob): Promise<Blob> {
    // Placeholder for volume normalization
    return audioBlob;
  }
  
  private encodeWAV(audioBuffer: AudioBuffer): Blob {
    const length = audioBuffer.length;
    const sampleRate = audioBuffer.sampleRate;
    const buffer = new ArrayBuffer(44 + length * 2);
    const view = new DataView(buffer);
    
    // WAV header
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };
    
    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, 1, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * 2, true);
    view.setUint16(32, 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length * 2, true);
    
    // Convert audio data
    const channelData = audioBuffer.getChannelData(0);
    let offset = 44;
    for (let i = 0; i < length; i++) {
      const sample = Math.max(-1, Math.min(1, channelData[i]));
      view.setInt16(offset, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
      offset += 2;
    }
    
    return new Blob([buffer], { type: 'audio/wav' });
  }
  
  private encodeMP3(audioBuffer: AudioBuffer): Blob {
    // Placeholder for MP3 encoding
    // Would use a library like lamejs
    throw new Error('MP3 encoding not implemented');
  }
}
```

## 🧪 Testing Strategy

### Unit Tests
```typescript
describe('VoiceModule', () => {
  let voiceModule: VoiceModule;
  let mockSTTService: jest.Mocked<STTService>;
  
  beforeEach(() => {
    mockSTTService = {
      transcribe: jest.fn(),
      transcribeFile: jest.fn(),
      transcribeStream: jest.fn(),
      getSupportedLanguages: jest.fn(),
      detectLanguage: jest.fn()
    };
    
    voiceModule = new VoiceModule(mockSTTService);
  });
  
  test('should start and stop recording', async () => {
    await voiceModule.startRecording();
    expect(voiceModule.isRecording()).toBe(true);
    
    const transcript = await voiceModule.stopRecording();
    expect(typeof transcript).toBe('string');
    expect(voiceModule.isRecording()).toBe(false);
  });
  
  test('should process voice commands', async () => {
    const commandProcessor = new VoiceCommandProcessor();
    commandProcessor.registerCommand({
      name: 'test-command',
      trigger: 'test',
      description: 'Test command',
      execute: async () => 'executed'
    });
    
    const result = await commandProcessor.processTranscription('test command');
    expect(result?.success).toBe(true);
  });
});
```
