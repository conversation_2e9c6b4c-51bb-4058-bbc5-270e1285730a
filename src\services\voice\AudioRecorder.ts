/**
 * Audio Recorder implementation
 * Handles microphone input and audio recording with real-time monitoring
 */

import {
  IAudioRecorder,
  RecordingOptions,
  RecordingState,
  AudioRecordingError
} from './types';
import { LoggerFactory } from '../../core/logger';
import { toError } from '../../core/utils/errorUtils';

const logger = LoggerFactory.getInstance().getLogger('AudioRecorder');

/**
 * Audio Recorder implementation using Web Audio API
 */
export class AudioRecorder implements IAudioRecorder {
  private mediaRecorder: MediaRecorder | null = null;
  private audioStream: MediaStream | null = null;
  private audioChunks: Blob[] = [];
  private recordingState: RecordingState;
  private audioContext: AudioContext | null = null;
  private analyser: AnalyserNode | null = null;
  private microphone: MediaStreamAudioSourceNode | null = null;
  private animationFrame: number | null = null;
  private recordingStartTime: number = 0;
  private maxDurationTimeout: NodeJS.Timeout | null = null;

  constructor() {
    this.recordingState = {
      isRecording: false,
      duration: 0,
      audioLevel: 0,
      language: 'en-US',
      confidence: 0
    };
  }

  /**
   * Initialize audio recording capabilities
   */
  async initialize(): Promise<void> {
    try {
      // Check if getUserMedia is supported
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new AudioRecordingError('getUserMedia is not supported in this browser');
      }

      // Request microphone access
      this.audioStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 16000,
          channelCount: 1
        }
      });

      // Initialize audio context for level monitoring
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      this.analyser = this.audioContext.createAnalyser();
      this.microphone = this.audioContext.createMediaStreamSource(this.audioStream);
      
      this.microphone.connect(this.analyser);
      this.analyser.fftSize = 256;

      logger.info('Audio recorder initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize audio recorder', { error: toError(error) });
      throw new AudioRecordingError('Failed to initialize audio recorder', toError(error));
    }
  }

  /**
   * Start recording audio
   */
  async startRecording(options: RecordingOptions = {}): Promise<void> {
    try {
      if (this.recordingState.isRecording) {
        throw new AudioRecordingError('Recording is already in progress');
      }

      if (!this.audioStream) {
        await this.initialize();
      }

      // Clear previous chunks
      this.audioChunks = [];
      
      // Update recording state
      this.recordingState = {
        isRecording: true,
        duration: 0,
        audioLevel: 0,
        language: options.language || 'en-US',
        confidence: 0
      };

      // Determine MIME type
      const mimeType = this.getSupportedMimeType(options.format);
      
      // Create MediaRecorder
      this.mediaRecorder = new MediaRecorder(this.audioStream!, { 
        mimeType,
        audioBitsPerSecond: this.calculateBitrate(options)
      });

      // Set up event handlers
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      this.mediaRecorder.onerror = (event) => {
        logger.error('MediaRecorder error', { error: event });
        this.stopRecording().catch(() => {});
      };

      // Start recording
      this.mediaRecorder.start(100); // Collect data every 100ms
      this.recordingStartTime = Date.now();

      // Start audio level monitoring
      this.startAudioLevelMonitoring();

      // Set up auto-stop timer if specified
      if (options.maxDuration && options.maxDuration > 0) {
        this.maxDurationTimeout = setTimeout(() => {
          if (this.recordingState.isRecording) {
            this.stopRecording().catch((error) => {
              logger.error('Auto-stop recording failed', { error });
            });
          }
        }, options.maxDuration * 1000);
      }

      logger.info('Recording started', { 
        mimeType, 
        maxDuration: options.maxDuration,
        language: options.language 
      });
    } catch (error) {
      logger.error('Failed to start recording', { error: toError(error), options });
      throw new AudioRecordingError('Failed to start recording', toError(error));
    }
  }

  /**
   * Stop recording and return audio blob
   */
  async stopRecording(): Promise<Blob> {
    return new Promise((resolve, reject) => {
      try {
        if (!this.mediaRecorder || !this.recordingState.isRecording) {
          reject(new AudioRecordingError('No active recording to stop'));
          return;
        }

        // Set up stop handler
        this.mediaRecorder.onstop = () => {
          try {
            const audioBlob = new Blob(this.audioChunks, { 
              type: this.mediaRecorder!.mimeType 
            });

            // Update recording state
            this.recordingState.isRecording = false;
            this.recordingState.duration = (Date.now() - this.recordingStartTime) / 1000;

            // Stop audio level monitoring
            this.stopAudioLevelMonitoring();

            // Clear timeout if set
            if (this.maxDurationTimeout) {
              clearTimeout(this.maxDurationTimeout);
              this.maxDurationTimeout = null;
            }

            logger.info('Recording stopped', { 
              duration: this.recordingState.duration,
              size: audioBlob.size 
            });

            resolve(audioBlob);
          } catch (error) {
            reject(new AudioRecordingError('Failed to create audio blob', toError(error)));
          }
        };

        // Stop the recorder
        this.mediaRecorder.stop();
      } catch (error) {
        reject(new AudioRecordingError('Failed to stop recording', toError(error)));
      }
    });
  }

  /**
   * Get current recording state
   */
  getRecordingState(): RecordingState {
    if (this.recordingState.isRecording) {
      this.recordingState.duration = (Date.now() - this.recordingStartTime) / 1000;
    }
    return { ...this.recordingState };
  }

  /**
   * Check if currently recording
   */
  isRecording(): boolean {
    return this.recordingState.isRecording;
  }

  /**
   * Clean up resources
   */
  cleanup(): void {
    try {
      // Stop recording if active
      if (this.recordingState.isRecording) {
        this.stopRecording().catch(() => {});
      }

      // Stop audio level monitoring
      this.stopAudioLevelMonitoring();

      // Close audio stream
      if (this.audioStream) {
        this.audioStream.getTracks().forEach(track => track.stop());
        this.audioStream = null;
      }

      // Close audio context
      if (this.audioContext && this.audioContext.state !== 'closed') {
        this.audioContext.close().catch(() => {});
        this.audioContext = null;
      }

      // Clear timeout
      if (this.maxDurationTimeout) {
        clearTimeout(this.maxDurationTimeout);
        this.maxDurationTimeout = null;
      }

      // Reset state
      this.recordingState = {
        isRecording: false,
        duration: 0,
        audioLevel: 0,
        language: 'en-US',
        confidence: 0
      };

      logger.info('Audio recorder cleaned up');
    } catch (error) {
      logger.error('Error during audio recorder cleanup', { error });
    }
  }

  /**
   * Get supported MIME type for recording
   */
  private getSupportedMimeType(format?: string): string {
    const formats = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/wav'
    ];

    // If specific format requested, try it first
    if (format) {
      const formatMimeTypes = {
        'wav': 'audio/wav',
        'mp3': 'audio/mp3',
        'ogg': 'audio/ogg'
      };
      
      const requestedMimeType = formatMimeTypes[format as keyof typeof formatMimeTypes];
      if (requestedMimeType && MediaRecorder.isTypeSupported(requestedMimeType)) {
        return requestedMimeType;
      }
    }

    // Fall back to first supported format
    for (const mimeType of formats) {
      if (MediaRecorder.isTypeSupported(mimeType)) {
        return mimeType;
      }
    }

    // Last resort
    return 'audio/webm';
  }

  /**
   * Calculate bitrate based on options
   */
  private calculateBitrate(options: RecordingOptions): number {
    const sampleRate = options.sampleRate || 16000;
    const channels = options.channels || 1;
    const bitDepth = options.bitDepth || 16;
    
    // Calculate uncompressed bitrate and apply compression factor
    const uncompressedBitrate = sampleRate * channels * bitDepth;
    const compressionFactor = 0.3; // Assume 30% of uncompressed size
    
    return Math.floor(uncompressedBitrate * compressionFactor);
  }

  /**
   * Start monitoring audio levels
   */
  private startAudioLevelMonitoring(): void {
    if (!this.analyser) return;

    const dataArray = new Uint8Array(this.analyser.frequencyBinCount);

    const updateLevel = () => {
      if (!this.recordingState.isRecording || !this.analyser) {
        return;
      }

      this.analyser.getByteFrequencyData(dataArray);
      
      // Calculate average level
      const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
      this.recordingState.audioLevel = average / 255;

      // Continue monitoring
      this.animationFrame = requestAnimationFrame(updateLevel);
    };

    updateLevel();
  }

  /**
   * Stop monitoring audio levels
   */
  private stopAudioLevelMonitoring(): void {
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }
    this.recordingState.audioLevel = 0;
  }
}
