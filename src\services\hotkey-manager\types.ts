/**
 * Hotkey Manager module type definitions
 * Global hotkey registration and management capabilities
 */

// Core interfaces
export interface IHotkeyManager {
  registerHotkey(hotkey: HotkeyDefinition): Promise<boolean>;
  unregisterHotkey(accelerator: string): Promise<boolean>;
  updateHotkey(oldAccelerator: string, newAccelerator: string): Promise<boolean>;
  registerAction(actionId: string, handler: ActionHandler): Promise<void>;
  triggerAction(actionId: string, context?: ActionContext): Promise<void>;
  getRegisteredHotkeys(): HotkeyRegistration[];
  isHotkeyAvailable(accelerator: string): boolean;
  checkConflicts(accelerator: string): Promise<ConflictResult>;
}

// Hotkey definitions
export interface HotkeyDefinition {
  accelerator: string;
  actionId: string;
  description: string;
  category: string;
  enabled: boolean;
  global: boolean;
  context?: string[];
  priority?: number;
}

export interface HotkeyRegistration {
  id: string;
  definition: HotkeyDefinition;
  registeredAt: Date;
  lastTriggered?: Date;
  triggerCount: number;
  isActive: boolean;
  platformId?: string | number;
}

// Action handling
export interface ActionHandler {
  execute: (context?: ActionContext) => Promise<void>;
  canExecute?: (context?: ActionContext) => boolean;
  description: string;
  category?: string;
}

export interface ActionContext {
  windowId?: string;
  activeElement?: string;
  clipboardContent?: string;
  selectedText?: string;
  timestamp: Date;
  hotkeyAccelerator?: string;
  metadata?: Record<string, any>;
}

// Platform adapter interface
export interface IPlatformAdapter {
  registerGlobalHotkey(accelerator: string, callback: () => void): Promise<boolean>;
  unregisterGlobalHotkey(accelerator: string): Promise<boolean>;
  isHotkeyConflict(accelerator: string): Promise<boolean>;
  getSupportedModifiers(): string[];
  getSupportedKeys(): string[];
  normalizeAccelerator(accelerator: string): string;
}

// Conflict resolution
export interface ConflictResult {
  hasConflicts: boolean;
  conflicts: ConflictInfo[];
  suggestions: string[];
  canOverride?: boolean;
}

export interface ConflictInfo {
  type: 'system' | 'application' | 'external';
  description: string;
  severity: 'low' | 'medium' | 'high';
  source?: string;
}

// Configuration
export interface HotkeyManagerConfig {
  platform: 'windows' | 'macos' | 'linux';
  enableGlobalHotkeys: boolean;
  enableConflictDetection: boolean;
  enableAnalytics: boolean;
  maxHotkeys: number;
  defaultCategory: string;
  autoResolveConflicts: boolean;
  hotkeyTimeout: number;
}

// Analytics
export interface HotkeyUsageStats {
  accelerator: string;
  actionId: string;
  totalUsage: number;
  lastUsed: Date;
  usageHistory: UsageRecord[];
  averageResponseTime?: number;
  errorCount?: number;
}

export interface UsageRecord {
  timestamp: Date;
  context?: string;
  responseTime?: number;
  success?: boolean;
}

export interface HotkeyAnalytics {
  totalHotkeys: number;
  activeHotkeys: number;
  totalTriggers: number;
  mostUsedHotkeys: HotkeyUsageStats[];
  leastUsedHotkeys: HotkeyUsageStats[];
  averageResponseTime: number;
  errorRate: number;
  lastResetTime: Date;
}

// Key parsing
export interface ParsedAccelerator {
  modifiers: string[];
  key: string;
  isValid: boolean;
  normalized: string;
}

export interface KeyMapping {
  key: string;
  code: number;
  aliases: string[];
}

// Event types
export type HotkeyEvent = 
  | 'hotkey-registered'
  | 'hotkey-unregistered'
  | 'hotkey-triggered'
  | 'hotkey-conflict'
  | 'action-executed'
  | 'error';

export interface HotkeyEventData {
  accelerator?: string;
  actionId?: string;
  context?: ActionContext;
  error?: Error;
  timestamp: Date;
}

// Built-in actions
export interface BuiltInActions {
  'show-floating-window': ActionHandler;
  'capture-screen': ActionHandler;
  'start-voice-input': ActionHandler;
  'show-prompt-library': ActionHandler;
  'quick-search': ActionHandler;
  'enhance-clipboard': ActionHandler;
  'show-help': ActionHandler;
  'quit-application': ActionHandler;
  'close-floating-window': ActionHandler;
  'execute-prompt': ActionHandler;
  'save-prompt': ActionHandler;
  'rename-prompt': ActionHandler;
}

// Import/Export
export interface HotkeyExportData {
  version: string;
  exportedAt: Date;
  hotkeys: HotkeyDefinition[];
  analytics?: HotkeyAnalytics;
}

export interface HotkeyImportOptions {
  mergeStrategy: 'replace' | 'merge' | 'skip-conflicts';
  validateConflicts: boolean;
  preserveAnalytics: boolean;
  enableImportedHotkeys: boolean;
}

export interface HotkeyImportResult {
  imported: number;
  skipped: number;
  conflicts: number;
  errors: ImportError[];
}

export interface ImportError {
  accelerator: string;
  error: string;
  suggestion?: string;
}

// Validation
export interface HotkeyValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

// Error types
export class HotkeyManagerError extends Error {
  constructor(message: string, public readonly cause?: Error) {
    super(message);
    this.name = 'HotkeyManagerError';
  }
}

export class HotkeyRegistrationError extends HotkeyManagerError {
  constructor(message: string, cause?: Error) {
    super(message, cause);
    this.name = 'HotkeyRegistrationError';
  }
}

export class HotkeyConflictError extends HotkeyManagerError {
  constructor(message: string, public readonly conflicts: ConflictInfo[], cause?: Error) {
    super(message, cause);
    this.name = 'HotkeyConflictError';
  }
}

export class ActionExecutionError extends HotkeyManagerError {
  constructor(message: string, cause?: Error) {
    super(message, cause);
    this.name = 'ActionExecutionError';
  }
}

// Platform-specific types
export interface WindowsHotkeyData {
  id: number;
  modifiers: number;
  keyCode: number;
}

export interface MacOSHotkeyData {
  id: string;
  carbonRef?: any;
  cocoaRef?: any;
}

export interface LinuxHotkeyData {
  id: string;
  x11Ref?: any;
}

// Modifier constants
export const MODIFIERS = {
  CTRL: 'Ctrl',
  ALT: 'Alt',
  SHIFT: 'Shift',
  META: process.platform === 'darwin' ? 'Cmd' : 'Win'
} as const;

export const MODIFIER_CODES = {
  windows: {
    CTRL: 0x0002,
    ALT: 0x0001,
    SHIFT: 0x0004,
    WIN: 0x0008
  },
  macos: {
    CMD: 0x0100,
    ALT: 0x0800,
    SHIFT: 0x0200,
    CTRL: 0x1000
  }
} as const;

// Key codes mapping
export const KEY_CODES = {
  // Letters
  A: 0x41, B: 0x42, C: 0x43, D: 0x44, E: 0x45, F: 0x46, G: 0x47, H: 0x48,
  I: 0x49, J: 0x4A, K: 0x4B, L: 0x4C, M: 0x4D, N: 0x4E, O: 0x4F, P: 0x50,
  Q: 0x51, R: 0x52, S: 0x53, T: 0x54, U: 0x55, V: 0x56, W: 0x57, X: 0x58,
  Y: 0x59, Z: 0x5A,
  
  // Numbers
  '0': 0x30, '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34,
  '5': 0x35, '6': 0x36, '7': 0x37, '8': 0x38, '9': 0x39,
  
  // Function keys
  F1: 0x70, F2: 0x71, F3: 0x72, F4: 0x73, F5: 0x74, F6: 0x75,
  F7: 0x76, F8: 0x77, F9: 0x78, F10: 0x79, F11: 0x7A, F12: 0x7B,
  
  // Special keys
  Space: 0x20, Enter: 0x0D, Escape: 0x1B, Tab: 0x09,
  Backspace: 0x08, Delete: 0x2E, Insert: 0x2D, Home: 0x24,
  End: 0x23, PageUp: 0x21, PageDown: 0x22,
  
  // Arrow keys
  Left: 0x25, Up: 0x26, Right: 0x27, Down: 0x28
} as const;

// Default hotkey categories
export const HOTKEY_CATEGORIES = {
  WINDOW_MANAGEMENT: 'Window Management',
  CONTEXT_EXTRACTION: 'Context Extraction',
  VOICE_INPUT: 'Voice Input',
  PROMPT_MANAGEMENT: 'Prompt Management',
  SEARCH: 'Search',
  PROMPT_ENHANCEMENT: 'Prompt Enhancement',
  HELP: 'Help',
  APPLICATION: 'Application',
  PROMPT_EXECUTION: 'Prompt Execution'
} as const;
