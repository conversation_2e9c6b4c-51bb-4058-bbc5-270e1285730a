/**
 * Log formatters for different output formats
 */

import {
  <PERSON>g<PERSON><PERSON><PERSON><PERSON>,
  Log<PERSON>ntry,
  LogLevel,
  LogContext,
  ErrorInfo
} from './types';

export class DefaultFormatter implements LogFormatter {
  format(entry: LogEntry): string {
    const timestamp = entry.timestamp.toISOString();
    const level = LogLevel[entry.level].padEnd(5);
    const context = this.formatContext(entry.context);
    const metadata = entry.metadata ? ` ${JSON.stringify(entry.metadata)}` : '';

    let message = `${timestamp} [${level}]${context} ${entry.message}${metadata}`;

    if (entry.error) {
      message += `\n${this.formatError(entry.error)}`;
    }

    if (entry.performance) {
      message += ` (${entry.performance.duration.toFixed(2)}ms)`;
    }

    return message;
  }

  private formatContext(context: LogContext): string {
    const parts: string[] = [];

    if (context.module) parts.push(`${context.module}`);
    if (context.component) parts.push(`${context.component}`);
    if (context.requestId) parts.push(`req:${context.requestId.slice(0, 8)}`);

    return parts.length > 0 ? ` [${parts.join(':')}]` : '';
  }

  private formatError(error: ErrorInfo): string {
    let formatted = `${error.name}: ${error.message}`;

    if (error.stack) {
      formatted += `\n${error.stack}`;
    }

    if (error.cause) {
      formatted += `\nCaused by: ${this.formatError(error.cause)}`;
    }

    return formatted;
  }
}

export class JSONFormatter implements LogFormatter {
  format(entry: LogEntry): string {
    return JSON.stringify({
      timestamp: entry.timestamp.toISOString(),
      level: LogLevel[entry.level],
      message: entry.message,
      context: entry.context,
      metadata: entry.metadata,
      error: entry.error,
      performance: entry.performance
    });
  }
}

export class CompactFormatter implements LogFormatter {
  format(entry: LogEntry): string {
    const timestamp = entry.timestamp.toISOString().slice(11, 23); // HH:mm:ss.SSS
    const level = LogLevel[entry.level].charAt(0); // First letter only
    const module = entry.context.module ? `[${entry.context.module}]` : '';
    
    let message = `${timestamp} ${level}${module} ${entry.message}`;

    if (entry.performance) {
      message += ` (${entry.performance.duration.toFixed(1)}ms)`;
    }

    return message;
  }
}
