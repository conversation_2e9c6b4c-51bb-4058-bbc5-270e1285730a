import { app, BrowserWindow, ipc<PERSON>ain } from 'electron';
import * as path from 'path';
import { IPCRouter } from '../shared/ipc/IPCRouter';
import { IPCHandlers } from '../shared/ipc/IPCHandlers';
import { IPCEventEmitter, EventBroadcaster } from '../shared/ipc/IPCEventEmitter';
import { logger } from '../core/logger';

// Enable live reload for Electron in development
if (process.env.NODE_ENV === 'development') {
  require('electron-reload')(__dirname, {
    electron: path.join(__dirname, '..', 'node_modules', '.bin', 'electron'),
    hardResetMethod: 'exit'
  });
}

class MainProcess {
  private mainWindow: BrowserWindow | null = null;
  private ipcRouter: IPCRouter;
  private ipcHandlers: IPCHandlers;
  private eventEmitter: IPCEventEmitter;
  private eventBroadcaster: EventBroadcaster;

  constructor() {
    this.initializeIPC();
    this.initializeApp();
  }

  private initializeIPC(): void {
    // Initialize IPC components
    this.ipcRouter = new IPCRouter({
      enableValidation: true,
      enableMiddleware: true,
      enableMetrics: true,
      enableLogging: true,
      timeout: 30000
    });

    this.ipcHandlers = new IPCHandlers(this.ipcRouter);
    this.eventEmitter = new IPCEventEmitter();
    this.eventBroadcaster = new EventBroadcaster(this.eventEmitter);

    // Register all IPC handlers
    this.ipcHandlers.registerAllHandlers();

    logger.info('IPC system initialized');
  }

  private initializeApp(): void {
    // Handle app ready event
    app.whenReady().then(() => {
      this.createMainWindow();
      this.setupComprehensiveIPC();

      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createMainWindow();
        }
      });
    });

    // Handle window closed events
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    // Security: Prevent new window creation
    app.on('web-contents-created', (_event, contents) => {
      contents.setWindowOpenHandler(() => {
        return { action: 'deny' };
      });
    });
  }

  private createMainWindow(): void {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      show: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js'),
        sandbox: false,
      },
    });

    // Load the renderer
    if (process.env.NODE_ENV === 'development') {
      this.mainWindow.loadURL('http://localhost:3000');
      this.mainWindow.webContents.openDevTools();
    } else {
      this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    }

    // Show window when ready
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show();

      // Set main window reference in IPC handlers
      this.ipcHandlers.setMainWindow(this.mainWindow!);
    });

    // Handle window closed
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });
  }

  private setupComprehensiveIPC(): void {
    // Set up comprehensive IPC routing
    ipcMain.handle = (channel: string, listener: any) => {
      const originalHandle = ipcMain.handle.bind(ipcMain);

      // Route through our IPC router for validation and middleware
      return originalHandle(channel, async (event, ...args) => {
        try {
          return await this.ipcRouter.routeMessage(channel, args[0], event);
        } catch (error) {
          logger.error('IPC handler error', { channel, error });
          throw error;
        }
      });
    };

    // Keep basic handlers for backward compatibility
    ipcMain.handle('app:get-version', () => {
      return app.getVersion();
    });

    ipcMain.handle('app:get-platform', () => {
      return process.platform;
    });

    // Legacy window handlers (will be replaced by comprehensive handlers)
    ipcMain.handle('window:minimize', () => {
      this.mainWindow?.minimize();
    });

    ipcMain.handle('window:maximize', () => {
      if (this.mainWindow?.isMaximized()) {
        this.mainWindow.unmaximize();
      } else {
        this.mainWindow?.maximize();
      }
    });

    ipcMain.handle('window:close', () => {
      this.mainWindow?.close();
    });

    logger.info('Comprehensive IPC system set up');
  }

  /**
   * Get IPC router instance
   */
  getIPCRouter(): IPCRouter {
    return this.ipcRouter;
  }

  /**
   * Get event broadcaster instance
   */
  getEventBroadcaster(): EventBroadcaster {
    return this.eventBroadcaster;
  }
}

// Initialize the main process
new MainProcess();
