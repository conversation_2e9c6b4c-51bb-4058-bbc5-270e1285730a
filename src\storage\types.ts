/**
 * Storage module type definitions
 * Database interfaces and types for SQLite storage
 */

// Database configuration
export interface DatabaseConfig {
  readonly path: string;
  readonly encryption: {
    readonly enabled: boolean;
    readonly key?: string;
  };
  readonly performance: {
    readonly journalMode: 'DELETE' | 'TRUNCATE' | 'PERSIST' | 'MEMORY' | 'WAL';
    readonly synchronous: 'OFF' | 'NORMAL' | 'FULL' | 'EXTRA';
    readonly cacheSize: number;
    readonly mmapSize: number;
  };
  readonly backup: {
    readonly enabled: boolean;
    readonly interval: number; // minutes
    readonly maxBackups: number;
    readonly compress?: boolean;
  };
}

// Query filter interface
export interface QueryFilter {
  readonly [key: string]: any;
  readonly orderBy?: string;
  readonly orderDirection?: 'ASC' | 'DESC';
  readonly limit?: number;
  readonly offset?: number;
}

// Database transaction interface
export interface Transaction {
  readonly id: string;
  readonly startTime: Date;
  commit(): Promise<void>;
  rollback(): Promise<void>;
  isActive(): boolean;
}

// Migration interface
export interface Migration {
  readonly version: number;
  readonly description: string;
  readonly checksum?: string;
  up(db: any): Promise<void>;
  down(db: any): Promise<void>;
}

// Schema version record
export interface SchemaVersion {
  readonly version: number;
  readonly description: string;
  readonly appliedAt: Date;
  readonly checksum?: string;
}

// Database connection interface
export interface DatabaseConnection {
  run(sql: string, params?: any[]): Promise<{ lastID?: number; changes: number }>;
  get<T = any>(sql: string, params?: any[]): Promise<T | undefined>;
  all<T = any>(sql: string, params?: any[]): Promise<T[]>;
  exec(sql: string): Promise<void>;
  prepare(sql: string): Promise<PreparedStatement>;
  close(): Promise<void>;
  backup(destination: string): Promise<void>;
  transaction<T>(fn: (tx: Transaction) => Promise<T>): Promise<T>;
}

// Prepared statement interface
export interface PreparedStatement {
  run(params?: any[]): Promise<{ lastID?: number; changes: number }>;
  get<T = any>(params?: any[]): Promise<T | undefined>;
  all<T = any>(params?: any[]): Promise<T[]>;
  finalize(): Promise<void>;
}

// Repository interface
export interface Repository<T> {
  save(entity: T): Promise<string>;
  findById(id: string): Promise<T | null>;
  findAll(filter?: QueryFilter): Promise<T[]>;
  update(id: string, updates: Partial<T>): Promise<void>;
  delete(id: string): Promise<void>;
  count(filter?: QueryFilter): Promise<number>;
  exists(id: string): Promise<boolean>;
}

// Storage manager interface
export interface StorageManager {
  initialize(): Promise<void>;
  close(): Promise<void>;
  getConnection(): DatabaseConnection;
  transaction<T>(fn: (tx: Transaction) => Promise<T>): Promise<T>;
  backup(destination?: string): Promise<string>;
  restore(source: string): Promise<void>;
  vacuum(): Promise<void>;
  getStats(): Promise<DatabaseStats>;
}

// Database statistics
export interface DatabaseStats {
  readonly size: number;
  readonly pageCount: number;
  readonly pageSize: number;
  readonly freePages: number;
  readonly schemaVersion: number;
  readonly userVersion: number;
  readonly tables: TableStats[];
}

// Table statistics
export interface TableStats {
  readonly name: string;
  readonly rowCount: number;
  readonly size: number;
  readonly indexes: IndexStats[];
}

// Index statistics
export interface IndexStats {
  readonly name: string;
  readonly unique: boolean;
  readonly columns: string[];
}

// Backup metadata
export interface BackupMetadata {
  readonly path: string;
  readonly size: number;
  readonly createdAt: Date;
  readonly schemaVersion: number;
  readonly compressed: boolean;
  readonly checksum: string;
}

// Query builder interface
export interface QueryBuilder {
  select(columns?: string[]): QueryBuilder;
  from(table: string): QueryBuilder;
  where(condition: string, ...params: any[]): QueryBuilder;
  whereIn(column: string, values: any[]): QueryBuilder;
  whereNotNull(column: string): QueryBuilder;
  whereNull(column: string): QueryBuilder;
  join(table: string, condition: string): QueryBuilder;
  leftJoin(table: string, condition: string): QueryBuilder;
  orderBy(column: string, direction?: 'ASC' | 'DESC'): QueryBuilder;
  groupBy(columns: string[]): QueryBuilder;
  having(condition: string, ...params: any[]): QueryBuilder;
  limit(count: number): QueryBuilder;
  offset(count: number): QueryBuilder;
  build(): { sql: string; params: any[] };
  execute<T = any>(): Promise<T[]>;
  first<T = any>(): Promise<T | undefined>;
  count(): Promise<number>;
}

// Database entity base interface
export interface BaseEntity {
  readonly id: string;
  readonly createdAt: Date;
  readonly updatedAt: Date;
}

// Prompt entity (from data models)
export interface PromptEntity extends BaseEntity {
  readonly title: string;
  readonly content: string;
  readonly description?: string;
  readonly categoryId: string;
  readonly tags: string; // JSON array
  readonly variables: string; // JSON array
  readonly metadata: string; // JSON object
  readonly version: number;
  readonly parentId?: string;
  readonly isTemplate: boolean;
  readonly isFavorite: boolean;
  readonly isArchived: boolean;
  readonly usageCount: number;
  readonly lastUsedAt?: Date;
  readonly createdBy: string;
}

// Category entity
export interface CategoryEntity extends BaseEntity {
  readonly name: string;
  readonly description?: string;
  readonly parentId?: string;
  readonly color?: string;
  readonly icon?: string;
  readonly sortOrder: number;
  readonly promptCount: number;
  readonly isSystem: boolean;
}

// Tag entity
export interface TagEntity {
  readonly name: string;
  readonly color?: string;
  readonly description?: string;
  readonly usageCount: number;
  readonly createdAt: Date;
  readonly lastUsedAt?: Date;
}

// Settings entity
export interface SettingsEntity {
  readonly key: string;
  readonly value: string;
  readonly valueType: 'string' | 'number' | 'boolean' | 'json';
  readonly category: string;
  readonly description?: string;
  readonly isSensitive: boolean;
  readonly createdAt: Date;
  readonly updatedAt: Date;
}

// Usage analytics entity
export interface UsageAnalyticsEntity extends BaseEntity {
  readonly promptId: string;
  readonly sessionId?: string;
  readonly usedAt: Date;
  readonly contextType: string;
  readonly enhancementUsed: boolean;
  readonly rating?: number;
  readonly success?: boolean;
  readonly executionTimeMs?: number;
  readonly tokensUsed?: number;
  readonly cost?: number;
  readonly metadata?: string; // JSON object
}

// Audit log entity
export interface AuditLogEntity extends BaseEntity {
  readonly action: string;
  readonly resourceType: string;
  readonly resourceId?: string;
  readonly oldValues?: string; // JSON object
  readonly newValues?: string; // JSON object
  readonly userId: string;
  readonly sessionId?: string;
  readonly ipAddress?: string;
  readonly userAgent?: string;
  readonly timestamp: Date;
}

// Error log entity
export interface ErrorLogEntity extends BaseEntity {
  readonly errorType: string;
  readonly errorMessage: string;
  readonly errorStack?: string;
  readonly component?: string;
  readonly severity: 'warning' | 'error' | 'critical';
  readonly sessionId?: string;
  readonly userAction?: string;
  readonly appVersion?: string;
  readonly osPlatform?: string;
  readonly timestamp: Date;
  readonly resolved: boolean;
  readonly resolutionNotes?: string;
}

// Performance metrics entity
export interface PerformanceMetricsEntity extends BaseEntity {
  readonly metricName: string;
  readonly metricValue: number;
  readonly metricUnit?: string;
  readonly component?: string;
  readonly sessionId?: string;
  readonly recordedAt: Date;
  readonly metadata?: string; // JSON object
}

// User session entity
export interface UserSessionEntity extends BaseEntity {
  readonly startedAt: Date;
  readonly endedAt?: Date;
  readonly durationMs?: number;
  readonly promptsUsed: number;
  readonly voiceCommands: number;
  readonly screenCaptures: number;
  readonly errorsEncountered: number;
  readonly appVersion?: string;
  readonly osPlatform?: string;
  readonly metadata?: string; // JSON object
}

// Prompt version entity
export interface PromptVersionEntity extends BaseEntity {
  readonly promptId: string;
  readonly versionNumber: number;
  readonly title: string;
  readonly content: string;
  readonly description?: string;
  readonly changesSummary?: string;
  readonly createdBy: string;
}

// User entity for database storage
export interface UserEntity extends BaseEntity {
  readonly username?: string;
  readonly email?: string;
  readonly displayName?: string;
  readonly avatar?: string;
  readonly preferences: string; // JSON serialized UserPreferences
  readonly subscription?: string; // JSON serialized Subscription
  readonly metadata: string; // JSON serialized UserMetadata
  readonly isActive: boolean;
  readonly totalPrompts: number;
}

// Session entity for database storage
export interface SessionEntity extends BaseEntity {
  readonly userId?: string;
  readonly startedAt: Date;
  readonly endedAt?: Date;
  readonly activities: string; // JSON serialized SessionActivity[]
  readonly metadata: string; // JSON serialized SessionMetadata
  readonly duration: number; // milliseconds
  readonly isActive: boolean;
  readonly promptsUsed: number;
  readonly errorsEncountered: number;
}

// Configuration entity for database storage
export interface ConfigEntity extends BaseEntity {
  readonly key: string;
  readonly value: string; // JSON serialized value
  readonly type: string; // ConfigSettingType
  readonly source: string; // ConfigSource
  readonly encrypted: boolean;
  readonly valid: boolean;
}

// Error log entity for database storage
export interface ErrorEntity extends BaseEntity {
  readonly type: string; // ErrorType
  readonly code: string;
  readonly message: string;
  readonly stack?: string;
  readonly context: string; // JSON serialized ErrorContext
  readonly metadata: string; // JSON serialized ErrorMetadata
  readonly severity: string; // ErrorSeverity
  readonly resolved: boolean;
  readonly timestamp: Date;
}

// Analytics event entity for database storage
export interface AnalyticsEntity extends BaseEntity {
  readonly name: string;
  readonly category: string; // EventCategory
  readonly properties: string; // JSON serialized properties
  readonly userId?: string;
  readonly sessionId?: string;
  readonly anonymousId?: string;
  readonly context: string; // JSON serialized EventContext
  readonly timestamp: Date;
}
