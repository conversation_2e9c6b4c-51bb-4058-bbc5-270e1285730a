# Security Module Specification

## 🎯 Purpose

The Security module provides comprehensive security features for the PromptPilot Desktop application, including data encryption, secure storage, API key management, and privacy protection mechanisms.

## 📋 Responsibilities

### Data Protection
- Local data encryption and decryption
- Secure API key storage and management
- Sensitive data sanitization
- Memory protection and cleanup

### Authentication & Authorization
- User authentication mechanisms
- Session management
- Access control and permissions
- Security policy enforcement

### Privacy & Compliance
- Data anonymization and pseudonymization
- Privacy-preserving analytics
- GDPR compliance features
- Audit logging and monitoring

## 🏗️ Architecture

### Core Security Manager
```typescript
class SecurityManager {
  private encryptionService: EncryptionService;
  private keyManager: KeyManager;
  private authService: AuthenticationService;
  private privacyManager: PrivacyManager;
  
  async initialize(config: SecurityConfig): Promise<void>;
  async encrypt(data: string | Buffer, context?: EncryptionContext): Promise<EncryptedData>;
  async decrypt(encryptedData: EncryptedData, context?: EncryptionContext): Promise<string | Buffer>;
  async secureStore(key: string, value: any): Promise<void>;
  async secureRetrieve(key: string): Promise<any>;
  async sanitizeData(data: any): Promise<any>;
  
  generateSecureToken(length?: number): string;
  hashPassword(password: string): Promise<string>;
  verifyPassword(password: string, hash: string): Promise<boolean>;
  
  isSecureContext(): boolean;
  enforceSecurityPolicy(operation: string, context: SecurityContext): Promise<boolean>;
}
```

### Security Configuration
```typescript
interface SecurityConfig {
  encryption: {
    algorithm: 'AES-256-GCM' | 'ChaCha20-Poly1305';
    keyDerivation: 'PBKDF2' | 'Argon2' | 'scrypt';
    iterations: number;
    saltLength: number;
  };
  storage: {
    encryptSensitiveData: boolean;
    secureDelete: boolean;
    memoryProtection: boolean;
  };
  authentication: {
    enabled: boolean;
    method: 'password' | 'biometric' | 'token';
    sessionTimeout: number;
    maxAttempts: number;
  };
  privacy: {
    dataMinimization: boolean;
    anonymizeAnalytics: boolean;
    autoCleanup: boolean;
    retentionPeriod: number;
  };
}

interface EncryptionContext {
  purpose: 'storage' | 'transmission' | 'backup';
  sensitivity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  metadata?: Record<string, any>;
}

interface EncryptedData {
  algorithm: string;
  iv: string;
  authTag: string;
  data: string;
  metadata: EncryptionMetadata;
}

interface EncryptionMetadata {
  timestamp: Date;
  keyId: string;
  version: number;
  context?: EncryptionContext;
}
```

## 🔐 Encryption Service

### AES-GCM Encryption
```typescript
class AESGCMEncryptionService implements EncryptionService {
  private algorithm = 'aes-256-gcm';
  private keyLength = 32; // 256 bits
  private ivLength = 16; // 128 bits
  private tagLength = 16; // 128 bits
  
  async encrypt(data: string | Buffer, key: Buffer, context?: EncryptionContext): Promise<EncryptedData> {
    try {
      const iv = crypto.randomBytes(this.ivLength);
      const cipher = crypto.createCipher(this.algorithm, key, { iv });
      
      const dataBuffer = typeof data === 'string' ? Buffer.from(data, 'utf8') : data;
      
      let encrypted = cipher.update(dataBuffer);
      encrypted = Buffer.concat([encrypted, cipher.final()]);
      
      const authTag = cipher.getAuthTag();
      
      return {
        algorithm: this.algorithm,
        iv: iv.toString('base64'),
        authTag: authTag.toString('base64'),
        data: encrypted.toString('base64'),
        metadata: {
          timestamp: new Date(),
          keyId: this.generateKeyId(key),
          version: 1,
          context
        }
      };
    } catch (error) {
      throw new SecurityError('Encryption failed', error);
    }
  }
  
  async decrypt(encryptedData: EncryptedData, key: Buffer): Promise<Buffer> {
    try {
      const iv = Buffer.from(encryptedData.iv, 'base64');
      const authTag = Buffer.from(encryptedData.authTag, 'base64');
      const data = Buffer.from(encryptedData.data, 'base64');
      
      const decipher = crypto.createDecipher(encryptedData.algorithm, key, { iv });
      decipher.setAuthTag(authTag);
      
      let decrypted = decipher.update(data);
      decrypted = Buffer.concat([decrypted, decipher.final()]);
      
      return decrypted;
    } catch (error) {
      throw new SecurityError('Decryption failed', error);
    }
  }
  
  async generateKey(password?: string, salt?: Buffer): Promise<Buffer> {
    if (password) {
      const actualSalt = salt || crypto.randomBytes(32);
      return crypto.pbkdf2Sync(password, actualSalt, 100000, this.keyLength, 'sha256');
    } else {
      return crypto.randomBytes(this.keyLength);
    }
  }
  
  private generateKeyId(key: Buffer): string {
    return crypto.createHash('sha256').update(key).digest('hex').slice(0, 16);
  }
}
```

### Key Management
```typescript
class KeyManager {
  private keys: Map<string, KeyInfo> = new Map();
  private masterKey: Buffer | null = null;
  private keyStore: SecureKeyStore;
  
  constructor(keyStore: SecureKeyStore) {
    this.keyStore = keyStore;
  }
  
  async initialize(masterPassword?: string): Promise<void> {
    if (masterPassword) {
      this.masterKey = await this.deriveMasterKey(masterPassword);
    } else {
      this.masterKey = await this.loadOrGenerateMasterKey();
    }
    
    await this.loadKeys();
  }
  
  async createKey(purpose: string, context?: KeyContext): Promise<string> {
    const keyId = this.generateKeyId();
    const key = crypto.randomBytes(32);
    
    const keyInfo: KeyInfo = {
      id: keyId,
      purpose,
      algorithm: 'AES-256-GCM',
      createdAt: new Date(),
      lastUsed: new Date(),
      usageCount: 0,
      context
    };
    
    await this.storeKey(keyId, key, keyInfo);
    this.keys.set(keyId, keyInfo);
    
    return keyId;
  }
  
  async getKey(keyId: string): Promise<Buffer | null> {
    const keyInfo = this.keys.get(keyId);
    if (!keyInfo) return null;
    
    const encryptedKey = await this.keyStore.retrieve(keyId);
    if (!encryptedKey) return null;
    
    const key = await this.decryptKey(encryptedKey);
    
    // Update usage statistics
    keyInfo.lastUsed = new Date();
    keyInfo.usageCount++;
    
    return key;
  }
  
  async rotateKey(keyId: string): Promise<string> {
    const oldKeyInfo = this.keys.get(keyId);
    if (!oldKeyInfo) {
      throw new SecurityError(`Key ${keyId} not found`);
    }
    
    const newKeyId = await this.createKey(oldKeyInfo.purpose, oldKeyInfo.context);
    
    // Mark old key as deprecated
    oldKeyInfo.deprecated = true;
    oldKeyInfo.replacedBy = newKeyId;
    
    return newKeyId;
  }
  
  async deleteKey(keyId: string): Promise<void> {
    await this.keyStore.delete(keyId);
    this.keys.delete(keyId);
  }
  
  private async deriveMasterKey(password: string): Promise<Buffer> {
    const salt = await this.getOrCreateSalt();
    return crypto.pbkdf2Sync(password, salt, 100000, 32, 'sha256');
  }
  
  private async loadOrGenerateMasterKey(): Promise<Buffer> {
    // In a real implementation, this would use OS keychain or hardware security module
    const existingKey = await this.keyStore.retrieveMasterKey();
    if (existingKey) {
      return existingKey;
    }
    
    const newKey = crypto.randomBytes(32);
    await this.keyStore.storeMasterKey(newKey);
    return newKey;
  }
  
  private async storeKey(keyId: string, key: Buffer, keyInfo: KeyInfo): Promise<void> {
    const encryptedKey = await this.encryptKey(key);
    await this.keyStore.store(keyId, encryptedKey, keyInfo);
  }
  
  private async encryptKey(key: Buffer): Promise<EncryptedData> {
    if (!this.masterKey) {
      throw new SecurityError('Master key not initialized');
    }
    
    const encryptionService = new AESGCMEncryptionService();
    return encryptionService.encrypt(key, this.masterKey);
  }
  
  private async decryptKey(encryptedKey: EncryptedData): Promise<Buffer> {
    if (!this.masterKey) {
      throw new SecurityError('Master key not initialized');
    }
    
    const encryptionService = new AESGCMEncryptionService();
    return encryptionService.decrypt(encryptedKey, this.masterKey);
  }
  
  private generateKeyId(): string {
    return crypto.randomUUID();
  }
  
  private async getOrCreateSalt(): Promise<Buffer> {
    // Implementation would store/retrieve salt securely
    return crypto.randomBytes(32);
  }
  
  private async loadKeys(): Promise<void> {
    const keyInfos = await this.keyStore.listKeys();
    for (const keyInfo of keyInfos) {
      this.keys.set(keyInfo.id, keyInfo);
    }
  }
}

interface KeyInfo {
  id: string;
  purpose: string;
  algorithm: string;
  createdAt: Date;
  lastUsed: Date;
  usageCount: number;
  deprecated?: boolean;
  replacedBy?: string;
  context?: KeyContext;
}

interface KeyContext {
  userId?: string;
  application?: string;
  environment?: string;
  [key: string]: any;
}
```

## 🔒 Secure Storage

### Secure Key Store
```typescript
class SecureKeyStore {
  private storePath: string;
  private encryptionService: EncryptionService;
  
  constructor(storePath: string, encryptionService: EncryptionService) {
    this.storePath = storePath;
    this.encryptionService = encryptionService;
  }
  
  async store(keyId: string, encryptedKey: EncryptedData, keyInfo: KeyInfo): Promise<void> {
    const keyData = {
      encryptedKey,
      keyInfo
    };
    
    const filePath = path.join(this.storePath, `${keyId}.key`);
    const serialized = JSON.stringify(keyData);
    
    await fs.mkdir(path.dirname(filePath), { recursive: true });
    await fs.writeFile(filePath, serialized, { mode: 0o600 }); // Owner read/write only
  }
  
  async retrieve(keyId: string): Promise<EncryptedData | null> {
    try {
      const filePath = path.join(this.storePath, `${keyId}.key`);
      const content = await fs.readFile(filePath, 'utf8');
      const keyData = JSON.parse(content);
      return keyData.encryptedKey;
    } catch (error) {
      if (error.code === 'ENOENT') {
        return null;
      }
      throw new SecurityError('Failed to retrieve key', error);
    }
  }
  
  async delete(keyId: string): Promise<void> {
    const filePath = path.join(this.storePath, `${keyId}.key`);
    
    try {
      // Secure deletion: overwrite file before deletion
      const stats = await fs.stat(filePath);
      const randomData = crypto.randomBytes(stats.size);
      await fs.writeFile(filePath, randomData);
      await fs.unlink(filePath);
    } catch (error) {
      if (error.code !== 'ENOENT') {
        throw new SecurityError('Failed to delete key', error);
      }
    }
  }
  
  async listKeys(): Promise<KeyInfo[]> {
    try {
      const files = await fs.readdir(this.storePath);
      const keyFiles = files.filter(file => file.endsWith('.key'));
      
      const keyInfos: KeyInfo[] = [];
      for (const file of keyFiles) {
        try {
          const filePath = path.join(this.storePath, file);
          const content = await fs.readFile(filePath, 'utf8');
          const keyData = JSON.parse(content);
          keyInfos.push(keyData.keyInfo);
        } catch (error) {
          console.warn(`Failed to read key file ${file}:`, error);
        }
      }
      
      return keyInfos;
    } catch (error) {
      if (error.code === 'ENOENT') {
        return [];
      }
      throw new SecurityError('Failed to list keys', error);
    }
  }
  
  async storeMasterKey(key: Buffer): Promise<void> {
    // In a real implementation, this would use OS keychain
    // For now, we'll store it in a protected file
    const filePath = path.join(this.storePath, '.master');
    await fs.mkdir(path.dirname(filePath), { recursive: true });
    await fs.writeFile(filePath, key, { mode: 0o600 });
  }
  
  async retrieveMasterKey(): Promise<Buffer | null> {
    try {
      const filePath = path.join(this.storePath, '.master');
      return await fs.readFile(filePath);
    } catch (error) {
      if (error.code === 'ENOENT') {
        return null;
      }
      throw new SecurityError('Failed to retrieve master key', error);
    }
  }
}
```

## 🛡️ Privacy Manager

### Data Anonymization
```typescript
class PrivacyManager {
  private anonymizers: Map<string, DataAnonymizer> = new Map();
  private retentionPolicies: Map<string, RetentionPolicy> = new Map();
  
  constructor() {
    this.registerDefaultAnonymizers();
    this.registerDefaultRetentionPolicies();
  }
  
  async anonymizeData(data: any, dataType: string): Promise<any> {
    const anonymizer = this.anonymizers.get(dataType);
    if (!anonymizer) {
      return data; // No anonymization rules for this data type
    }
    
    return anonymizer.anonymize(data);
  }
  
  async pseudonymizeData(data: any, dataType: string, userId: string): Promise<any> {
    const anonymizer = this.anonymizers.get(dataType);
    if (!anonymizer) {
      return data;
    }
    
    return anonymizer.pseudonymize(data, userId);
  }
  
  async sanitizeForAnalytics(data: any): Promise<any> {
    const sanitized = { ...data };
    
    // Remove or hash personally identifiable information
    if (sanitized.userId) {
      sanitized.userId = this.hashUserId(sanitized.userId);
    }
    
    if (sanitized.email) {
      delete sanitized.email;
    }
    
    if (sanitized.ipAddress) {
      sanitized.ipAddress = this.anonymizeIP(sanitized.ipAddress);
    }
    
    // Remove sensitive prompt content
    if (sanitized.promptContent) {
      sanitized.promptContent = '[REDACTED]';
    }
    
    return sanitized;
  }
  
  async applyRetentionPolicy(dataType: string): Promise<void> {
    const policy = this.retentionPolicies.get(dataType);
    if (!policy) return;
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - policy.retentionDays);
    
    await policy.cleanup(cutoffDate);
  }
  
  private registerDefaultAnonymizers(): void {
    this.anonymizers.set('prompt', new PromptAnonymizer());
    this.anonymizers.set('usage', new UsageAnonymizer());
    this.anonymizers.set('error', new ErrorAnonymizer());
  }
  
  private registerDefaultRetentionPolicies(): void {
    this.retentionPolicies.set('logs', {
      retentionDays: 30,
      cleanup: async (cutoffDate: Date) => {
        // Implementation to clean up old logs
      }
    });
    
    this.retentionPolicies.set('analytics', {
      retentionDays: 90,
      cleanup: async (cutoffDate: Date) => {
        // Implementation to clean up old analytics data
      }
    });
  }
  
  private hashUserId(userId: string): string {
    return crypto.createHash('sha256').update(userId).digest('hex').slice(0, 16);
  }
  
  private anonymizeIP(ip: string): string {
    // For IPv4, zero out the last octet
    if (ip.includes('.')) {
      const parts = ip.split('.');
      parts[3] = '0';
      return parts.join('.');
    }
    
    // For IPv6, zero out the last 64 bits
    if (ip.includes(':')) {
      const parts = ip.split(':');
      return parts.slice(0, 4).join(':') + '::';
    }
    
    return '[ANONYMIZED]';
  }
}

interface DataAnonymizer {
  anonymize(data: any): Promise<any>;
  pseudonymize(data: any, userId: string): Promise<any>;
}

interface RetentionPolicy {
  retentionDays: number;
  cleanup(cutoffDate: Date): Promise<void>;
}

class PromptAnonymizer implements DataAnonymizer {
  async anonymize(prompt: any): Promise<any> {
    const anonymized = { ...prompt };
    
    // Remove or redact sensitive content
    if (anonymized.content) {
      anonymized.content = this.redactSensitiveContent(anonymized.content);
    }
    
    // Remove user-identifying metadata
    delete anonymized.userId;
    delete anonymized.userEmail;
    delete anonymized.deviceId;
    
    return anonymized;
  }
  
  async pseudonymize(prompt: any, userId: string): Promise<any> {
    const pseudonymized = { ...prompt };
    
    // Replace user ID with pseudonym
    if (pseudonymized.userId) {
      pseudonymized.userId = this.generatePseudonym(userId);
    }
    
    return pseudonymized;
  }
  
  private redactSensitiveContent(content: string): string {
    // Redact common sensitive patterns
    return content
      .replace(/\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g, '[CREDIT_CARD]')
      .replace(/\b\d{3}-\d{2}-\d{4}\b/g, '[SSN]')
      .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL]')
      .replace(/\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g, '[PHONE]');
  }
  
  private generatePseudonym(userId: string): string {
    return crypto.createHash('sha256').update(userId + 'salt').digest('hex').slice(0, 16);
  }
}
```

## 🧪 Testing Strategy

### Unit Tests
```typescript
describe('SecurityManager', () => {
  let securityManager: SecurityManager;
  let mockEncryptionService: jest.Mocked<EncryptionService>;
  
  beforeEach(() => {
    mockEncryptionService = {
      encrypt: jest.fn(),
      decrypt: jest.fn(),
      generateKey: jest.fn()
    };
    
    securityManager = new SecurityManager(mockEncryptionService);
  });
  
  test('should encrypt and decrypt data', async () => {
    const testData = 'sensitive information';
    const mockEncrypted: EncryptedData = {
      algorithm: 'aes-256-gcm',
      iv: 'mock-iv',
      authTag: 'mock-tag',
      data: 'encrypted-data',
      metadata: {
        timestamp: new Date(),
        keyId: 'test-key',
        version: 1
      }
    };
    
    mockEncryptionService.encrypt.mockResolvedValue(mockEncrypted);
    mockEncryptionService.decrypt.mockResolvedValue(Buffer.from(testData));
    
    const encrypted = await securityManager.encrypt(testData);
    const decrypted = await securityManager.decrypt(encrypted);
    
    expect(encrypted).toEqual(mockEncrypted);
    expect(decrypted.toString()).toBe(testData);
  });
  
  test('should anonymize sensitive data', async () => {
    const privacyManager = new PrivacyManager();
    const sensitiveData = {
      userId: 'user123',
      email: '<EMAIL>',
      promptContent: 'My credit card number is 1234-5678-9012-3456'
    };
    
    const anonymized = await privacyManager.sanitizeForAnalytics(sensitiveData);
    
    expect(anonymized.email).toBeUndefined();
    expect(anonymized.userId).not.toBe('user123');
    expect(anonymized.promptContent).toBe('[REDACTED]');
  });
});
```
