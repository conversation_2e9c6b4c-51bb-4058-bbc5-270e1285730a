/**
 * Floating Window module type definitions
 * Floating window management and UI capabilities
 */

// Core interfaces
export interface IFloatingWindow {
  show(options?: WindowShowOptions): Promise<void>;
  hide(): Promise<void>;
  toggle(): Promise<void>;
  isVisible(): boolean;
  setPosition(x: number, y: number): Promise<void>;
  setSize(width: number, height: number): Promise<void>;
  setContent(content: WindowContent): Promise<void>;
  focus(): Promise<void>;
  minimize(): Promise<void>;
  maximize(): Promise<void>;
  restore(): Promise<void>;
  close(): Promise<void>;
}

// Window configuration
export interface FloatingWindowConfig {
  window: {
    width: number;
    height: number;
    minWidth: number;
    minHeight: number;
    maxWidth?: number;
    maxHeight?: number;
    x?: number;
    y?: number;
    alwaysOnTop: boolean;
    resizable: boolean;
    movable: boolean;
    minimizable: boolean;
    maximizable: boolean;
    closable: boolean;
    skipTaskbar: boolean;
    transparent: boolean;
    opacity: number;
  };
  ui: {
    theme: 'light' | 'dark' | 'auto';
    accentColor: string;
    fontSize: number;
    fontFamily: string;
    borderRadius: number;
    showTitleBar: boolean;
    showControls: boolean;
  };
  behavior: {
    autoHide: boolean;
    autoHideDelay: number;
    stayOnTop: boolean;
    followCursor: boolean;
    snapToEdges: boolean;
    fadeInOut: boolean;
    rememberPosition: boolean;
    rememberSize: boolean;
  };
  hotkeys: {
    show: string;
    hide: string;
    toggle: string;
    focus: string;
  };
}

// Window show options
export interface WindowShowOptions {
  position?: WindowPosition;
  size?: WindowSize;
  animation?: WindowAnimation;
  focus?: boolean;
  center?: boolean;
}

export interface WindowPosition {
  x: number;
  y: number;
}

export interface WindowSize {
  width: number;
  height: number;
}

export interface WindowAnimation {
  type: 'fade' | 'slide' | 'scale' | 'none';
  duration: number;
  easing: 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'linear';
}

// Window content
export interface WindowContent {
  type: 'prompt-input' | 'prompt-library' | 'settings' | 'help' | 'custom';
  data?: any;
  template?: string;
  styles?: Record<string, string>;
}

// Window state
export interface WindowState {
  isVisible: boolean;
  isMinimized: boolean;
  isMaximized: boolean;
  isFocused: boolean;
  position: WindowPosition;
  size: WindowSize;
  opacity: number;
  zIndex: number;
}

// UI components
export interface UIComponent {
  id: string;
  type: ComponentType;
  props: Record<string, any>;
  children?: UIComponent[];
  events?: ComponentEvents;
}

export type ComponentType = 
  | 'container'
  | 'text-input'
  | 'text-area'
  | 'button'
  | 'dropdown'
  | 'checkbox'
  | 'radio'
  | 'slider'
  | 'progress'
  | 'list'
  | 'tree'
  | 'tabs'
  | 'modal'
  | 'tooltip'
  | 'icon'
  | 'image'
  | 'divider'
  | 'spacer';

export interface ComponentEvents {
  onClick?: (event: UIEvent) => void;
  onChange?: (value: any) => void;
  onFocus?: (event: UIEvent) => void;
  onBlur?: (event: UIEvent) => void;
  onKeyDown?: (event: KeyboardEvent) => void;
  onKeyUp?: (event: KeyboardEvent) => void;
  onMouseEnter?: (event: MouseEvent) => void;
  onMouseLeave?: (event: MouseEvent) => void;
}

export interface UIEvent {
  type: string;
  target: any;
  data?: any;
  timestamp: Date;
}

// Window manager
export interface IWindowManager {
  createWindow(config: FloatingWindowConfig): Promise<IFloatingWindow>;
  getWindow(id: string): IFloatingWindow | null;
  getAllWindows(): IFloatingWindow[];
  closeWindow(id: string): Promise<void>;
  closeAllWindows(): Promise<void>;
  showWindow(id: string, options?: WindowShowOptions): Promise<void>;
  hideWindow(id: string): Promise<void>;
  focusWindow(id: string): Promise<void>;
}

// Themes
export interface WindowTheme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    shadow: string;
    accent: string;
    success: string;
    warning: string;
    error: string;
  };
  typography: {
    fontFamily: string;
    fontSize: {
      small: string;
      medium: string;
      large: string;
      xlarge: string;
    };
    fontWeight: {
      normal: string;
      medium: string;
      bold: string;
    };
    lineHeight: {
      tight: string;
      normal: string;
      relaxed: string;
    };
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
    full: string;
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
}

// Layout system
export interface LayoutContainer {
  type: 'flex' | 'grid' | 'absolute' | 'relative';
  direction?: 'row' | 'column';
  justify?: 'start' | 'center' | 'end' | 'space-between' | 'space-around' | 'space-evenly';
  align?: 'start' | 'center' | 'end' | 'stretch';
  gap?: number;
  padding?: number | [number, number] | [number, number, number, number];
  margin?: number | [number, number] | [number, number, number, number];
}

// Animation system
export interface AnimationConfig {
  duration: number;
  easing: string;
  delay?: number;
  iterations?: number;
  direction?: 'normal' | 'reverse' | 'alternate' | 'alternate-reverse';
  fillMode?: 'none' | 'forwards' | 'backwards' | 'both';
}

// Event system
export type WindowEvent = 
  | 'window-created'
  | 'window-destroyed'
  | 'window-shown'
  | 'window-hidden'
  | 'window-focused'
  | 'window-blurred'
  | 'window-moved'
  | 'window-resized'
  | 'window-minimized'
  | 'window-maximized'
  | 'window-restored'
  | 'content-changed'
  | 'theme-changed';

export interface WindowEventData {
  windowId: string;
  event: WindowEvent;
  data?: any;
  timestamp: Date;
}

// Error types
export class FloatingWindowError extends Error {
  constructor(message: string, public readonly cause?: Error) {
    super(message);
    this.name = 'FloatingWindowError';
  }
}

export class WindowCreationError extends FloatingWindowError {
  constructor(message: string, cause?: Error) {
    super(message, cause);
    this.name = 'WindowCreationError';
  }
}

export class WindowOperationError extends FloatingWindowError {
  constructor(message: string, cause?: Error) {
    super(message, cause);
    this.name = 'WindowOperationError';
  }
}

// Platform-specific types
export interface PlatformWindowHandle {
  id: string;
  nativeHandle?: any;
  webContents?: any;
  browserWindow?: any;
}

// Accessibility
export interface AccessibilityOptions {
  role?: string;
  label?: string;
  description?: string;
  keyboardNavigation?: boolean;
  screenReaderSupport?: boolean;
  highContrast?: boolean;
  focusIndicator?: boolean;
}

// Performance
export interface PerformanceMetrics {
  renderTime: number;
  memoryUsage: number;
  cpuUsage: number;
  frameRate: number;
  lastUpdate: Date;
}
