/**
 * Secure storage implementation
 * Provides encrypted storage for sensitive data with secure key management
 */

import * as fs from 'fs/promises';
import * as fsSync from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import {
  SecureKeyStore,
  KeyInfo,
  EncryptedData,
  SecurityError,
  SecureStorageOptions
} from './types';
import { AESGCMEncryptionService, EncryptionUtils } from './Encryption';

export class FileSecureKeyStore implements SecureKeyStore {
  private storePath: string;
  private encryptionService: AESGCMEncryptionService;

  constructor(storePath: string) {
    this.storePath = storePath;
    this.encryptionService = new AESGCMEncryptionService();
  }

  async store(keyId: string, encryptedKey: EncryptedData, keyInfo: KeyInfo): Promise<void> {
    try {
      const keyData = {
        encryptedKey,
        keyInfo: {
          ...keyInfo,
          createdAt: keyInfo.createdAt.toISOString(),
          lastUsed: keyInfo.lastUsed.toISOString()
        }
      };

      const filePath = path.join(this.storePath, `${keyId}.key`);
      const serialized = JSON.stringify(keyData, null, 2);

      await fs.mkdir(path.dirname(filePath), { recursive: true });
      await fs.writeFile(filePath, serialized, { mode: 0o600 }); // Owner read/write only
    } catch (error) {
      const securityError = new Error('Failed to store key') as SecurityError;
      securityError.code = 'KEY_STORE_FAILED';
      securityError.details = error;
      securityError.severity = 'high';
      throw securityError;
    }
  }

  async retrieve(keyId: string): Promise<EncryptedData | null> {
    try {
      const filePath = path.join(this.storePath, `${keyId}.key`);
      const content = await fs.readFile(filePath, 'utf8');
      const keyData = JSON.parse(content);
      return keyData.encryptedKey;
    } catch (error: any) {
      if (error.code === 'ENOENT') {
        return null;
      }
      const securityError = new Error('Failed to retrieve key') as SecurityError;
      securityError.code = 'KEY_RETRIEVE_FAILED';
      securityError.details = error;
      securityError.severity = 'medium';
      throw securityError;
    }
  }

  async delete(keyId: string): Promise<void> {
    const filePath = path.join(this.storePath, `${keyId}.key`);

    try {
      // Secure deletion: overwrite file before deletion
      const stats = await fs.stat(filePath);
      const randomData = crypto.randomBytes(stats.size);
      await fs.writeFile(filePath, randomData);
      await fs.unlink(filePath);
    } catch (error: any) {
      if (error.code !== 'ENOENT') {
        const securityError = new Error('Failed to delete key') as SecurityError;
        securityError.code = 'KEY_DELETE_FAILED';
        securityError.details = error;
        securityError.severity = 'medium';
        throw securityError;
      }
    }
  }

  async listKeys(): Promise<KeyInfo[]> {
    try {
      const files = await fs.readdir(this.storePath);
      const keyFiles = files.filter(file => file.endsWith('.key'));

      const keyInfos: KeyInfo[] = [];
      for (const file of keyFiles) {
        try {
          const filePath = path.join(this.storePath, file);
          const content = await fs.readFile(filePath, 'utf8');
          const keyData = JSON.parse(content);
          
          // Convert date strings back to Date objects
          const keyInfo = {
            ...keyData.keyInfo,
            createdAt: new Date(keyData.keyInfo.createdAt),
            lastUsed: new Date(keyData.keyInfo.lastUsed)
          };
          
          keyInfos.push(keyInfo);
        } catch (error) {
          console.warn(`Failed to read key file ${file}:`, error);
        }
      }

      return keyInfos;
    } catch (error: any) {
      if (error.code === 'ENOENT') {
        return [];
      }
      const securityError = new Error('Failed to list keys') as SecurityError;
      securityError.code = 'KEY_LIST_FAILED';
      securityError.details = error;
      securityError.severity = 'low';
      throw securityError;
    }
  }

  async storeMasterKey(key: Buffer): Promise<void> {
    try {
      // In a real implementation, this would use OS keychain
      // For now, we'll store it in a protected file
      const filePath = path.join(this.storePath, '.master');
      await fs.mkdir(path.dirname(filePath), { recursive: true });
      await fs.writeFile(filePath, key, { mode: 0o600 });
    } catch (error) {
      const securityError = new Error('Failed to store master key') as SecurityError;
      securityError.code = 'MASTER_KEY_STORE_FAILED';
      securityError.details = error;
      securityError.severity = 'critical';
      throw securityError;
    }
  }

  async retrieveMasterKey(): Promise<Buffer | null> {
    try {
      const filePath = path.join(this.storePath, '.master');
      return await fs.readFile(filePath);
    } catch (error: any) {
      if (error.code === 'ENOENT') {
        return null;
      }
      const securityError = new Error('Failed to retrieve master key') as SecurityError;
      securityError.code = 'MASTER_KEY_RETRIEVE_FAILED';
      securityError.details = error;
      securityError.severity = 'critical';
      throw securityError;
    }
  }
}

/**
 * Secure storage for application data
 */
export class SecureDataStorage {
  private storePath: string;
  private encryptionService: AESGCMEncryptionService;
  private masterKey: Buffer | null = null;

  constructor(storePath: string) {
    this.storePath = storePath;
    this.encryptionService = new AESGCMEncryptionService();
  }

  async initialize(masterKey: Buffer): Promise<void> {
    this.masterKey = masterKey;
    await fs.mkdir(this.storePath, { recursive: true });
  }

  async store(key: string, value: any, options: SecureStorageOptions = {}): Promise<void> {
    if (!this.masterKey) {
      throw new Error('SecureDataStorage not initialized');
    }

    try {
      const data = {
        value,
        metadata: {
          ...options.metadata,
          timestamp: new Date().toISOString(),
          ttl: options.ttl
        }
      };

      let serialized = JSON.stringify(data);

      if (options.encrypt !== false) {
        const encrypted = await this.encryptionService.encrypt(serialized, this.masterKey);
        serialized = JSON.stringify(encrypted);
      }

      const filePath = path.join(this.storePath, `${this.sanitizeKey(key)}.data`);
      await fs.writeFile(filePath, serialized, { mode: 0o600 });

      // Create backup if requested
      if (options.backup) {
        const backupPath = `${filePath}.backup`;
        await fs.copyFile(filePath, backupPath);
      }
    } catch (error) {
      const securityError = new Error('Failed to store secure data') as SecurityError;
      securityError.code = 'SECURE_STORE_FAILED';
      securityError.details = error;
      securityError.severity = 'medium';
      throw securityError;
    }
  }

  async retrieve(key: string): Promise<any> {
    if (!this.masterKey) {
      throw new Error('SecureDataStorage not initialized');
    }

    try {
      const filePath = path.join(this.storePath, `${this.sanitizeKey(key)}.data`);
      const content = await fs.readFile(filePath, 'utf8');

      let data;
      try {
        // Try to parse as encrypted data first
        const encryptedData = JSON.parse(content);
        if (encryptedData.algorithm && encryptedData.iv && encryptedData.data) {
          const decrypted = await this.encryptionService.decrypt(encryptedData, this.masterKey);
          data = JSON.parse(decrypted.toString('utf8'));
        } else {
          // Fallback to plain JSON
          data = encryptedData;
        }
      } catch {
        // If parsing fails, treat as plain text
        data = JSON.parse(content);
      }

      // Check TTL
      if (data.metadata?.ttl) {
        const createdAt = new Date(data.metadata.timestamp);
        const expiresAt = new Date(createdAt.getTime() + data.metadata.ttl * 1000);
        if (new Date() > expiresAt) {
          await this.delete(key);
          return null;
        }
      }

      return data.value;
    } catch (error: any) {
      if (error.code === 'ENOENT') {
        return null;
      }
      const securityError = new Error('Failed to retrieve secure data') as SecurityError;
      securityError.code = 'SECURE_RETRIEVE_FAILED';
      securityError.details = error;
      securityError.severity = 'medium';
      throw securityError;
    }
  }

  async delete(key: string): Promise<void> {
    try {
      const filePath = path.join(this.storePath, `${this.sanitizeKey(key)}.data`);
      
      // Secure deletion
      try {
        const stats = await fs.stat(filePath);
        const randomData = crypto.randomBytes(stats.size);
        await fs.writeFile(filePath, randomData);
      } catch {
        // Ignore errors in secure overwrite
      }
      
      await fs.unlink(filePath);

      // Also delete backup if it exists
      const backupPath = `${filePath}.backup`;
      try {
        await fs.unlink(backupPath);
      } catch {
        // Ignore if backup doesn't exist
      }
    } catch (error: any) {
      if (error.code !== 'ENOENT') {
        const securityError = new Error('Failed to delete secure data') as SecurityError;
        securityError.code = 'SECURE_DELETE_FAILED';
        securityError.details = error;
        securityError.severity = 'low';
        throw securityError;
      }
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      const filePath = path.join(this.storePath, `${this.sanitizeKey(key)}.data`);
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  async listKeys(): Promise<string[]> {
    try {
      const files = await fs.readdir(this.storePath);
      return files
        .filter(file => file.endsWith('.data'))
        .map(file => file.replace('.data', ''));
    } catch (error: any) {
      if (error.code === 'ENOENT') {
        return [];
      }
      throw error;
    }
  }

  private sanitizeKey(key: string): string {
    // Remove or replace characters that are not safe for filenames
    return key.replace(/[^a-zA-Z0-9._-]/g, '_');
  }
}
