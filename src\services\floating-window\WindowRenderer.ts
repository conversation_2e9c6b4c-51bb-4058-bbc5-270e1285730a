/**
 * Window Renderer
 * Handles window rendering and DOM manipulation
 */

import { EventEmitter } from 'events';
import { FloatingWindowConfig, WindowContent, WindowOperationError } from './types';
import { LoggerFactory } from '../../core/logger';
import { toError } from '../../core/utils/errorUtils';

const logger = LoggerFactory.getInstance().getLogger('WindowRenderer');

/**
 * Window Renderer implementation
 */
export class WindowRenderer extends EventEmitter {
  private windowId: string;
  private config: FloatingWindowConfig;
  private windowElement: HTMLElement | undefined;
  private isInitialized: boolean = false;

  constructor(windowId: string, config: FloatingWindowConfig) {
    super();
    this.windowId = windowId;
    this.config = config;
  }

  /**
   * Initialize the renderer
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      await this.createWindowElement();
      this.setupEventListeners();
      this.isInitialized = true;
      
      logger.info('Window renderer initialized', { windowId: this.windowId });
    } catch (error) {
      logger.error('Failed to initialize window renderer', { error, windowId: this.windowId });
      throw new WindowOperationError('Failed to initialize window renderer', toError(error));
    }
  }

  /**
   * Show the window
   */
  async show(): Promise<void> {
    if (!this.windowElement) {
      throw new WindowOperationError('Window element not initialized');
    }

    this.windowElement.style.display = 'block';
    this.windowElement.style.opacity = this.config.window.opacity.toString();
    
    // Apply z-index for always on top
    if (this.config.window.alwaysOnTop) {
      this.windowElement.style.zIndex = '9999';
    }

    logger.debug('Window shown', { windowId: this.windowId });
  }

  /**
   * Hide the window
   */
  async hide(): Promise<void> {
    if (!this.windowElement) {
      return;
    }

    this.windowElement.style.display = 'none';
    logger.debug('Window hidden', { windowId: this.windowId });
  }

  /**
   * Set window position
   */
  async setPosition(x: number, y: number): Promise<void> {
    if (!this.windowElement) {
      throw new WindowOperationError('Window element not initialized');
    }

    this.windowElement.style.left = `${x}px`;
    this.windowElement.style.top = `${y}px`;
    
    logger.debug('Window position set', { windowId: this.windowId, x, y });
  }

  /**
   * Set window size
   */
  async setSize(width: number, height: number): Promise<void> {
    if (!this.windowElement) {
      throw new WindowOperationError('Window element not initialized');
    }

    this.windowElement.style.width = `${width}px`;
    this.windowElement.style.height = `${height}px`;
    
    logger.debug('Window size set', { windowId: this.windowId, width, height });
  }

  /**
   * Set window content
   */
  async setContent(content: WindowContent): Promise<void> {
    if (!this.windowElement) {
      throw new WindowOperationError('Window element not initialized');
    }

    const contentElement = this.windowElement.querySelector('.window-content');
    if (!contentElement) {
      throw new WindowOperationError('Content element not found');
    }

    switch (content.type) {
      case 'prompt-input':
        contentElement.innerHTML = this.renderPromptInput(content.data);
        break;
      case 'prompt-library':
        contentElement.innerHTML = this.renderPromptLibrary(content.data);
        break;
      case 'settings':
        contentElement.innerHTML = this.renderSettings(content.data);
        break;
      case 'help':
        contentElement.innerHTML = this.renderHelp(content.data);
        break;
      case 'custom':
        contentElement.innerHTML = content.template || '';
        break;
      default:
        contentElement.innerHTML = '<div>Unknown content type</div>';
    }

    // Apply custom styles if provided
    if (content.styles) {
      Object.assign((contentElement as HTMLElement).style, content.styles);
    }

    logger.debug('Window content set', { windowId: this.windowId, contentType: content.type });
  }

  /**
   * Focus the window
   */
  async focus(): Promise<void> {
    if (!this.windowElement) {
      throw new WindowOperationError('Window element not initialized');
    }

    this.windowElement.focus();
    this.emit('focus');
    
    logger.debug('Window focused', { windowId: this.windowId });
  }

  /**
   * Minimize the window
   */
  async minimize(): Promise<void> {
    if (!this.windowElement) {
      throw new WindowOperationError('Window element not initialized');
    }

    this.windowElement.style.transform = 'scale(0.1)';
    this.windowElement.style.opacity = '0';
    
    setTimeout(() => {
      if (this.windowElement) {
        this.windowElement.style.display = 'none';
      }
    }, 200);

    logger.debug('Window minimized', { windowId: this.windowId });
  }

  /**
   * Maximize the window
   */
  async maximize(): Promise<void> {
    if (!this.windowElement) {
      throw new WindowOperationError('Window element not initialized');
    }

    // In a real implementation, this would maximize to screen size
    this.windowElement.style.width = '100vw';
    this.windowElement.style.height = '100vh';
    this.windowElement.style.left = '0px';
    this.windowElement.style.top = '0px';

    logger.debug('Window maximized', { windowId: this.windowId });
  }

  /**
   * Restore the window
   */
  async restore(): Promise<void> {
    if (!this.windowElement) {
      throw new WindowOperationError('Window element not initialized');
    }

    this.windowElement.style.transform = 'scale(1)';
    this.windowElement.style.opacity = this.config.window.opacity.toString();
    this.windowElement.style.display = 'block';

    // Restore original size and position
    this.windowElement.style.width = `${this.config.window.width}px`;
    this.windowElement.style.height = `${this.config.window.height}px`;

    logger.debug('Window restored', { windowId: this.windowId });
  }

  /**
   * Destroy the window
   */
  async destroy(): Promise<void> {
    if (this.windowElement && this.windowElement.parentNode) {
      this.windowElement.parentNode.removeChild(this.windowElement);
    }
    
    this.removeAllListeners();
    this.windowElement = undefined;
    
    logger.debug('Window destroyed', { windowId: this.windowId });
  }

  /**
   * Create window DOM element
   */
  private async createWindowElement(): Promise<void> {
    this.windowElement = document.createElement('div');
    this.windowElement.id = `floating-window-${this.windowId}`;
    this.windowElement.className = 'floating-window';
    
    // Apply base styles
    Object.assign(this.windowElement.style, {
      position: 'fixed',
      width: `${this.config.window.width}px`,
      height: `${this.config.window.height}px`,
      left: `${this.config.window.x || 100}px`,
      top: `${this.config.window.y || 100}px`,
      backgroundColor: '#ffffff',
      border: '1px solid #cccccc',
      borderRadius: `${this.config.ui.borderRadius}px`,
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      display: 'none',
      opacity: '0',
      zIndex: '1000',
      fontFamily: this.config.ui.fontFamily,
      fontSize: `${this.config.ui.fontSize}px`,
      overflow: 'hidden',
      userSelect: 'none',
      transition: 'opacity 0.2s ease-in-out'
    });

    // Create window structure
    this.windowElement.innerHTML = `
      <div class="window-header" style="
        height: 32px;
        background: linear-gradient(to bottom, #f0f0f0, #e0e0e0);
        border-bottom: 1px solid #cccccc;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 8px;
        cursor: move;
      ">
        <div class="window-title" style="
          font-weight: 500;
          color: #333333;
          font-size: 12px;
        ">Personal Prompt</div>
        <div class="window-controls" style="
          display: flex;
          gap: 4px;
        ">
          <button class="minimize-btn" style="
            width: 16px;
            height: 16px;
            border: none;
            border-radius: 2px;
            background: #ffbd2e;
            cursor: pointer;
          "></button>
          <button class="maximize-btn" style="
            width: 16px;
            height: 16px;
            border: none;
            border-radius: 2px;
            background: #28ca42;
            cursor: pointer;
          "></button>
          <button class="close-btn" style="
            width: 16px;
            height: 16px;
            border: none;
            border-radius: 2px;
            background: #ff5f56;
            cursor: pointer;
          "></button>
        </div>
      </div>
      <div class="window-content" style="
        height: calc(100% - 32px);
        padding: 16px;
        overflow-y: auto;
      "></div>
    `;

    // Add to document
    document.body.appendChild(this.windowElement);
  }

  /**
   * Setup event listeners
   */
  private setupEventListeners(): void {
    if (!this.windowElement) return;

    // Window controls
    const minimizeBtn = this.windowElement.querySelector('.minimize-btn');
    const maximizeBtn = this.windowElement.querySelector('.maximize-btn');
    const closeBtn = this.windowElement.querySelector('.close-btn');

    minimizeBtn?.addEventListener('click', () => this.minimize());
    maximizeBtn?.addEventListener('click', () => this.maximize());
    closeBtn?.addEventListener('click', () => this.emit('close-requested'));

    // Focus/blur events
    this.windowElement.addEventListener('focus', () => this.emit('focus'));
    this.windowElement.addEventListener('blur', () => this.emit('blur'));

    // Make window draggable
    this.setupDragBehavior();
  }

  /**
   * Setup drag behavior
   */
  private setupDragBehavior(): void {
    if (!this.windowElement) return;

    const header = this.windowElement.querySelector('.window-header') as HTMLElement;
    if (!header) return;

    let isDragging = false;
    let dragOffset = { x: 0, y: 0 };

    header.addEventListener('mousedown', (e) => {
      if (!this.config.window.movable) return;
      
      isDragging = true;
      const rect = this.windowElement!.getBoundingClientRect();
      dragOffset.x = e.clientX - rect.left;
      dragOffset.y = e.clientY - rect.top;
      
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    });

    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging || !this.windowElement) return;
      
      const x = e.clientX - dragOffset.x;
      const y = e.clientY - dragOffset.y;
      
      this.setPosition(x, y);
    };

    const handleMouseUp = () => {
      isDragging = false;
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }

  /**
   * Render prompt input content
   */
  private renderPromptInput(data?: any): string {
    return `
      <div class="prompt-input-container">
        <textarea 
          class="prompt-textarea"
          placeholder="Enter your prompt here..."
          style="
            width: 100%;
            height: 200px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 12px;
            font-family: inherit;
            font-size: 14px;
            resize: vertical;
            outline: none;
          "
        >${data?.prompt || ''}</textarea>
        <div class="prompt-actions" style="
          margin-top: 12px;
          display: flex;
          gap: 8px;
          justify-content: flex-end;
        ">
          <button class="btn-secondary" style="
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f8f9fa;
            cursor: pointer;
          ">Clear</button>
          <button class="btn-primary" style="
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
          ">Send</button>
        </div>
      </div>
    `;
  }

  /**
   * Render prompt library content
   */
  private renderPromptLibrary(_data?: any): string {
    return `
      <div class="prompt-library-container">
        <div class="search-bar" style="margin-bottom: 16px;">
          <input 
            type="text" 
            placeholder="Search prompts..."
            style="
              width: 100%;
              padding: 8px 12px;
              border: 1px solid #ddd;
              border-radius: 4px;
              outline: none;
            "
          />
        </div>
        <div class="prompt-list">
          <div class="prompt-item" style="
            padding: 12px;
            border: 1px solid #eee;
            border-radius: 4px;
            margin-bottom: 8px;
            cursor: pointer;
          ">
            <div class="prompt-title" style="font-weight: 500;">Sample Prompt</div>
            <div class="prompt-preview" style="
              color: #666;
              font-size: 12px;
              margin-top: 4px;
            ">This is a sample prompt for demonstration...</div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Render settings content
   */
  private renderSettings(_data?: any): string {
    return `
      <div class="settings-container">
        <h3 style="margin-top: 0;">Settings</h3>
        <div class="setting-group" style="margin-bottom: 16px;">
          <label style="display: block; margin-bottom: 4px;">Theme</label>
          <select style="
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
          ">
            <option value="light">Light</option>
            <option value="dark">Dark</option>
            <option value="auto">Auto</option>
          </select>
        </div>
        <div class="setting-group">
          <label style="display: flex; align-items: center; gap: 8px;">
            <input type="checkbox" />
            Always on top
          </label>
        </div>
      </div>
    `;
  }

  /**
   * Render help content
   */
  private renderHelp(_data?: any): string {
    return `
      <div class="help-container">
        <h3 style="margin-top: 0;">Help & Shortcuts</h3>
        <div class="help-section">
          <h4>Keyboard Shortcuts</h4>
          <ul style="margin: 8px 0; padding-left: 20px;">
            <li>Ctrl+Shift+P - Show/Hide Window</li>
            <li>Ctrl+Shift+O - Capture Screen</li>
            <li>Ctrl+Shift+V - Voice Input</li>
            <li>Escape - Hide Window</li>
          </ul>
        </div>
        <div class="help-section">
          <h4>Features</h4>
          <ul style="margin: 8px 0; padding-left: 20px;">
            <li>Screen capture with OCR</li>
            <li>Voice-to-text input</li>
            <li>Prompt library management</li>
            <li>Context extraction</li>
          </ul>
        </div>
      </div>
    `;
  }
}
