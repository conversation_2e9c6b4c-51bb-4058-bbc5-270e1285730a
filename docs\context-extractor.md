# Context Extractor Module Specification

## 🎯 Purpose

The Context Extractor module provides comprehensive context gathering capabilities, including screen capture, OCR text extraction, clipboard monitoring, and active window content detection to enhance prompt generation.

## 📋 Responsibilities

### Screen Capture
- Full screen and region-based screenshots
- OCR text extraction from images
- Window-specific capture
- Multi-monitor support

### Text Extraction
- Clipboard content monitoring
- Active window text extraction
- File content reading
- Web page content extraction

### Context Processing
- Text cleaning and formatting
- Language detection
- Content summarization
- Metadata extraction

## 🏗️ Architecture

### Core Extractor
```typescript
class ContextExtractor {
  private ocrEngine: OCREngine;
  private clipboardMonitor: ClipboardMonitor;
  private windowManager: WindowManager;
  private fileReader: FileReader;
  
  async captureScreen(region?: ScreenRegion): Promise<string>;
  async getClipboardText(): Promise<string>;
  async getActiveWindowText(): Promise<string>;
  async extractFromFile(filePath: string): Promise<string>;
  async extractFromUrl(url: string): Promise<string>;
}
```

### Screen Capture Interface
```typescript
interface ScreenRegion {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface ScreenCaptureOptions {
  region?: ScreenRegion;
  format?: 'png' | 'jpg' | 'webp';
  quality?: number;
  includeOCR?: boolean;
  ocrLanguage?: string;
}

interface CaptureResult {
  imagePath: string;
  extractedText?: string;
  metadata: {
    timestamp: Date;
    region: ScreenRegion;
    displayInfo: DisplayInfo;
  };
}
```

## 📷 Screen Capture Implementation

### Screen Capture Service
```typescript
class ScreenCaptureService {
  private displays: Display[] = [];
  
  constructor() {
    this.updateDisplays();
  }
  
  async captureFullScreen(options: ScreenCaptureOptions = {}): Promise<CaptureResult> {
    const primaryDisplay = screen.getPrimaryDisplay();
    const region = {
      x: 0,
      y: 0,
      width: primaryDisplay.bounds.width,
      height: primaryDisplay.bounds.height
    };
    
    return this.captureRegion(region, options);
  }
  
  async captureRegion(region: ScreenRegion, options: ScreenCaptureOptions = {}): Promise<CaptureResult> {
    try {
      // Use Electron's desktopCapturer
      const sources = await desktopCapturer.getSources({
        types: ['screen'],
        thumbnailSize: {
          width: region.width,
          height: region.height
        }
      });
      
      const primarySource = sources[0];
      const imagePath = await this.saveScreenshot(primarySource.thumbnail, options);
      
      let extractedText: string | undefined;
      if (options.includeOCR) {
        extractedText = await this.ocrEngine.extractText(imagePath, {
          language: options.ocrLanguage || 'eng'
        });
      }
      
      return {
        imagePath,
        extractedText,
        metadata: {
          timestamp: new Date(),
          region,
          displayInfo: this.getDisplayInfo()
        }
      };
    } catch (error) {
      throw new Error(`Screen capture failed: ${error.message}`);
    }
  }
  
  async captureActiveWindow(): Promise<CaptureResult> {
    const activeWindow = await this.getActiveWindow();
    if (!activeWindow) {
      throw new Error('No active window found');
    }
    
    const bounds = activeWindow.getBounds();
    return this.captureRegion(bounds);
  }
  
  private async saveScreenshot(thumbnail: NativeImage, options: ScreenCaptureOptions): Promise<string> {
    const format = options.format || 'png';
    const quality = options.quality || 90;
    
    let buffer: Buffer;
    switch (format) {
      case 'jpg':
        buffer = thumbnail.toJPEG(quality);
        break;
      case 'webp':
        buffer = thumbnail.toDataURL().split(',')[1];
        buffer = Buffer.from(buffer, 'base64');
        break;
      default:
        buffer = thumbnail.toPNG();
    }
    
    const filename = `screenshot_${Date.now()}.${format}`;
    const filepath = path.join(os.tmpdir(), filename);
    
    await fs.writeFile(filepath, buffer);
    return filepath;
  }
  
  private updateDisplays(): void {
    this.displays = screen.getAllDisplays();
  }
  
  private getDisplayInfo(): DisplayInfo {
    return {
      displays: this.displays.map(display => ({
        id: display.id,
        bounds: display.bounds,
        workArea: display.workArea,
        scaleFactor: display.scaleFactor
      }))
    };
  }
}
```

## 🔍 OCR Engine Implementation

### OCR Interface
```typescript
interface OCREngine {
  extractText(imagePath: string, options?: OCROptions): Promise<string>;
  extractTextFromBuffer(imageBuffer: Buffer, options?: OCROptions): Promise<string>;
  getSupportedLanguages(): string[];
  detectLanguage(imagePath: string): Promise<string>;
}

interface OCROptions {
  language?: string;
  pageSegMode?: number;
  ocrEngineMode?: number;
  whitelist?: string;
  blacklist?: string;
  preserveInterwordSpaces?: boolean;
}
```

### Tesseract OCR Implementation
```typescript
class TesseractOCREngine implements OCREngine {
  private tesseract: any;
  private supportedLanguages: string[] = ['eng', 'spa', 'fra', 'deu', 'chi_sim', 'chi_tra'];
  
  constructor() {
    this.initializeTesseract();
  }
  
  async extractText(imagePath: string, options: OCROptions = {}): Promise<string> {
    try {
      const { data: { text } } = await this.tesseract.recognize(imagePath, {
        lang: options.language || 'eng',
        options: {
          tessedit_pageseg_mode: options.pageSegMode || 6,
          tessedit_ocr_engine_mode: options.ocrEngineMode || 3,
          tessedit_char_whitelist: options.whitelist,
          tessedit_char_blacklist: options.blacklist,
          preserve_interword_spaces: options.preserveInterwordSpaces ? '1' : '0'
        }
      });
      
      return this.cleanExtractedText(text);
    } catch (error) {
      throw new Error(`OCR extraction failed: ${error.message}`);
    }
  }
  
  async extractTextFromBuffer(imageBuffer: Buffer, options: OCROptions = {}): Promise<string> {
    const tempPath = path.join(os.tmpdir(), `ocr_temp_${Date.now()}.png`);
    await fs.writeFile(tempPath, imageBuffer);
    
    try {
      const result = await this.extractText(tempPath, options);
      await fs.unlink(tempPath); // Clean up temp file
      return result;
    } catch (error) {
      await fs.unlink(tempPath).catch(() => {}); // Clean up on error
      throw error;
    }
  }
  
  getSupportedLanguages(): string[] {
    return [...this.supportedLanguages];
  }
  
  async detectLanguage(imagePath: string): Promise<string> {
    // Simple language detection based on character patterns
    const text = await this.extractText(imagePath, { language: 'eng+spa+fra+deu' });
    
    // Basic language detection logic
    if (/[àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]/i.test(text)) {
      return 'fra'; // French
    } else if (/[äöüß]/i.test(text)) {
      return 'deu'; // German
    } else if (/[ñáéíóúü]/i.test(text)) {
      return 'spa'; // Spanish
    } else if (/[\u4e00-\u9fff]/i.test(text)) {
      return 'chi_sim'; // Chinese Simplified
    }
    
    return 'eng'; // Default to English
  }
  
  private async initializeTesseract(): Promise<void> {
    const { createWorker } = await import('tesseract.js');
    this.tesseract = await createWorker();
  }
  
  private cleanExtractedText(text: string): string {
    return text
      .replace(/\n\s*\n/g, '\n') // Remove multiple empty lines
      .replace(/^\s+|\s+$/g, '') // Trim whitespace
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
  }
}
```

## 📋 Clipboard Monitoring

### Clipboard Monitor
```typescript
class ClipboardMonitor {
  private lastClipboardContent: string = '';
  private listeners: Set<(content: string) => void> = new Set();
  private monitoring = false;
  private monitorInterval: NodeJS.Timeout | null = null;
  
  startMonitoring(intervalMs: number = 1000): void {
    if (this.monitoring) return;
    
    this.monitoring = true;
    this.monitorInterval = setInterval(() => {
      this.checkClipboard();
    }, intervalMs);
  }
  
  stopMonitoring(): void {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }
    this.monitoring = false;
  }
  
  getCurrentContent(): string {
    return clipboard.readText();
  }
  
  onContentChange(listener: (content: string) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }
  
  private checkClipboard(): void {
    try {
      const currentContent = clipboard.readText();
      
      if (currentContent !== this.lastClipboardContent) {
        this.lastClipboardContent = currentContent;
        this.notifyListeners(currentContent);
      }
    } catch (error) {
      console.warn('Failed to read clipboard:', error);
    }
  }
  
  private notifyListeners(content: string): void {
    this.listeners.forEach(listener => {
      try {
        listener(content);
      } catch (error) {
        console.error('Clipboard listener error:', error);
      }
    });
  }
}
```

## 🪟 Window Text Extraction

### Active Window Text Extractor
```typescript
class WindowTextExtractor {
  async getActiveWindowText(): Promise<string> {
    const activeWindow = BrowserWindow.getFocusedWindow();
    if (!activeWindow) {
      throw new Error('No active window found');
    }
    
    try {
      // For web content windows
      if (activeWindow.webContents) {
        return await this.extractWebContent(activeWindow.webContents);
      }
      
      // For native windows, use platform-specific methods
      return await this.extractNativeWindowText(activeWindow);
    } catch (error) {
      throw new Error(`Failed to extract window text: ${error.message}`);
    }
  }
  
  private async extractWebContent(webContents: WebContents): Promise<string> {
    return await webContents.executeJavaScript(`
      // Extract visible text from the page
      function extractVisibleText() {
        const walker = document.createTreeWalker(
          document.body,
          NodeFilter.SHOW_TEXT,
          {
            acceptNode: function(node) {
              const parent = node.parentElement;
              if (!parent) return NodeFilter.FILTER_REJECT;
              
              const style = window.getComputedStyle(parent);
              if (style.display === 'none' || 
                  style.visibility === 'hidden' ||
                  style.opacity === '0') {
                return NodeFilter.FILTER_REJECT;
              }
              
              return NodeFilter.FILTER_ACCEPT;
            }
          }
        );
        
        let text = '';
        let node;
        while (node = walker.nextNode()) {
          text += node.textContent + ' ';
        }
        
        return text.trim();
      }
      
      extractVisibleText();
    `);
  }
  
  private async extractNativeWindowText(window: BrowserWindow): Promise<string> {
    // Platform-specific implementation would go here
    // This is a placeholder for native window text extraction
    const title = window.getTitle();
    return `Window Title: ${title}`;
  }
}
```

## 📁 File Content Extraction

### File Reader
```typescript
class FileContentReader {
  private supportedExtensions = new Set([
    '.txt', '.md', '.json', '.js', '.ts', '.py', '.java', '.cpp', '.c',
    '.html', '.css', '.xml', '.yaml', '.yml', '.csv', '.log'
  ]);
  
  async readFile(filePath: string): Promise<string> {
    const ext = path.extname(filePath).toLowerCase();
    
    if (!this.supportedExtensions.has(ext)) {
      throw new Error(`Unsupported file type: ${ext}`);
    }
    
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      return this.processFileContent(content, ext);
    } catch (error) {
      throw new Error(`Failed to read file: ${error.message}`);
    }
  }
  
  async readMultipleFiles(filePaths: string[]): Promise<Record<string, string>> {
    const results: Record<string, string> = {};
    
    await Promise.all(
      filePaths.map(async (filePath) => {
        try {
          results[filePath] = await this.readFile(filePath);
        } catch (error) {
          results[filePath] = `Error reading file: ${error.message}`;
        }
      })
    );
    
    return results;
  }
  
  private processFileContent(content: string, extension: string): string {
    switch (extension) {
      case '.json':
        try {
          return JSON.stringify(JSON.parse(content), null, 2);
        } catch {
          return content;
        }
      
      case '.csv':
        return this.formatCSV(content);
      
      default:
        return content;
    }
  }
  
  private formatCSV(content: string): string {
    const lines = content.split('\n');
    if (lines.length < 2) return content;
    
    const headers = lines[0].split(',');
    const rows = lines.slice(1, 6); // Limit to first 5 rows for preview
    
    let formatted = `Headers: ${headers.join(', ')}\n\n`;
    formatted += `Sample data (first ${rows.length} rows):\n`;
    rows.forEach((row, index) => {
      formatted += `Row ${index + 1}: ${row}\n`;
    });
    
    return formatted;
  }
}
```

## 🧪 Testing Strategy

### Unit Tests
```typescript
describe('ContextExtractor', () => {
  let extractor: ContextExtractor;
  let mockOCREngine: jest.Mocked<OCREngine>;
  
  beforeEach(() => {
    mockOCREngine = {
      extractText: jest.fn(),
      extractTextFromBuffer: jest.fn(),
      getSupportedLanguages: jest.fn(),
      detectLanguage: jest.fn()
    };
    
    extractor = new ContextExtractor(mockOCREngine);
  });
  
  test('should capture screen and extract text', async () => {
    const mockText = 'Extracted text from screen';
    mockOCREngine.extractText.mockResolvedValue(mockText);
    
    const result = await extractor.captureScreen();
    
    expect(result).toBe(mockText);
    expect(mockOCREngine.extractText).toHaveBeenCalled();
  });
  
  test('should get clipboard text', async () => {
    const clipboardText = 'Test clipboard content';
    jest.spyOn(clipboard, 'readText').mockReturnValue(clipboardText);
    
    const result = await extractor.getClipboardText();
    
    expect(result).toBe(clipboardText);
  });
});
```
