/**
 * Template Processor implementation
 * Handles template processing with Handlebars-like syntax and built-in helpers
 */

import {
  PromptTemplate,
  TemplateValidationResult,
  // TemplateProcessingResult,
  TemplateHelper,
  TemplateProcessingError
} from './types';
import { LoggerFactory } from '../../core/logger';
import { toError, getErrorMessage } from '../../core/utils/errorUtils';

const logger = LoggerFactory.getInstance().getLogger('TemplateProcessor');

/**
 * Simple template processor with Handlebars-like syntax
 */
export class TemplateProcessor {
  private helpers: Map<string, TemplateHelper> = new Map();
  private builtInTemplates: PromptTemplate[] = [];

  constructor() {
    this.registerBuiltInHelpers();
  }

  /**
   * Initialize the template processor
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Template processor initialized');
    } catch (error) {
      logger.error('Failed to initialize template processor', { error });
      throw new TemplateProcessingError('Failed to initialize template processor', toError(error));
    }
  }

  /**
   * Process a template with variables
   */
  process(template: string, variables: Record<string, any>): string {
    try {
      let result = template;

      // Process simple variable substitution {{variable}}
      result = result.replace(/\{\{([^}]+)\}\}/g, (match, variablePath) => {
        const trimmedPath = variablePath.trim();
        
        // Handle helper calls like {{helper arg1 arg2}}
        if (trimmedPath.includes(' ')) {
          return this.processHelper(trimmedPath, variables);
        }
        
        // Handle simple variable substitution
        return this.getVariableValue(trimmedPath, variables) || match;
      });

      // Process conditional blocks {{#if condition}}...{{/if}}
      result = this.processConditionals(result, variables);

      // Process loops {{#each array}}...{{/each}}
      result = this.processLoops(result, variables);

      return result;
    } catch (error) {
      logger.error('Template processing failed', { error, template: template.substring(0, 100) });
      throw new TemplateProcessingError('Template processing failed', toError(error));
    }
  }

  /**
   * Validate template syntax
   */
  validate(template: string): TemplateValidationResult {
    try {
      const warnings: string[] = [];
      const missingVariables: string[] = [];

      // Check for unmatched braces
      const openBraces = (template.match(/\{\{/g) || []).length;
      const closeBraces = (template.match(/\}\}/g) || []).length;
      
      if (openBraces !== closeBraces) {
        return {
          valid: false,
          error: 'Unmatched template braces'
        };
      }

      // Check for valid helper syntax
      const helperMatches = template.match(/\{\{#(\w+)[^}]*\}\}/g);
      if (helperMatches) {
        for (const match of helperMatches) {
          const helperName = match.match(/\{\{#(\w+)/)?.[1];
          if (helperName && !this.helpers.has(helperName) && !['if', 'each', 'unless'].includes(helperName)) {
            warnings.push(`Unknown helper: ${helperName}`);
          }
        }
      }

      // Extract variable references
      const variableMatches = template.match(/\{\{([^#/][^}]*)\}\}/g);
      if (variableMatches) {
        for (const match of variableMatches) {
          const variable = match.replace(/[{}]/g, '').trim();
          if (!variable.includes(' ')) { // Not a helper call
            missingVariables.push(variable);
          }
        }
      }

      return {
        valid: true,
        warnings: warnings.length > 0 ? warnings : [],
        missingVariables: missingVariables.length > 0 ? missingVariables : []
      };
    } catch (error) {
      return {
        valid: false,
        error: getErrorMessage(error)
      };
    }
  }

  /**
   * Register a custom helper
   */
  registerHelper(helper: TemplateHelper): void {
    this.helpers.set(helper.name, helper);
    logger.debug('Template helper registered', { name: helper.name });
  }

  /**
   * Load built-in templates
   */
  async loadBuiltInTemplates(): Promise<void> {
    try {
      this.builtInTemplates = this.getBuiltInTemplates();
      logger.info('Built-in templates loaded', { count: this.builtInTemplates.length });
    } catch (error) {
      logger.error('Failed to load built-in templates', { error });
      throw new TemplateProcessingError('Failed to load built-in templates', toError(error));
    }
  }

  /**
   * Get built-in templates
   */
  getBuiltInTemplates(): PromptTemplate[] {
    return [
      {
        id: 'code-review',
        name: 'Code Review Request',
        description: 'Generate a code review request prompt',
        template: `Please review the following {{language}} code:

\`\`\`{{language}}
{{code}}
\`\`\`

Focus on:
{{#each focusAreas}}
- {{this}}
{{/each}}

{{#if context}}
Additional context: {{context}}
{{/if}}`,
        variables: [
          {
            name: 'language',
            type: 'string',
            required: true,
            description: 'Programming language'
          },
          {
            name: 'code',
            type: 'string',
            required: true,
            description: 'Code to review'
          },
          {
            name: 'focusAreas',
            type: 'array',
            required: false,
            defaultValue: ['Performance', 'Security', 'Best Practices'],
            description: 'Areas to focus on during review'
          }
        ],
        category: 'Development',
        tags: ['code', 'review', 'development'],
        metadata: {
          author: 'system',
          version: '1.0.0',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      },
      {
        id: 'text-summarization',
        name: 'Text Summarization',
        description: 'Summarize long text content',
        template: `Please summarize the following text in {{#if length}}{{length}}{{else}}3-5{{/if}} sentences:

{{text}}

{{#if style}}
Style: {{style}}
{{/if}}

{{#if audience}}
Target audience: {{audience}}
{{/if}}`,
        variables: [
          {
            name: 'text',
            type: 'string',
            required: true,
            description: 'Text to summarize'
          },
          {
            name: 'length',
            type: 'string',
            required: false,
            description: 'Desired summary length'
          },
          {
            name: 'style',
            type: 'string',
            required: false,
            description: 'Summary style (bullet points, paragraph, etc.)'
          }
        ],
        category: 'Text Processing',
        tags: ['summary', 'text', 'analysis'],
        metadata: {
          author: 'system',
          version: '1.0.0',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      }
    ];
  }

  /**
   * Get variable value from object using dot notation
   */
  private getVariableValue(path: string, variables: Record<string, any>): string {
    const keys = path.split('.');
    let value = variables;

    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return '';
      }
    }

    return String(value || '');
  }

  /**
   * Process helper function calls
   */
  private processHelper(helperCall: string, variables: Record<string, any>): string {
    const parts = helperCall.split(' ');
    const helperName = parts[0];
    const args = parts.slice(1);

    const helper = this.helpers.get(helperName);
    if (!helper) {
      return `{{${helperCall}}}`;
    }

    try {
      // Resolve arguments
      const resolvedArgs = args.map(arg => {
        // Remove quotes if present
        if ((arg.startsWith('"') && arg.endsWith('"')) || (arg.startsWith("'") && arg.endsWith("'"))) {
          return arg.slice(1, -1);
        }
        // Try to resolve as variable
        return this.getVariableValue(arg, variables) || arg;
      });

      return String(helper.execute(...resolvedArgs));
    } catch (error) {
      logger.warn('Helper execution failed', { helper: helperName, error });
      return `{{${helperCall}}}`;
    }
  }

  /**
   * Process conditional blocks
   */
  private processConditionals(template: string, variables: Record<string, any>): string {
    // Simple if/else processing
    return template.replace(/\{\{#if\s+([^}]+)\}\}([\s\S]*?)\{\{\/if\}\}/g, (_match, condition, content) => {
      const value = this.getVariableValue(condition.trim(), variables);
      return this.isTruthy(value) ? content : '';
    });
  }

  /**
   * Process loop blocks
   */
  private processLoops(template: string, variables: Record<string, any>): string {
    return template.replace(/\{\{#each\s+([^}]+)\}\}([\s\S]*?)\{\{\/each\}\}/g, (_match, arrayPath, content) => {
      const array = this.getVariableValue(arrayPath.trim(), variables);
      
      if (!Array.isArray(array)) {
        return '';
      }

      return array.map((item, index) => {
        let itemContent = content;
        
        // Replace {{this}} with current item
        itemContent = itemContent.replace(/\{\{this\}\}/g, String(item));
        
        // Replace {{@index}} with current index
        itemContent = itemContent.replace(/\{\{@index\}\}/g, String(index));
        
        return itemContent;
      }).join('');
    });
  }

  /**
   * Check if value is truthy
   */
  private isTruthy(value: any): boolean {
    if (typeof value === 'string') {
      return value.length > 0 && value !== 'false' && value !== '0';
    }
    return Boolean(value);
  }

  /**
   * Register built-in helpers
   */
  private registerBuiltInHelpers(): void {
    // Date formatting helper
    this.registerHelper({
      name: 'formatDate',
      description: 'Format a date',
      execute: (date: string | Date, format: string = 'YYYY-MM-DD') => {
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';
        
        // Simple date formatting
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        
        return format
          .replace('YYYY', String(year))
          .replace('MM', month)
          .replace('DD', day);
      }
    });

    // Text transformation helpers
    this.registerHelper({
      name: 'uppercase',
      description: 'Convert text to uppercase',
      execute: (text: string) => String(text).toUpperCase()
    });

    this.registerHelper({
      name: 'lowercase',
      description: 'Convert text to lowercase',
      execute: (text: string) => String(text).toLowerCase()
    });

    this.registerHelper({
      name: 'capitalize',
      description: 'Capitalize first letter',
      execute: (text: string) => {
        const str = String(text);
        return str.charAt(0).toUpperCase() + str.slice(1);
      }
    });

    // Array helpers
    this.registerHelper({
      name: 'join',
      description: 'Join array elements',
      execute: (array: any[], separator: string = ', ') => {
        if (!Array.isArray(array)) return '';
        return array.join(separator);
      }
    });

    // String helpers
    this.registerHelper({
      name: 'truncate',
      description: 'Truncate text to specified length',
      execute: (text: string, length: number = 100) => {
        const str = String(text);
        return str.length > length ? str.substring(0, length) + '...' : str;
      }
    });

    this.registerHelper({
      name: 'replace',
      description: 'Replace text',
      execute: (text: string, search: string, replacement: string) => {
        return String(text).replace(new RegExp(search, 'g'), replacement);
      }
    });
  }
}
