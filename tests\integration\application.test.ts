/**
 * Integration tests for the main application
 */

import { Application } from '../../src/main/Application';
import { LoggerFactory } from '../../src/core/logger';

describe('Application Integration Tests', () => {
  let app: Application;
  
  beforeEach(() => {
    // Create application with test configuration
    app = new Application({
      database: {
        path: ':memory:', // Use in-memory database for tests
        enableWAL: false,
        enableForeignKeys: true
      },
      logging: {
        level: 'error', // Reduce log noise in tests
        enableFileLogging: false,
        logDirectory: './test-logs'
      },
      hotkeys: {
        enableGlobalHotkeys: false, // Disable for tests
        defaultHotkeys: {}
      },
      ui: {
        theme: 'light',
        enableAnimations: false,
        windowOpacity: 1.0
      },
      voice: {
        enableVoiceInput: false, // Disable for tests
        defaultLanguage: 'en-US',
        enableContinuousListening: false
      },
      context: {
        enableScreenCapture: false, // Disable for tests
        enableClipboardMonitoring: false,
        ocrLanguage: 'eng'
      },
      ai: {
        defaultProvider: 'mock',
        enableStreaming: false,
        maxTokens: 1000
      }
    });
  });

  afterEach(async () => {
    if (app) {
      await app.shutdown();
    }
  });

  describe('Application Lifecycle', () => {
    test('should initialize successfully', async () => {
      await expect(app.initialize()).resolves.not.toThrow();
      
      const status = app.getStatus();
      expect(status.isInitialized).toBe(true);
      expect(status.isRunning).toBe(false);
    });

    test('should start successfully after initialization', async () => {
      await app.initialize();
      await expect(app.start()).resolves.not.toThrow();
      
      const status = app.getStatus();
      expect(status.isInitialized).toBe(true);
      expect(status.isRunning).toBe(true);
    });

    test('should stop successfully', async () => {
      await app.initialize();
      await app.start();
      await expect(app.stop()).resolves.not.toThrow();
      
      const status = app.getStatus();
      expect(status.isRunning).toBe(false);
    });

    test('should restart successfully', async () => {
      await app.initialize();
      await app.start();
      await expect(app.restart()).resolves.not.toThrow();
      
      const status = app.getStatus();
      expect(status.isRunning).toBe(true);
    });

    test('should shutdown gracefully', async () => {
      await app.initialize();
      await app.start();
      await expect(app.shutdown()).resolves.not.toThrow();
      
      const status = app.getStatus();
      expect(status.isRunning).toBe(false);
    });
  });

  describe('Module Management', () => {
    test('should initialize all required modules', async () => {
      await app.initialize();
      
      const status = app.getStatus();
      const expectedModules = [
        'prompt-library',
        'prompt-engine',
        'voice-module',
        'hotkey-manager',
        'context-extractor',
        'window-manager'
      ];
      
      for (const moduleName of expectedModules) {
        expect(status.modules).toContain(moduleName);
      }
    });

    test('should provide access to modules', async () => {
      await app.initialize();
      
      const promptLibrary = app.getModule('prompt-library');
      expect(promptLibrary).toBeDefined();
      
      const promptEngine = app.getModule('prompt-engine');
      expect(promptEngine).toBeDefined();
      
      const windowManager = app.getModule('window-manager');
      expect(windowManager).toBeDefined();
    });
  });

  describe('Configuration Management', () => {
    test('should update configuration', async () => {
      await app.initialize();
      
      const newConfig = {
        ui: {
          theme: 'dark' as const,
          enableAnimations: true,
          windowOpacity: 0.8
        }
      };
      
      expect(() => app.updateConfig(newConfig)).not.toThrow();
      
      const status = app.getStatus();
      expect(status.config.ui.theme).toBe('dark');
      expect(status.config.ui.enableAnimations).toBe(true);
      expect(status.config.ui.windowOpacity).toBe(0.8);
    });
  });

  describe('Event Handling', () => {
    test('should emit lifecycle events', async () => {
      const events: string[] = [];
      
      app.on('initialized', () => events.push('initialized'));
      app.on('started', () => events.push('started'));
      app.on('stopped', () => events.push('stopped'));
      app.on('shutdown', () => events.push('shutdown'));
      
      await app.initialize();
      await app.start();
      await app.stop();
      await app.shutdown();
      
      expect(events).toEqual(['initialized', 'started', 'stopped', 'shutdown']);
    });

    test('should emit configuration update events', async () => {
      await app.initialize();
      
      let configUpdated = false;
      app.on('config-updated', () => {
        configUpdated = true;
      });
      
      app.updateConfig({ ui: { theme: 'dark' } });
      
      expect(configUpdated).toBe(true);
    });
  });

  describe('Error Handling', () => {
    test('should handle initialization errors gracefully', async () => {
      // Create app with invalid configuration
      const invalidApp = new Application({
        database: {
          path: '/invalid/path/database.db',
          enableWAL: true,
          enableForeignKeys: true
        }
      });
      
      await expect(invalidApp.initialize()).rejects.toThrow();
    });

    test('should emit error events', async () => {
      await app.initialize();
      
      let errorEmitted = false;
      app.on('error', () => {
        errorEmitted = true;
      });
      
      // Simulate an error
      app.emit('error', new Error('Test error'));
      
      expect(errorEmitted).toBe(true);
    });
  });

  describe('Status Reporting', () => {
    test('should provide accurate status information', async () => {
      const initialStatus = app.getStatus();
      expect(initialStatus.isInitialized).toBe(false);
      expect(initialStatus.isRunning).toBe(false);
      expect(initialStatus.modules).toEqual([]);
      
      await app.initialize();
      const initializedStatus = app.getStatus();
      expect(initializedStatus.isInitialized).toBe(true);
      expect(initializedStatus.isRunning).toBe(false);
      expect(initializedStatus.modules.length).toBeGreaterThan(0);
      
      await app.start();
      const runningStatus = app.getStatus();
      expect(runningStatus.isInitialized).toBe(true);
      expect(runningStatus.isRunning).toBe(true);
    });
  });
});

describe('Application Module Integration', () => {
  let app: Application;
  
  beforeEach(async () => {
    app = new Application({
      database: {
        path: ':memory:',
        enableWAL: true,
        enableForeignKeys: true
      },
      logging: {
        level: 'error',
        enableFileLogging: false,
        logDirectory: './logs'
      },
      hotkeys: {
        enableGlobalHotkeys: false,
        defaultHotkeys: {}
      },
      voice: {
        enableVoiceInput: false,
        defaultLanguage: 'en-US',
        enableContinuousListening: false
      },
      context: {
        enableScreenCapture: false,
        enableClipboardMonitoring: false,
        ocrLanguage: 'eng'
      }
    });
    
    await app.initialize();
  });

  afterEach(async () => {
    await app.shutdown();
  });

  test('should integrate prompt library and engine', async () => {
    const promptLibrary = app.getModule('prompt-library');
    const promptEngine = app.getModule('prompt-engine');
    
    expect(promptLibrary).toBeDefined();
    expect(promptEngine).toBeDefined();
    
    // Test basic functionality
    expect(typeof promptLibrary.createPrompt).toBe('function');
    expect(typeof promptEngine.processPrompt).toBe('function');
  });

  test('should integrate window manager and context extractor', async () => {
    const windowManager = app.getModule('window-manager');
    const contextExtractor = app.getModule('context-extractor');
    
    expect(windowManager).toBeDefined();
    expect(contextExtractor).toBeDefined();
    
    // Test basic functionality
    expect(typeof windowManager.createWindow).toBe('function');
    expect(typeof contextExtractor.getClipboardText).toBe('function');
  });

  test('should integrate hotkey manager with other modules', async () => {
    const hotkeyManager = app.getModule('hotkey-manager');
    
    expect(hotkeyManager).toBeDefined();
    expect(typeof hotkeyManager.registerAction).toBe('function');
    expect(typeof hotkeyManager.triggerAction).toBe('function');
  });
});

describe('Application Performance', () => {
  test('should initialize within reasonable time', async () => {
    const app = new Application({
      database: { path: ':memory:' },
      logging: {
        level: 'error',
        enableFileLogging: false,
        logDirectory: './logs'
      },
      hotkeys: {
        enableGlobalHotkeys: false,
        defaultHotkeys: {}
      },
      voice: {
        enableVoiceInput: false,
        defaultLanguage: 'en-US',
        enableContinuousListening: false
      },
      context: {
        enableScreenCapture: false,
        enableClipboardMonitoring: false,
        ocrLanguage: 'eng'
      }
    });
    
    const startTime = Date.now();
    await app.initialize();
    const initTime = Date.now() - startTime;
    
    expect(initTime).toBeLessThan(5000); // Should initialize within 5 seconds
    
    await app.shutdown();
  });

  test('should start within reasonable time', async () => {
    const app = new Application({
      database: {
        path: ':memory:',
        enableWAL: true,
        enableForeignKeys: true
      },
      logging: {
        level: 'error',
        enableFileLogging: false,
        logDirectory: './logs'
      },
      hotkeys: {
        enableGlobalHotkeys: false,
        defaultHotkeys: {}
      },
      voice: {
        enableVoiceInput: false,
        defaultLanguage: 'en-US',
        enableContinuousListening: false
      },
      context: {
        enableScreenCapture: false,
        enableClipboardMonitoring: false,
        ocrLanguage: 'eng'
      }
    });
    
    await app.initialize();
    
    const startTime = Date.now();
    await app.start();
    const startupTime = Date.now() - startTime;
    
    expect(startupTime).toBeLessThan(2000); // Should start within 2 seconds
    
    await app.shutdown();
  });
});
