/**
 * Category and tag model definitions
 * Organization and classification models for prompt management
 */

// Category metadata
export interface CategoryMetadata {
  readonly createdAt: Date;
  readonly updatedAt: Date;
  readonly lastUsed?: Date;
  readonly totalUsage: number;
  readonly averageRating?: number;
  readonly customFields: Readonly<Record<string, unknown>>;
}

// Category model
export interface Category {
  readonly id: string;
  name: string;
  description: string;
  readonly parentId?: string;
  color?: string;
  icon?: string;
  sortOrder: number;
  readonly promptCount: number;
  readonly isSystem: boolean;
  readonly metadata: CategoryMetadata;
  
  // Computed properties
  readonly path: readonly string[];
  readonly depth: number;
  readonly hasChildren: boolean;
}

// Category tree structure
export interface CategoryTree {
  readonly category: Category;
  readonly children: readonly CategoryTree[];
  readonly promptCount: number;
  readonly totalPromptCount: number; // Including children
  readonly depth: number;
}

// Tag model
export interface Tag {
  readonly name: string;
  color?: string;
  description?: string;
  readonly usageCount: number;
  readonly createdAt: Date;
  readonly lastUsedAt?: Date;
  readonly relatedTags: readonly string[];
  readonly category?: string;
}

// Tag co-occurrence data
export interface TagCoOccurrence {
  readonly tagName: string;
  readonly frequency: number;
  readonly strength: number; // 0-1
}

// Tag statistics
export interface TagStatistics {
  readonly tag: Tag;
  readonly promptCount: number;
  readonly recentUsage: number; // Last 30 days
  readonly trending: boolean;
  readonly coOccurringTags: readonly TagCoOccurrence[];
}

// Category creation/update DTOs
export interface CreateCategoryRequest {
  name: string;
  description: string;
  parentId?: string;
  color?: string;
  icon?: string;
  sortOrder?: number;
}

export interface UpdateCategoryRequest {
  name?: string;
  description?: string;
  parentId?: string;
  color?: string;
  icon?: string;
  sortOrder?: number;
}

// Tag creation/update DTOs
export interface CreateTagRequest {
  name: string;
  color?: string;
  description?: string;
  category?: string;
}

export interface UpdateTagRequest {
  color?: string;
  description?: string;
  category?: string;
}

// Category search and filtering
export interface CategorySearchCriteria {
  query?: string;
  parentId?: string;
  isSystem?: boolean;
  hasPrompts?: boolean;
  sortBy?: 'name' | 'createdAt' | 'updatedAt' | 'promptCount' | 'sortOrder';
  sortOrder?: 'asc' | 'desc';
  includeEmpty?: boolean;
}

export interface CategorySearchResult {
  categories: readonly Category[];
  total: number;
  tree?: CategoryTree;
}

// Tag search and filtering
export interface TagSearchCriteria {
  query?: string;
  category?: string;
  minUsageCount?: number;
  trending?: boolean;
  sortBy?: 'name' | 'usageCount' | 'createdAt' | 'lastUsedAt';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
}

export interface TagSearchResult {
  tags: readonly Tag[];
  total: number;
  statistics?: readonly TagStatistics[];
}

// Category operations
export interface CategoryOperationResult {
  success: boolean;
  category?: Category;
  error?: string;
  affectedPrompts?: number;
}

export interface TagOperationResult {
  success: boolean;
  tag?: Tag;
  error?: string;
  affectedPrompts?: number;
}

// Category validation
export interface CategoryValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

export interface TagValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

// Category analytics
export interface CategoryAnalytics {
  readonly categoryId: string;
  readonly promptCount: number;
  readonly totalUsage: number;
  readonly averageRating: number;
  readonly topTags: readonly string[];
  readonly usageTrend: readonly {
    date: Date;
    count: number;
  }[];
  readonly popularPrompts: readonly string[];
}

export interface TagAnalytics {
  readonly tagName: string;
  readonly usageCount: number;
  readonly promptCount: number;
  readonly categories: readonly string[];
  readonly relatedTags: readonly TagCoOccurrence[];
  readonly usageTrend: readonly {
    date: Date;
    count: number;
  }[];
}

// Category import/export
export interface CategoryExportData {
  categories: readonly Category[];
  tags: readonly Tag[];
  relationships: readonly {
    categoryId: string;
    parentId?: string;
    tags: string[];
  }[];
  metadata: {
    exportedAt: Date;
    version: string;
    totalCategories: number;
    totalTags: number;
  };
}

export interface CategoryImportOptions {
  mergeStrategy: 'replace' | 'merge' | 'skip';
  preserveIds: boolean;
  validateStructure: boolean;
  createMissing: boolean;
}

export interface CategoryImportResult {
  success: boolean;
  imported: {
    categories: number;
    tags: number;
  };
  skipped: {
    categories: number;
    tags: number;
  };
  errors: string[];
  warnings: string[];
}
