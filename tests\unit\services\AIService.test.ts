/**
 * AI Service unit tests
 */

import { AIService } from '../../../src/services/ai/AIService';
import { BaseAIProvider } from '../../../src/services/ai/BaseAIProvider';
import { 
  AIProvider, 
  AIProviderConfig, 
  AIModel, 
  AIRequest, 
  AIResponse, 
  AIStreamChunk,
  // AIUsageStats
} from '../../../src/services/ai/types';

// Mock configuration manager
jest.mock('../../../src/core/config', () => ({
  getConfigManager: () => ({
    get: jest.fn((key: string) => {
      const config: Record<string, any> = {
        'ai.openai.apiKey': 'test-api-key',
        'ai.openai.model': 'gpt-3.5-turbo'
      };
      return config[key];
    })
  })
}));

// Mock logger
jest.mock('../../../src/core/logger', () => ({
  LoggerFactory: {
    getInstance: () => ({
      getLogger: () => ({
        info: jest.fn(),
        debug: jest.fn(),
        warn: jest.fn(),
        error: jest.fn()
      })
    })
  }
}));

// Mock fetch for HTTP requests
global.fetch = jest.fn();

/**
 * Mock AI Provider for testing
 */
class MockAIProvider extends BaseAIProvider {
  private mockModels: AIModel[] = [
    {
      id: 'mock-model-1',
      name: 'Mock Model 1',
      provider: 'mock' as AIProvider,
      description: 'Test model',
      capabilities: {
        maxTokens: 4096,
        supportsStreaming: true,
        supportsImages: false,
        supportsTools: false,
        supportsFunctions: false,
        contextWindow: 4096,
        costPer1kTokens: { input: 0.001, output: 0.002 }
      },
      isAvailable: true
    }
  ];

  constructor(config: AIProviderConfig) {
    super('mock' as AIProvider, config);
  }

  async getModels(): Promise<AIModel[]> {
    return this.mockModels;
  }

  protected async makeRequest(_request: AIRequest): Promise<AIResponse> {
    return {
      id: 'mock-response-id',
      model: 'mock-model-1',
      provider: 'mock' as AIProvider,
      content: 'Mock response content',
      finishReason: 'stop',
      usage: {
        promptTokens: 10,
        completionTokens: 20,
        totalTokens: 30,
        cost: 0.001
      },
      timestamp: new Date(),
      latency: 100
    };
  }

  protected async* makeStreamRequest(_request: AIRequest): AsyncIterable<AIStreamChunk> {
    const chunks = [
      {
        id: 'mock-chunk-1',
        model: 'mock-model-1',
        provider: 'mock' as AIProvider,
        delta: { content: 'Mock ' }
      },
      {
        id: 'mock-chunk-2',
        model: 'mock-model-1',
        provider: 'mock' as AIProvider,
        delta: { content: 'stream ' }
      },
      {
        id: 'mock-chunk-3',
        model: 'mock-model-1',
        provider: 'mock' as AIProvider,
        delta: { content: 'response' },
        finishReason: 'stop',
        usage: { promptTokens: 10, completionTokens: 20, totalTokens: 30 }
      }
    ];

    for (const chunk of chunks) {
      yield chunk;
    }
  }
}

describe('AIService', () => {
  let aiService: AIService;
  let mockProvider: MockAIProvider;

  beforeEach(async () => {
    aiService = new AIService({
      defaultProvider: 'mock' as AIProvider,
      fallbackProviders: ['mock' as AIProvider],
      enableCaching: false, // Disable caching for tests
      enableRateLimiting: false // Disable rate limiting for tests
    });

    mockProvider = new MockAIProvider({
      provider: 'mock' as AIProvider,
      apiKey: 'test-key',
      enabled: true
    });

    await mockProvider.initialize();
    aiService.registerProvider(mockProvider);
    await aiService.initialize();
  });

  describe('Initialization', () => {
    test('should initialize successfully', () => {
      expect(aiService).toBeDefined();
    });

    test('should register providers', () => {
      const providers = aiService.getAvailableProviders();
      expect(providers).toContain('mock');
    });

    test('should set default provider', () => {
      const config = aiService.getConfig();
      expect(config.defaultProvider).toBe('mock');
    });
  });

  describe('Provider Management', () => {
    test('should get registered provider', () => {
      const provider = aiService.getProvider('mock' as AIProvider);
      expect(provider).toBe(mockProvider);
    });

    test('should return null for unregistered provider', () => {
      const provider = aiService.getProvider('nonexistent' as AIProvider);
      expect(provider).toBeNull();
    });

    test('should change default provider', () => {
      aiService.setDefaultProvider('mock' as AIProvider);
      const config = aiService.getConfig();
      expect(config.defaultProvider).toBe('mock');
    });

    test('should throw error when setting invalid default provider', () => {
      expect(() => {
        aiService.setDefaultProvider('invalid' as AIProvider);
      }).toThrow('Provider invalid is not registered');
    });
  });

  describe('Model Operations', () => {
    test('should get available models', async () => {
      const models = await aiService.getAvailableModels();
      expect(models.length).toBeGreaterThan(0);
      expect(models.some(m => m.id === 'mock-model-1')).toBe(true);
    });

    test('should get specific model', async () => {
      const model = await aiService.getModel('mock-model-1');
      expect(model).toBeDefined();
      expect(model?.id).toBe('mock-model-1');
    });

    test('should return null for non-existent model', async () => {
      const model = await aiService.getModel('non-existent-model');
      expect(model).toBeNull();
    });

    test('should get model from specific provider', async () => {
      const model = await aiService.getModel('mock-model-1', 'mock' as AIProvider);
      expect(model).toBeDefined();
      expect(model?.provider).toBe('mock');
    });
  });

  describe('Request Operations', () => {
    const testRequest: AIRequest = {
      messages: [
        { role: 'user', content: 'Hello, world!' }
      ],
      options: {
        model: 'mock-model-1',
        maxTokens: 100,
        temperature: 0.7
      }
    };

    test('should complete request', async () => {
      const response = await aiService.complete(testRequest);
      
      expect(response).toBeDefined();
      expect(response.content).toBe('Mock response content');
      expect(response.provider).toBe('mock');
      expect(response.usage.totalTokens).toBe(30);
    });

    test('should complete request with specific provider', async () => {
      const response = await aiService.complete(testRequest, 'mock' as AIProvider);
      
      expect(response).toBeDefined();
      expect(response.provider).toBe('mock');
    });

    test('should stream request', async () => {
      const chunks: AIStreamChunk[] = [];
      
      for await (const chunk of aiService.stream(testRequest)) {
        chunks.push(chunk);
      }
      
      expect(chunks).toHaveLength(3);
      expect(chunks[0].delta.content).toBe('Mock ');
      expect(chunks[1].delta.content).toBe('stream ');
      expect(chunks[2].delta.content).toBe('response');
      expect(chunks[2].finishReason).toBe('stop');
    });

    test('should stream request with specific provider', async () => {
      const chunks: AIStreamChunk[] = [];
      
      for await (const chunk of aiService.stream(testRequest, 'mock' as AIProvider)) {
        chunks.push(chunk);
      }
      
      expect(chunks).toHaveLength(3);
    });
  });

  describe('Context Management', () => {
    test('should create context', () => {
      const context = aiService.createContext();
      
      expect(context).toBeDefined();
      expect(context.conversationId).toBeDefined();
      expect(context.sessionId).toBeDefined();
      expect(context.history).toEqual([]);
      expect(context.maxHistoryLength).toBe(10);
    });

    test('should create context with options', () => {
      const context = aiService.createContext({
        userId: 'test-user',
        maxHistoryLength: 20
      });
      
      expect(context.userId).toBe('test-user');
      expect(context.maxHistoryLength).toBe(20);
    });

    test('should update context', () => {
      const context = aiService.createContext();
      const updatedContext = aiService.updateContext(context, {
        userId: 'new-user'
      });
      
      expect(updatedContext.userId).toBe('new-user');
      expect(updatedContext.conversationId).toBe(context.conversationId);
    });
  });

  describe('Utility Operations', () => {
    test('should estimate tokens', async () => {
      const tokens = await aiService.estimateTokens('Hello, world!');
      expect(tokens).toBeGreaterThan(0);
    });

    test('should estimate tokens with model and provider', async () => {
      const tokens = await aiService.estimateTokens(
        'Hello, world!',
        'mock-model-1',
        'mock' as AIProvider
      );
      expect(tokens).toBeGreaterThan(0);
    });

    test('should estimate cost', async () => {
      const cost = await aiService.estimateCost(1000, 'mock-model-1');
      expect(cost).toBeGreaterThan(0);
    });

    test('should return 0 cost for unknown model', async () => {
      const cost = await aiService.estimateCost(1000, 'unknown-model');
      expect(cost).toBe(0);
    });
  });

  describe('Configuration', () => {
    test('should update configuration', () => {
      const newConfig = {
        enableCaching: true,
        cacheTimeout: 600000
      };
      
      aiService.configure(newConfig);
      const config = aiService.getConfig();
      
      expect(config.enableCaching).toBe(true);
      expect(config.cacheTimeout).toBe(600000);
    });

    test('should get current configuration', () => {
      const config = aiService.getConfig();
      
      expect(config).toBeDefined();
      expect(config.defaultProvider).toBe('mock');
      expect(config.fallbackProviders).toContain('mock');
    });
  });

  describe('Error Handling', () => {
    test('should throw error when not initialized', async () => {
      const uninitializedService = new AIService();
      
      await expect(uninitializedService.complete({
        messages: [{ role: 'user', content: 'test' }]
      })).rejects.toThrow('AI Service not initialized');
    });

    test('should handle provider errors gracefully', async () => {
      // Create a provider that always throws
      const errorProvider = new MockAIProvider({
        provider: 'error' as AIProvider,
        apiKey: 'test-key',
        enabled: true
      });
      
      (errorProvider as any).makeRequest = jest.fn().mockRejectedValue(new Error('Provider error'));
      
      const errorService = new AIService({
        defaultProvider: 'error' as AIProvider,
        fallbackProviders: []
      });
      
      errorService.registerProvider(errorProvider);
      await errorService.initialize();
      
      await expect(errorService.complete({
        messages: [{ role: 'user', content: 'test' }]
      })).rejects.toThrow('All providers failed');
    });
  });

  describe('Events', () => {
    test('should emit completion events', async () => {
      const completionHandler = jest.fn();
      aiService.on('completion', completionHandler);
      
      await aiService.complete({
        messages: [{ role: 'user', content: 'test' }]
      });
      
      expect(completionHandler).toHaveBeenCalledWith(
        expect.objectContaining({
          provider: 'mock',
          model: 'mock-model-1',
          tokens: 30
        })
      );
    });

    test('should emit stream chunk events', async () => {
      const chunkHandler = jest.fn();
      aiService.on('stream-chunk', chunkHandler);
      
      const chunks: AIStreamChunk[] = [];
      for await (const chunk of aiService.stream({
        messages: [{ role: 'user', content: 'test' }]
      })) {
        chunks.push(chunk);
      }
      
      expect(chunkHandler).toHaveBeenCalledTimes(3);
    });
  });
});
