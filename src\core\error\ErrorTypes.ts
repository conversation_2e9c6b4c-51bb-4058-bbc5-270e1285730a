/**
 * Error classification and type definitions
 * Provides structured error types and classification utilities
 */

import { v4 as uuidv4 } from 'uuid';
import { 
  AppError, 
  ErrorDetails, 
  ErrorContext, 
  ErrorCategory, 
  ErrorSeverity, 
  ErrorCode,
  ErrorCodes 
} from './types';

/**
 * Base application error class
 */
export class ApplicationError extends Error implements AppError {
  public readonly id: string;
  public readonly code: string;
  public readonly category: ErrorCategory;
  public readonly severity: ErrorSeverity;
  public readonly context: ErrorContext;
  public readonly details: ErrorDetails;
  public readonly cause?: Error;
  public readonly recoverable: boolean;
  public readonly timestamp: Date;
  public readonly stackTrace?: string;

  constructor(
    details: Partial<ErrorDetails>,
    context: Partial<ErrorContext>,
    cause?: Error
  ) {
    const errorDetails: ErrorDetails = {
      code: details.code || ErrorCodes.UNKNOWN_ERROR,
      message: details.message || 'An unknown error occurred',
      category: details.category || 'unknown',
      severity: details.severity || 'medium',
      recoverable: details.recoverable ?? true,
      userMessage: details.userMessage,
      technicalMessage: details.technicalMessage,
      suggestions: details.suggestions,
      documentation: details.documentation
    };

    super(errorDetails.message);

    this.id = uuidv4();
    this.code = errorDetails.code;
    this.category = errorDetails.category;
    this.severity = errorDetails.severity;
    this.recoverable = errorDetails.recoverable;
    this.details = errorDetails;
    this.cause = cause;
    this.timestamp = new Date();
    this.stackTrace = this.stack;

    this.context = {
      component: context.component || 'unknown',
      operation: context.operation || 'unknown',
      timestamp: this.timestamp,
      userId: context.userId,
      sessionId: context.sessionId,
      requestId: context.requestId,
      userAgent: context.userAgent,
      url: context.url,
      metadata: context.metadata
    };

    // Maintain proper prototype chain
    Object.setPrototypeOf(this, ApplicationError.prototype);
  }

  /**
   * Convert to JSON for serialization
   */
  toJSON(): any {
    return {
      id: this.id,
      code: this.code,
      category: this.category,
      severity: this.severity,
      message: this.message,
      context: this.context,
      details: this.details,
      recoverable: this.recoverable,
      timestamp: this.timestamp.toISOString(),
      stackTrace: this.stackTrace,
      cause: this.cause ? {
        name: this.cause.name,
        message: this.cause.message,
        stack: this.cause.stack
      } : undefined
    };
  }

  /**
   * Create from JSON
   */
  static fromJSON(json: any): ApplicationError {
    const error = new ApplicationError(
      json.details,
      json.context,
      json.cause ? new Error(json.cause.message) : undefined
    );
    
    // Restore properties
    (error as any).id = json.id;
    (error as any).timestamp = new Date(json.timestamp);
    
    return error;
  }
}

/**
 * Validation error
 */
export class ValidationError extends ApplicationError {
  constructor(
    message: string,
    field?: string,
    value?: any,
    context?: Partial<ErrorContext>
  ) {
    super(
      {
        code: ErrorCodes.VALIDATION_FAILED,
        message,
        category: 'validation',
        severity: 'medium',
        recoverable: true,
        userMessage: `Validation failed: ${message}`,
        technicalMessage: `Field '${field}' with value '${value}' failed validation`,
        suggestions: ['Please check your input and try again']
      },
      {
        ...context,
        metadata: { field, value, ...context?.metadata }
      }
    );
  }
}

/**
 * Network error
 */
export class NetworkError extends ApplicationError {
  constructor(
    message: string,
    statusCode?: number,
    endpoint?: string,
    context?: Partial<ErrorContext>
  ) {
    super(
      {
        code: ErrorCodes.NETWORK_ERROR,
        message,
        category: 'network',
        severity: statusCode && statusCode >= 500 ? 'high' : 'medium',
        recoverable: true,
        userMessage: 'Network connection failed. Please check your internet connection.',
        technicalMessage: `Network request failed: ${message}`,
        suggestions: [
          'Check your internet connection',
          'Try again in a few moments',
          'Contact support if the problem persists'
        ]
      },
      {
        ...context,
        metadata: { statusCode, endpoint, ...context?.metadata }
      }
    );
  }
}

/**
 * File system error
 */
export class FileSystemError extends ApplicationError {
  constructor(
    message: string,
    path?: string,
    operation?: string,
    context?: Partial<ErrorContext>
  ) {
    super(
      {
        code: ErrorCodes.FILE_NOT_FOUND,
        message,
        category: 'filesystem',
        severity: 'medium',
        recoverable: true,
        userMessage: 'File operation failed. Please check file permissions.',
        technicalMessage: `File system operation '${operation}' failed: ${message}`,
        suggestions: [
          'Check file permissions',
          'Ensure the file path is correct',
          'Try running as administrator if needed'
        ]
      },
      {
        ...context,
        metadata: { path, operation, ...context?.metadata }
      }
    );
  }
}

/**
 * Database error
 */
export class DatabaseError extends ApplicationError {
  constructor(
    message: string,
    query?: string,
    context?: Partial<ErrorContext>
  ) {
    super(
      {
        code: ErrorCodes.DATABASE_CONNECTION_FAILED,
        message,
        category: 'database',
        severity: 'high',
        recoverable: true,
        userMessage: 'Database operation failed. Please try again.',
        technicalMessage: `Database operation failed: ${message}`,
        suggestions: [
          'Try the operation again',
          'Check database connectivity',
          'Contact support if the problem persists'
        ]
      },
      {
        ...context,
        metadata: { query, ...context?.metadata }
      }
    );
  }
}

/**
 * AI Provider error
 */
export class AIProviderError extends ApplicationError {
  constructor(
    message: string,
    provider?: string,
    model?: string,
    context?: Partial<ErrorContext>
  ) {
    super(
      {
        code: ErrorCodes.AI_API_ERROR,
        message,
        category: 'ai_provider',
        severity: 'medium',
        recoverable: true,
        userMessage: 'AI service is temporarily unavailable. Please try again.',
        technicalMessage: `AI provider '${provider}' error: ${message}`,
        suggestions: [
          'Try again in a few moments',
          'Check your API key configuration',
          'Try a different AI model if available'
        ]
      },
      {
        ...context,
        metadata: { provider, model, ...context?.metadata }
      }
    );
  }
}

/**
 * STT Provider error
 */
export class STTProviderError extends ApplicationError {
  constructor(
    message: string,
    provider?: string,
    language?: string,
    context?: Partial<ErrorContext>
  ) {
    super(
      {
        code: ErrorCodes.STT_API_ERROR,
        message,
        category: 'stt_provider',
        severity: 'medium',
        recoverable: true,
        userMessage: 'Speech recognition service is temporarily unavailable.',
        technicalMessage: `STT provider '${provider}' error: ${message}`,
        suggestions: [
          'Try again in a few moments',
          'Check your microphone permissions',
          'Ensure your audio quality is good'
        ]
      },
      {
        ...context,
        metadata: { provider, language, ...context?.metadata }
      }
    );
  }
}

/**
 * Configuration error
 */
export class ConfigurationError extends ApplicationError {
  constructor(
    message: string,
    configKey?: string,
    context?: Partial<ErrorContext>
  ) {
    super(
      {
        code: ErrorCodes.CONFIG_INVALID,
        message,
        category: 'configuration',
        severity: 'high',
        recoverable: false,
        userMessage: 'Application configuration error. Please check your settings.',
        technicalMessage: `Configuration error for '${configKey}': ${message}`,
        suggestions: [
          'Check your configuration settings',
          'Reset to default configuration',
          'Contact support for assistance'
        ]
      },
      {
        ...context,
        metadata: { configKey, ...context?.metadata }
      }
    );
  }
}

/**
 * Business logic error
 */
export class BusinessLogicError extends ApplicationError {
  constructor(
    message: string,
    rule?: string,
    context?: Partial<ErrorContext>
  ) {
    super(
      {
        code: ErrorCodes.BUSINESS_RULE_VIOLATION,
        message,
        category: 'business_logic',
        severity: 'medium',
        recoverable: true,
        userMessage: message,
        technicalMessage: `Business rule '${rule}' violation: ${message}`,
        suggestions: ['Please review your input and try again']
      },
      {
        ...context,
        metadata: { rule, ...context?.metadata }
      }
    );
  }
}

/**
 * System error
 */
export class SystemError extends ApplicationError {
  constructor(
    message: string,
    systemInfo?: any,
    context?: Partial<ErrorContext>
  ) {
    super(
      {
        code: ErrorCodes.SYSTEM_ERROR,
        message,
        category: 'system',
        severity: 'critical',
        recoverable: false,
        userMessage: 'A system error occurred. Please restart the application.',
        technicalMessage: `System error: ${message}`,
        suggestions: [
          'Restart the application',
          'Check system resources',
          'Contact support if the problem persists'
        ]
      },
      {
        ...context,
        metadata: { systemInfo, ...context?.metadata }
      }
    );
  }
}

/**
 * Error classification utilities
 */
export class ErrorClassifier {
  /**
   * Classify an error based on its properties
   */
  static classify(error: Error): { category: ErrorCategory; severity: ErrorSeverity } {
    if (error instanceof ApplicationError) {
      return { category: error.category, severity: error.severity };
    }

    // Classify based on error type and message
    const message = error.message.toLowerCase();
    const name = error.name.toLowerCase();

    // Network errors
    if (name.includes('network') || message.includes('network') || 
        message.includes('fetch') || message.includes('timeout')) {
      return { category: 'network', severity: 'medium' };
    }

    // File system errors
    if (name.includes('enoent') || name.includes('eacces') || 
        message.includes('file') || message.includes('directory')) {
      return { category: 'filesystem', severity: 'medium' };
    }

    // Validation errors
    if (name.includes('validation') || message.includes('invalid') || 
        message.includes('required')) {
      return { category: 'validation', severity: 'low' };
    }

    // Default classification
    return { category: 'unknown', severity: 'medium' };
  }

  /**
   * Determine if an error is recoverable
   */
  static isRecoverable(error: Error): boolean {
    if (error instanceof ApplicationError) {
      return error.recoverable;
    }

    const { category, severity } = this.classify(error);
    
    // Critical system errors are usually not recoverable
    if (severity === 'critical') {
      return false;
    }

    // Configuration errors are usually not recoverable at runtime
    if (category === 'configuration') {
      return false;
    }

    // Most other errors are recoverable
    return true;
  }
}
