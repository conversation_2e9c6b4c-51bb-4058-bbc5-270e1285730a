/**
 * Main Prompt Engine implementation
 * Core prompt enhancement, processing, and transformation capabilities
 */

import { EventEmitter } from 'events';
import {
  IPromptEngine,
  PromptEngineConfig,
  EnhancementOptions,
  PromptTemplate,
  ContextSource,
  SummarizationOptions,
  PromptEngineError,
  PromptEngineEvent,
  PromptAnalytics
} from './types';
import { AIServiceManager } from './AIServiceManager';
import { TemplateProcessor } from './TemplateProcessor';
import { ContextInjector } from './ContextInjector';
import { PromptCache } from './PromptCache';
import { ConversationManager } from './ConversationManager';
import { getContextExtractor } from '../context-extractor';
import { LoggerFactory } from '../../core/logger';
import { toError } from '../../core/utils/errorUtils';

const logger = LoggerFactory.getInstance().getLogger('PromptEngine');

/**
 * Main Prompt Engine implementation
 */
export class PromptEngine extends EventEmitter implements IPromptEngine {
  private config: PromptEngineConfig;
  private aiService: AIServiceManager;
  private templateProcessor: TemplateProcessor;
  private contextInjector: ContextInjector;
  private cache: PromptCache;
  private conversationManager: ConversationManager;
  private isInitialized: boolean = false;
  private metrics: {
    totalEnhancements: number;
    totalTemplateProcessing: number;
    totalContextInjections: number;
    totalResponseTime: number;
    requestCount: number;
    errorCount: number;
    startTime: Date;
  };

  constructor(config?: Partial<PromptEngineConfig>) {
    super();
    
    this.config = {
      ai: {
        provider: 'openai',
        defaultModel: 'gpt-3.5-turbo',
        timeout: 30000,
        retries: 3,
        ...config?.ai
      },
      cache: {
        maxSize: 100 * 1024 * 1024, // 100MB
        ttl: 3600000, // 1 hour
        maxEntries: 1000,
        ...config?.cache
      },
      templates: {
        builtInEnabled: true,
        autoSave: true,
        ...config?.templates
      },
      context: {
        maxSources: 5,
        maxContentLength: 10000,
        enableSensitiveFilter: true,
        ...config?.context
      },
      enhancement: {
        defaultStyle: 'professional',
        defaultLength: 'detailed',
        defaultTone: 'friendly',
        maxVariations: 5,
        ...config?.enhancement
      }
    };

    this.initializeComponents();
    this.initializeMetrics();
  }

  /**
   * Initialize all components
   */
  private initializeComponents(): void {
    this.aiService = new AIServiceManager(this.config.ai);
    this.templateProcessor = new TemplateProcessor();
    this.contextInjector = new ContextInjector(this.config.context);
    this.cache = new PromptCache(this.config.cache);
    this.conversationManager = new ConversationManager();
  }

  /**
   * Initialize metrics tracking
   */
  private initializeMetrics(): void {
    this.metrics = {
      totalEnhancements: 0,
      totalTemplateProcessing: 0,
      totalContextInjections: 0,
      totalResponseTime: 0,
      requestCount: 0,
      errorCount: 0,
      startTime: new Date()
    };
  }

  /**
   * Initialize the prompt engine
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      await this.aiService.initialize();
      await this.templateProcessor.initialize();
      
      if (this.config.templates.builtInEnabled) {
        await this.templateProcessor.loadBuiltInTemplates();
      }

      this.isInitialized = true;
      logger.info('Prompt engine initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize prompt engine', { error });
      throw new PromptEngineError('Failed to initialize prompt engine', error);
    }
  }

  /**
   * Enhance a prompt using AI
   */
  async enhance(prompt: string, options: EnhancementOptions = {}): Promise<string> {
    const startTime = Date.now();
    
    try {
      this.emit('enhancement-start', { prompt: prompt.substring(0, 100), options });

      // Check cache first
      const cacheKey = this.generateCacheKey('enhance', prompt, options);
      const cached = this.cache.get(cacheKey);
      if (cached) {
        this.emit('cache-hit', { key: cacheKey });
        return cached;
      }

      // Apply default options
      const enhancementOptions: EnhancementOptions = {
        style: this.config.enhancement.defaultStyle as any,
        length: this.config.enhancement.defaultLength as any,
        tone: this.config.enhancement.defaultTone as any,
        temperature: 0.7,
        maxTokens: 1000,
        ...options
      };

      // Inject context if specified
      let contextualPrompt = prompt;
      if (enhancementOptions.context && enhancementOptions.context.length > 0) {
        contextualPrompt = await this.injectContext(prompt, enhancementOptions.context);
      }

      // Enhance with AI service
      const enhanceOptions = {
        model: enhancementOptions.model || this.config.ai.defaultModel,
        ...(enhancementOptions.temperature !== undefined && { temperature: enhancementOptions.temperature }),
        ...(enhancementOptions.maxTokens !== undefined && { maxTokens: enhancementOptions.maxTokens }),
        ...(enhancementOptions.style !== undefined && { style: enhancementOptions.style }),
        ...(enhancementOptions.length !== undefined && { length: enhancementOptions.length }),
        ...(enhancementOptions.tone !== undefined && { tone: enhancementOptions.tone })
      };
      const enhanced = await this.aiService.enhance(contextualPrompt, enhanceOptions);

      // Cache the result
      this.cache.set(cacheKey, enhanced);
      this.emit('cache-miss', { key: cacheKey });

      // Update metrics
      this.updateMetrics('enhancement', Date.now() - startTime);

      // Track analytics
      const analytics: PromptAnalytics = {
        originalLength: prompt.length,
        enhancedLength: enhanced.length,
        processingTime: Date.now() - startTime,
        tokensUsed: Math.ceil((prompt.length + enhanced.length) / 4), // Rough estimate
        model: enhancementOptions.model || this.config.ai.defaultModel,
        timestamp: new Date()
      };

      this.emit('enhancement-complete', { original: prompt, enhanced, analytics });
      
      logger.info('Prompt enhanced successfully', { 
        originalLength: prompt.length,
        enhancedLength: enhanced.length,
        processingTime: Date.now() - startTime
      });

      return enhanced;
    } catch (error) {
      this.metrics.errorCount++;
      logger.error('Failed to enhance prompt', { error, prompt: prompt.substring(0, 100) });
      this.emit('error', new PromptEngineError('Failed to enhance prompt', toError(error)));
      throw error;
    }
  }

  /**
   * Process a template with variables
   */
  async processTemplate(template: PromptTemplate, variables: Record<string, any>): Promise<string> {
    const startTime = Date.now();

    try {
      this.emit('template-processing-start', { templateId: template.id, variables });

      // Validate template
      const validation = this.templateProcessor.validate(template.template);
      if (!validation.valid) {
        throw new PromptEngineError(`Template validation failed: ${validation.error}`);
      }

      // Process template
      const result = this.templateProcessor.process(template.template, variables);

      // Update metrics
      this.updateMetrics('template', Date.now() - startTime);

      this.emit('template-processed', { 
        templateId: template.id, 
        result: result.substring(0, 100),
        processingTime: Date.now() - startTime
      });

      logger.info('Template processed successfully', { 
        templateId: template.id,
        resultLength: result.length,
        processingTime: Date.now() - startTime
      });

      return result;
    } catch (error) {
      this.metrics.errorCount++;
      logger.error('Failed to process template', { error, templateId: template.id });
      this.emit('error', new PromptEngineError('Failed to process template', toError(error)));
      throw error;
    }
  }

  /**
   * Inject context into a prompt
   */
  async injectContext(prompt: string, contextSources: ContextSource[]): Promise<string> {
    const startTime = Date.now();

    try {
      this.emit('context-injection-start', { prompt: prompt.substring(0, 100), contextSources });

      const contextualPrompt = await this.contextInjector.injectContext(prompt, contextSources);

      // Update metrics
      this.updateMetrics('context', Date.now() - startTime);

      this.emit('context-injected', { 
        originalLength: prompt.length,
        contextualLength: contextualPrompt.length,
        sources: contextSources,
        processingTime: Date.now() - startTime
      });

      logger.info('Context injected successfully', { 
        originalLength: prompt.length,
        contextualLength: contextualPrompt.length,
        sources: contextSources.length,
        processingTime: Date.now() - startTime
      });

      return contextualPrompt;
    } catch (error) {
      this.metrics.errorCount++;
      logger.error('Failed to inject context', { error, contextSources });
      this.emit('error', new PromptEngineError('Failed to inject context', toError(error)));
      throw error;
    }
  }

  /**
   * Generate variations of a prompt
   */
  async generateVariations(prompt: string, count: number): Promise<string[]> {
    const startTime = Date.now();

    try {
      if (count > this.config.enhancement.maxVariations) {
        count = this.config.enhancement.maxVariations;
        logger.warn('Variation count limited to maximum', { 
          requested: count, 
          max: this.config.enhancement.maxVariations 
        });
      }

      this.emit('variation-generation-start', { prompt: prompt.substring(0, 100), count });

      const variations = await this.aiService.generateVariations(prompt, count, {
        model: this.config.ai.defaultModel,
        temperature: 0.8 // Higher temperature for more variety
      });

      this.emit('variation-generated', { 
        originalPrompt: prompt.substring(0, 100),
        variationCount: variations.length,
        processingTime: Date.now() - startTime
      });

      logger.info('Variations generated successfully', { 
        count: variations.length,
        processingTime: Date.now() - startTime
      });

      return variations;
    } catch (error) {
      this.metrics.errorCount++;
      logger.error('Failed to generate variations', { error, prompt: prompt.substring(0, 100) });
      this.emit('error', new PromptEngineError('Failed to generate variations', toError(error)));
      throw error;
    }
  }

  /**
   * Summarize text
   */
  async summarize(text: string, options: SummarizationOptions = {}): Promise<string> {
    const startTime = Date.now();

    try {
      const summarizeOptions = {
        model: this.config.ai.defaultModel,
        ...(options.length !== undefined && { length: options.length }),
        ...(options.style !== undefined && { style: options.style }),
        ...(options.audience !== undefined && { audience: options.audience })
      };
      const summary = await this.aiService.summarize(text, summarizeOptions);

      logger.info('Text summarized successfully', { 
        originalLength: text.length,
        summaryLength: summary.length,
        processingTime: Date.now() - startTime
      });

      return summary;
    } catch (error) {
      this.metrics.errorCount++;
      logger.error('Failed to summarize text', { error, textLength: text.length });
      this.emit('error', new PromptEngineError('Failed to summarize text', toError(error)));
      throw error;
    }
  }

  /**
   * Translate text
   */
  async translate(text: string, targetLanguage: string): Promise<string> {
    const startTime = Date.now();

    try {
      const translated = await this.aiService.translate(text, targetLanguage, {
        model: this.config.ai.defaultModel
      });

      logger.info('Text translated successfully', { 
        originalLength: text.length,
        translatedLength: translated.length,
        targetLanguage,
        processingTime: Date.now() - startTime
      });

      return translated;
    } catch (error) {
      this.metrics.errorCount++;
      logger.error('Failed to translate text', { error, targetLanguage });
      this.emit('error', new PromptEngineError('Failed to translate text', toError(error)));
      throw error;
    }
  }

  /**
   * Get engine metrics
   */
  getMetrics() {
    const uptime = Date.now() - this.metrics.startTime.getTime();
    const averageResponseTime = this.metrics.requestCount > 0 ? 
      this.metrics.totalResponseTime / this.metrics.requestCount : 0;

    return {
      totalEnhancements: this.metrics.totalEnhancements,
      totalTemplateProcessing: this.metrics.totalTemplateProcessing,
      totalContextInjections: this.metrics.totalContextInjections,
      averageResponseTime,
      cacheHitRate: this.cache.getHitRate(),
      errorRate: this.metrics.requestCount > 0 ? 
        this.metrics.errorCount / this.metrics.requestCount : 0,
      uptime,
      lastResetTime: this.metrics.startTime
    };
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<PromptEngineConfig>): void {
    this.config = { ...this.config, ...config };
    
    if (config.ai) {
      this.aiService.updateConfig(config.ai);
    }
    
    if (config.cache) {
      this.cache.updateConfig(config.cache);
    }

    logger.info('Prompt engine configuration updated');
  }

  /**
   * Get current configuration
   */
  getConfig(): PromptEngineConfig {
    return { ...this.config };
  }

  /**
   * Generate cache key
   */
  private generateCacheKey(operation: string, input: string, options: any): string {
    const data = { operation, input, options };
    return Buffer.from(JSON.stringify(data)).toString('base64');
  }

  /**
   * Update metrics
   */
  private updateMetrics(operation: string, responseTime: number): void {
    this.metrics.requestCount++;
    this.metrics.totalResponseTime += responseTime;

    switch (operation) {
      case 'enhancement':
        this.metrics.totalEnhancements++;
        break;
      case 'template':
        this.metrics.totalTemplateProcessing++;
        break;
      case 'context':
        this.metrics.totalContextInjections++;
        break;
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    try {
      this.cache.clear();
      this.removeAllListeners();
      
      logger.info('Prompt engine cleaned up');
    } catch (error) {
      logger.error('Error during prompt engine cleanup', { error });
    }
  }
}

// Global instance management
let globalPromptEngine: PromptEngine | null = null;

export const getPromptEngine = (): PromptEngine => {
  if (!globalPromptEngine) {
    globalPromptEngine = new PromptEngine();
  }
  return globalPromptEngine;
};

export const initializePromptEngine = async (config?: Partial<PromptEngineConfig>): Promise<PromptEngine> => {
  globalPromptEngine = new PromptEngine(config);
  await globalPromptEngine.initialize();
  return globalPromptEngine;
};
