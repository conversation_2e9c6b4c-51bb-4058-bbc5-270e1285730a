/**
 * Console transport implementation
 * Handles console output with optional colorization
 */

import {
  LogTransport,
  LogEntry,
  LogLevel,
  ConsoleTransportOptions,
  LogFormatter
} from './types';
import { DefaultFormatter } from './formatters';

export class ConsoleTransport implements LogTransport {
  private colorize: boolean;
  private formatter: LogFormatter;

  constructor(options: ConsoleTransportOptions = {}) {
    this.colorize = options.colorize ?? true;
    this.formatter = options.formatter || new DefaultFormatter();
  }

  async write(entry: LogEntry): Promise<void> {
    const formatted = this.formatter.format(entry);
    const output = this.colorize ? this.applyColors(formatted, entry.level) : formatted;

    // Use appropriate console method based on log level
    if (entry.level >= LogLevel.ERROR) {
      console.error(output);
    } else if (entry.level === LogLevel.WARN) {
      console.warn(output);
    } else {
      console.log(output);
    }
  }

  private applyColors(message: string, level: LogLevel): string {
    const colors = {
      [LogLevel.TRACE]: '\x1b[90m', // Gray
      [LogLevel.DEBUG]: '\x1b[36m', // Cyan
      [LogLevel.INFO]: '\x1b[32m',  // Green
      [LogLevel.WARN]: '\x1b[33m',  // Yellow
      [LogLevel.ERROR]: '\x1b[31m', // Red
      [LogLevel.FATAL]: '\x1b[35m'  // Magenta
    };

    const reset = '\x1b[0m';
    return `${colors[level]}${message}${reset}`;
  }
}
