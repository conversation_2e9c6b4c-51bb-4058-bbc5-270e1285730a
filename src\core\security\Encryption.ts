/**
 * Encryption service implementation
 * Provides AES-GCM encryption/decryption with secure key management
 */

import * as crypto from 'crypto';
import {
  EncryptionService,
  EncryptedData,
  EncryptionContext,
  EncryptionMetadata,
  SecurityError
} from './types';

export class AESGCMEncryptionService implements EncryptionService {
  private readonly algorithm = 'aes-256-cbc';
  private readonly keyLength = 32; // 256 bits
  private readonly ivLength = 16; // 128 bits
  private readonly tagLength = 0; // No auth tag for CBC

  async encrypt(
    data: string | Buffer,
    key: Buffer,
    context?: EncryptionContext
  ): Promise<EncryptedData> {
    try {
      if (key.length !== this.keyLength) {
        throw new Error(`Invalid key length. Expected ${this.keyLength} bytes, got ${key.length}`);
      }

      const iv = crypto.randomBytes(this.ivLength);
      const cipher = crypto.createCipheriv(this.algorithm, key, iv);

      const dataBuffer = typeof data === 'string' ? Buffer.from(data, 'utf8') : data;

      let encrypted = cipher.update(dataBuffer);
      encrypted = Buffer.concat([encrypted, cipher.final()]);

      const authTag = Buffer.alloc(0); // No auth tag for CBC mode

      const metadata: EncryptionMetadata = {
        timestamp: new Date(),
        keyId: this.generateKeyId(key),
        version: 1,
        context
      };

      return {
        algorithm: this.algorithm,
        iv: iv.toString('base64'),
        authTag: authTag.toString('base64'),
        data: encrypted.toString('base64'),
        metadata
      };
    } catch (error) {
      const securityError = new Error('Encryption failed') as SecurityError;
      securityError.code = 'ENCRYPTION_FAILED';
      securityError.details = error;
      securityError.severity = 'high';
      throw securityError;
    }
  }

  async decrypt(encryptedData: EncryptedData, key: Buffer): Promise<Buffer> {
    try {
      if (key.length !== this.keyLength) {
        throw new Error(`Invalid key length. Expected ${this.keyLength} bytes, got ${key.length}`);
      }

      if (encryptedData.algorithm !== this.algorithm) {
        throw new Error(`Unsupported algorithm: ${encryptedData.algorithm}`);
      }

      const iv = Buffer.from(encryptedData.iv, 'base64');
      const data = Buffer.from(encryptedData.data, 'base64');

      const decipher = crypto.createDecipheriv(encryptedData.algorithm, key, iv);

      let decrypted = decipher.update(data);
      decrypted = Buffer.concat([decrypted, decipher.final()]);

      return decrypted;
    } catch (error) {
      const securityError = new Error('Decryption failed') as SecurityError;
      securityError.code = 'DECRYPTION_FAILED';
      securityError.details = error;
      securityError.severity = 'high';
      throw securityError;
    }
  }

  async generateKey(password?: string, salt?: Buffer): Promise<Buffer> {
    try {
      if (password) {
        const actualSalt = salt || crypto.randomBytes(32);
        return crypto.pbkdf2Sync(password, actualSalt, 100000, this.keyLength, 'sha256');
      } else {
        return crypto.randomBytes(this.keyLength);
      }
    } catch (error) {
      const securityError = new Error('Key generation failed') as SecurityError;
      securityError.code = 'KEY_GENERATION_FAILED';
      securityError.details = error;
      securityError.severity = 'critical';
      throw securityError;
    }
  }

  /**
   * Generate a deterministic key ID from the key
   */
  private generateKeyId(key: Buffer): string {
    return crypto.createHash('sha256').update(key).digest('hex').slice(0, 16);
  }

  /**
   * Verify the integrity of encrypted data
   */
  async verifyIntegrity(encryptedData: EncryptedData): Promise<boolean> {
    try {
      // Check if all required fields are present
      if (!encryptedData.algorithm || !encryptedData.iv || !encryptedData.data) {
        return false;
      }

      // Verify base64 encoding
      try {
        Buffer.from(encryptedData.iv, 'base64');
        Buffer.from(encryptedData.data, 'base64');
        // authTag is not used in CBC mode
      } catch {
        return false;
      }

      // Check algorithm support
      if (encryptedData.algorithm !== this.algorithm) {
        return false;
      }

      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get encryption statistics
   */
  getEncryptionInfo(): {
    algorithm: string;
    keyLength: number;
    ivLength: number;
    tagLength: number;
  } {
    return {
      algorithm: this.algorithm,
      keyLength: this.keyLength,
      ivLength: this.ivLength,
      tagLength: this.tagLength
    };
  }
}

/**
 * Utility functions for encryption operations
 */
export class EncryptionUtils {
  /**
   * Generate a secure random salt
   */
  static generateSalt(length: number = 32): Buffer {
    return crypto.randomBytes(length);
  }

  /**
   * Generate a secure random password
   */
  static generateSecurePassword(length: number = 32): string {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    
    for (let i = 0; i < length; i++) {
      const randomIndex = crypto.randomInt(0, charset.length);
      password += charset[randomIndex];
    }
    
    return password;
  }

  /**
   * Hash data using SHA-256
   */
  static hash(data: string | Buffer, algorithm: string = 'sha256'): string {
    const hash = crypto.createHash(algorithm);
    hash.update(data);
    return hash.digest('hex');
  }

  /**
   * Generate HMAC for data integrity
   */
  static generateHMAC(data: string | Buffer, key: Buffer, algorithm: string = 'sha256'): string {
    const hmac = crypto.createHmac(algorithm, key);
    hmac.update(data);
    return hmac.digest('hex');
  }

  /**
   * Verify HMAC
   */
  static verifyHMAC(data: string | Buffer, key: Buffer, expectedHMAC: string, algorithm: string = 'sha256'): boolean {
    const computedHMAC = this.generateHMAC(data, key, algorithm);
    return crypto.timingSafeEqual(Buffer.from(computedHMAC), Buffer.from(expectedHMAC));
  }

  /**
   * Secure memory cleanup (best effort)
   */
  static secureCleanup(buffer: Buffer): void {
    if (buffer && buffer.length > 0) {
      buffer.fill(0);
    }
  }

  /**
   * Generate cryptographically secure random bytes
   */
  static randomBytes(size: number): Buffer {
    return crypto.randomBytes(size);
  }

  /**
   * Generate cryptographically secure random integer
   */
  static randomInt(min: number, max: number): number {
    return crypto.randomInt(min, max);
  }
}
