# Prompt Engine Module Specification

## 🎯 Purpose

The Prompt Engine is the core module responsible for enhancing, processing, and transforming user prompts using AI services, context injection, and template processing capabilities.

## 📋 Responsibilities

### Prompt Enhancement
- AI-powered prompt improvement and expansion
- Context-aware prompt modification
- Template-based prompt generation
- Multi-turn conversation management

### Context Integration
- Screen capture text integration
- Clipboard content incorporation
- File content extraction
- Dynamic variable substitution

### Template Processing
- Prompt template management
- Variable interpolation
- Conditional logic processing
- Output formatting

## 🏗️ Architecture

### Core Engine
```typescript
class PromptEngine {
  private aiService: AIService;
  private templateProcessor: TemplateProcessor;
  private contextExtractor: ContextExtractor;
  private promptLibrary: PromptLibrary;
  
  async enhance(prompt: string, options?: EnhancementOptions): Promise<string>;
  async processTemplate(template: PromptTemplate, variables: Record<string, any>): Promise<string>;
  async injectContext(prompt: string, contextSources: ContextSource[]): Promise<string>;
  async generateVariations(prompt: string, count: number): Promise<string[]>;
}
```

### Enhancement Options
```typescript
interface EnhancementOptions {
  style?: 'professional' | 'casual' | 'technical' | 'creative';
  length?: 'concise' | 'detailed' | 'comprehensive';
  tone?: 'formal' | 'friendly' | 'assertive' | 'questioning';
  context?: ContextSource[];
  temperature?: number;
  maxTokens?: number;
  model?: string;
}
```

### Prompt Template
```typescript
interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  template: string;
  variables: TemplateVariable[];
  category: string;
  tags: string[];
  metadata: TemplateMetadata;
}

interface TemplateVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  required: boolean;
  defaultValue?: any;
  description: string;
  validation?: ValidationRule[];
}
```

## 🤖 AI Integration

### AI Service Interface
```typescript
interface AIService {
  enhance(prompt: string, options: AIOptions): Promise<string>;
  complete(messages: ChatMessage[]): Promise<string>;
  generateVariations(prompt: string, count: number): Promise<string[]>;
  summarize(text: string): Promise<string>;
  translate(text: string, targetLanguage: string): Promise<string>;
}
```

### OpenAI-Compatible Implementation (Placeholder)
```typescript
class OpenAICompatibleService implements AIService {
  private apiKey: string;
  private baseURL: string;
  private defaultModel: string;
  
  constructor(config: AIServiceConfig) {
    this.apiKey = config.apiKey;
    this.baseURL = config.baseURL || 'https://api.openai.com/v1';
    this.defaultModel = config.defaultModel || 'gpt-3.5-turbo';
  }
  
  async enhance(prompt: string, options: AIOptions): Promise<string> {
    const messages: ChatMessage[] = [
      {
        role: 'system',
        content: this.buildSystemPrompt(options)
      },
      {
        role: 'user',
        content: prompt
      }
    ];
    
    const response = await this.makeRequest('/chat/completions', {
      model: options.model || this.defaultModel,
      messages,
      temperature: options.temperature || 0.7,
      max_tokens: options.maxTokens || 1000
    });
    
    return response.choices[0].message.content;
  }
  
  private async makeRequest(endpoint: string, data: any): Promise<any> {
    // Placeholder implementation - to be supplemented
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    
    if (!response.ok) {
      throw new Error(`AI API request failed: ${response.statusText}`);
    }
    
    return response.json();
  }
  
  private buildSystemPrompt(options: AIOptions): string {
    let systemPrompt = 'You are a helpful assistant that enhances and improves prompts.';
    
    if (options.style) {
      systemPrompt += ` Use a ${options.style} style.`;
    }
    
    if (options.tone) {
      systemPrompt += ` Maintain a ${options.tone} tone.`;
    }
    
    if (options.length) {
      systemPrompt += ` Make the response ${options.length}.`;
    }
    
    return systemPrompt;
  }
}
```

## 🔧 Template Processing

### Template Processor
```typescript
class TemplateProcessor {
  private handlebars: typeof Handlebars;
  
  constructor() {
    this.handlebars = Handlebars.create();
    this.registerHelpers();
  }
  
  process(template: string, variables: Record<string, any>): string {
    const compiled = this.handlebars.compile(template);
    return compiled(variables);
  }
  
  validate(template: string): TemplateValidationResult {
    try {
      this.handlebars.compile(template);
      return { valid: true };
    } catch (error) {
      return { 
        valid: false, 
        error: error.message 
      };
    }
  }
  
  private registerHelpers(): void {
    // Date formatting helper
    this.handlebars.registerHelper('formatDate', (date: Date, format: string) => {
      return moment(date).format(format);
    });
    
    // Text transformation helpers
    this.handlebars.registerHelper('uppercase', (text: string) => text.toUpperCase());
    this.handlebars.registerHelper('lowercase', (text: string) => text.toLowerCase());
    this.handlebars.registerHelper('capitalize', (text: string) => 
      text.charAt(0).toUpperCase() + text.slice(1)
    );
    
    // Conditional helpers
    this.handlebars.registerHelper('ifEquals', function(arg1, arg2, options) {
      return (arg1 == arg2) ? options.fn(this) : options.inverse(this);
    });
    
    // Array helpers
    this.handlebars.registerHelper('join', (array: any[], separator: string) => 
      array.join(separator)
    );
  }
}
```

### Template Examples
```typescript
const BUILT_IN_TEMPLATES: PromptTemplate[] = [
  {
    id: 'code-review',
    name: 'Code Review Request',
    description: 'Generate a code review request prompt',
    template: `Please review the following {{language}} code:

\`\`\`{{language}}
{{code}}
\`\`\`

Focus on:
{{#each focusAreas}}
- {{this}}
{{/each}}

{{#if context}}
Additional context: {{context}}
{{/if}}`,
    variables: [
      {
        name: 'language',
        type: 'string',
        required: true,
        description: 'Programming language'
      },
      {
        name: 'code',
        type: 'string',
        required: true,
        description: 'Code to review'
      },
      {
        name: 'focusAreas',
        type: 'array',
        required: false,
        defaultValue: ['Performance', 'Security', 'Best Practices'],
        description: 'Areas to focus on during review'
      }
    ],
    category: 'Development',
    tags: ['code', 'review', 'development'],
    metadata: {
      author: 'system',
      version: '1.0.0',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  },
  
  {
    id: 'text-summarization',
    name: 'Text Summarization',
    description: 'Summarize long text content',
    template: `Please summarize the following text in {{#if length}}{{length}}{{else}}3-5{{/if}} sentences:

{{text}}

{{#if style}}
Style: {{style}}
{{/if}}

{{#if audience}}
Target audience: {{audience}}
{{/if}}`,
    variables: [
      {
        name: 'text',
        type: 'string',
        required: true,
        description: 'Text to summarize'
      },
      {
        name: 'length',
        type: 'string',
        required: false,
        description: 'Desired summary length'
      },
      {
        name: 'style',
        type: 'string',
        required: false,
        description: 'Summary style (bullet points, paragraph, etc.)'
      }
    ],
    category: 'Text Processing',
    tags: ['summary', 'text', 'analysis'],
    metadata: {
      author: 'system',
      version: '1.0.0',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  }
];
```

## 🔄 Context Integration

### Context Sources
```typescript
enum ContextSource {
  CLIPBOARD = 'clipboard',
  SCREEN_CAPTURE = 'screen-capture',
  ACTIVE_WINDOW = 'active-window',
  FILE_CONTENT = 'file-content',
  SELECTED_TEXT = 'selected-text',
  CONVERSATION_HISTORY = 'conversation-history'
}

interface ContextData {
  source: ContextSource;
  content: string;
  metadata: {
    timestamp: Date;
    source_info?: any;
  };
}
```

### Context Injection
```typescript
class ContextInjector {
  async injectContext(prompt: string, contextSources: ContextSource[]): Promise<string> {
    const contextData: ContextData[] = [];
    
    for (const source of contextSources) {
      try {
        const data = await this.extractContext(source);
        if (data) {
          contextData.push(data);
        }
      } catch (error) {
        console.warn(`Failed to extract context from ${source}:`, error);
      }
    }
    
    if (contextData.length === 0) {
      return prompt;
    }
    
    return this.buildContextualPrompt(prompt, contextData);
  }
  
  private async extractContext(source: ContextSource): Promise<ContextData | null> {
    switch (source) {
      case ContextSource.CLIPBOARD:
        const clipboardText = await this.contextExtractor.getClipboardText();
        return {
          source,
          content: clipboardText,
          metadata: { timestamp: new Date() }
        };
        
      case ContextSource.SCREEN_CAPTURE:
        const screenText = await this.contextExtractor.captureScreenText();
        return {
          source,
          content: screenText,
          metadata: { timestamp: new Date() }
        };
        
      default:
        return null;
    }
  }
  
  private buildContextualPrompt(prompt: string, contextData: ContextData[]): string {
    let contextualPrompt = prompt;
    
    if (contextData.length > 0) {
      contextualPrompt += '\n\n--- Context ---\n';
      
      contextData.forEach((data, index) => {
        contextualPrompt += `\n${index + 1}. From ${data.source}:\n${data.content}\n`;
      });
    }
    
    return contextualPrompt;
  }
}
```

## 📊 Performance Optimization

### Caching Strategy
```typescript
class PromptCache {
  private cache: Map<string, CacheEntry> = new Map();
  private maxSize = 1000;
  private ttl = 3600000; // 1 hour
  
  get(key: string): string | null {
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    if (Date.now() - entry.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.value;
  }
  
  set(key: string, value: string): void {
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      value,
      timestamp: Date.now()
    });
  }
  
  private generateKey(prompt: string, options: EnhancementOptions): string {
    return crypto
      .createHash('sha256')
      .update(JSON.stringify({ prompt, options }))
      .digest('hex');
  }
}
```

## 🧪 Testing Strategy

### Unit Tests
```typescript
describe('PromptEngine', () => {
  let engine: PromptEngine;
  let mockAIService: jest.Mocked<AIService>;
  
  beforeEach(() => {
    mockAIService = {
      enhance: jest.fn(),
      complete: jest.fn(),
      generateVariations: jest.fn(),
      summarize: jest.fn(),
      translate: jest.fn()
    };
    
    engine = new PromptEngine(mockAIService);
  });
  
  test('should enhance prompt with AI service', async () => {
    const prompt = 'Write a function';
    const enhanced = 'Write a well-documented function with error handling';
    
    mockAIService.enhance.mockResolvedValue(enhanced);
    
    const result = await engine.enhance(prompt);
    
    expect(result).toBe(enhanced);
    expect(mockAIService.enhance).toHaveBeenCalledWith(prompt, expect.any(Object));
  });
  
  test('should process template with variables', async () => {
    const template = 'Hello {{name}}, welcome to {{app}}!';
    const variables = { name: 'John', app: 'PromptPilot' };
    
    const result = await engine.processTemplate({ template } as PromptTemplate, variables);
    
    expect(result).toBe('Hello John, welcome to PromptPilot!');
  });
});
```
