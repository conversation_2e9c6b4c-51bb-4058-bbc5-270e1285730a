/**
 * Shared TypeScript type definitions for PromptPilot Desktop
 * This file contains common types used across the application
 */

// Basic utility types
export type Nullable<T> = T | null;
export type Optional<T> = T | undefined;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// Application configuration types
export interface AppConfig {
  version: string;
  environment: 'development' | 'production' | 'test';
  platform: NodeJS.Platform;
}

// IPC message types
export interface IPCMessage<T = any> {
  id: string;
  channel: string;
  data: T;
  timestamp: number;
}

export interface IPCResponse<T = any> {
  id: string;
  success: boolean;
  data?: T;
  error?: string;
  timestamp: number;
}

// Window management types
export interface WindowConfig {
  width: number;
  height: number;
  minWidth?: number;
  minHeight?: number;
  resizable: boolean;
  frame: boolean;
  transparent: boolean;
  alwaysOnTop: boolean;
  skipTaskbar: boolean;
}

// Error types
export interface AppError {
  id: string;
  type: string;
  message: string;
  stack?: string;
  timestamp: Date;
  context?: Record<string, any>;
}

// Event types
export interface AppEvent<T = any> {
  type: string;
  payload: T;
  timestamp: Date;
  source: string;
}

// Base entity interface
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

// Validation result - moved to validation module

// Service status
export interface ServiceStatus {
  name: string;
  status: 'running' | 'stopped' | 'error';
  lastCheck: Date;
  error?: string;
}

// Application state
export interface AppState {
  initialized: boolean;
  services: ServiceStatus[];
  config: AppConfig;
  errors: AppError[];
}

// Core models
export * from './models/index';

// Validation (ValidationRule is imported in models, not exported)
// Export validation types explicitly to avoid conflicts
export type {
  ValidationType,
  ValidationSeverity,
  SchemaType,
  ValidationFunction,
  ValidationContext,
  ValidationRule,
  ValidationError,
  ValidationWarning,
  ValidationResult,
  Schema,
  Validator
} from './validation';

// Re-export types that will be defined in other modules
export * from './api/index';
export * from './events/index';
