/**
 * Main Hotkey Manager implementation
 * Global hotkey registration and management capabilities
 */

import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import {
  IHotkeyManager,
  HotkeyManagerConfig,
  HotkeyDefinition,
  HotkeyRegistration,
  ActionHandler,
  ActionContext,
  ConflictResult,
  HotkeyManagerError,
  HotkeyRegistrationError,
  HotkeyConflictError,
  ActionExecutionError,
  HotkeyEvent,
  HotkeyEventData,
  HotkeyAnalytics
} from './types';
import { PlatformAdapterFactory } from './PlatformAdapterFactory';
import { ConflictResolver } from './ConflictResolver';
import { HotkeyAnalyticsManager } from './HotkeyAnalyticsManager';
import { AcceleratorParser } from './AcceleratorParser';
import { LoggerFactory } from '../../core/logger';
import { toError } from '../../core/utils/errorUtils';

const logger = LoggerFactory.getInstance().getLogger('HotkeyManager');

/**
 * Main Hotkey Manager implementation
 */
export class HotkeyManager extends EventEmitter implements IHotkeyManager {
  private config: HotkeyManagerConfig;
  private registeredHotkeys: Map<string, HotkeyRegistration> = new Map();
  private actionHandlers: Map<string, ActionHandler> = new Map();
  private platformAdapter: any;
  private conflictResolver!: ConflictResolver;
  private analyticsManager!: HotkeyAnalyticsManager;
  private acceleratorParser!: AcceleratorParser;
  private isInitialized: boolean = false;

  constructor(config?: Partial<HotkeyManagerConfig>) {
    super();
    
    this.config = {
      platform: process.platform as any,
      enableGlobalHotkeys: true,
      enableConflictDetection: true,
      enableAnalytics: true,
      maxHotkeys: 50,
      defaultCategory: 'General',
      autoResolveConflicts: false,
      hotkeyTimeout: 5000,
      ...config
    };

    this.initializeComponents();
  }

  /**
   * Initialize all components
   */
  private initializeComponents(): void {
    this.platformAdapter = PlatformAdapterFactory.create(this.config.platform);
    this.conflictResolver = new ConflictResolver(this.config.platform);
    this.analyticsManager = new HotkeyAnalyticsManager(this.config.enableAnalytics);
    this.acceleratorParser = new AcceleratorParser();
  }

  /**
   * Initialize the hotkey manager
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Register built-in actions
      await this.registerBuiltInActions();

      // Load default hotkeys
      await this.loadDefaultHotkeys();

      this.isInitialized = true;
      logger.info('Hotkey manager initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize hotkey manager', { error });
      throw new HotkeyManagerError('Failed to initialize hotkey manager', toError(error));
    }
  }

  /**
   * Register a hotkey
   */
  async registerHotkey(hotkey: HotkeyDefinition): Promise<boolean> {
    try {
      // Validate hotkey definition
      this.validateHotkeyDefinition(hotkey);

      // Check if we've reached the maximum number of hotkeys
      if (this.registeredHotkeys.size >= this.config.maxHotkeys) {
        throw new HotkeyRegistrationError(`Maximum number of hotkeys (${this.config.maxHotkeys}) reached`);
      }

      // Normalize accelerator
      const normalizedAccelerator = this.acceleratorParser.normalize(hotkey.accelerator);
      
      // Check for conflicts if enabled
      if (this.config.enableConflictDetection) {
        const conflictResult = await this.checkConflicts(normalizedAccelerator);
        if (conflictResult.hasConflicts && !this.config.autoResolveConflicts) {
          throw new HotkeyConflictError(
            `Hotkey conflict detected for ${normalizedAccelerator}`,
            conflictResult.conflicts
          );
        }
      }

      // Check if action handler exists
      if (!this.actionHandlers.has(hotkey.actionId)) {
        throw new HotkeyRegistrationError(`Action handler not found: ${hotkey.actionId}`);
      }

      // Unregister existing hotkey if it exists
      if (this.registeredHotkeys.has(normalizedAccelerator)) {
        await this.unregisterHotkey(normalizedAccelerator);
      }

      // Create registration
      const registration: HotkeyRegistration = {
        id: uuidv4(),
        definition: {
          ...hotkey,
          accelerator: normalizedAccelerator
        },
        registeredAt: new Date(),
        triggerCount: 0,
        isActive: hotkey.enabled
      };

      // Register with platform adapter if global and enabled
      if (hotkey.global && hotkey.enabled && this.config.enableGlobalHotkeys) {
        const callback = () => this.handleHotkeyTrigger(normalizedAccelerator);
        const success = await this.platformAdapter.registerGlobalHotkey(normalizedAccelerator, callback);
        
        if (!success) {
          throw new HotkeyRegistrationError(`Failed to register global hotkey: ${normalizedAccelerator}`);
        }
      }

      // Store registration
      this.registeredHotkeys.set(normalizedAccelerator, registration);

      // Emit event
      this.emit('hotkey-registered', {
        accelerator: normalizedAccelerator,
        actionId: hotkey.actionId,
        timestamp: new Date()
      } as HotkeyEventData);

      logger.info('Hotkey registered successfully', { 
        accelerator: normalizedAccelerator,
        actionId: hotkey.actionId,
        global: hotkey.global
      });

      return true;
    } catch (error) {
      logger.error('Failed to register hotkey', { error, hotkey });
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Unregister a hotkey
   */
  async unregisterHotkey(accelerator: string): Promise<boolean> {
    try {
      const normalizedAccelerator = this.acceleratorParser.normalize(accelerator);
      const registration = this.registeredHotkeys.get(normalizedAccelerator);

      if (!registration) {
        return false;
      }

      // Unregister from platform adapter if global
      if (registration.definition.global && this.config.enableGlobalHotkeys) {
        await this.platformAdapter.unregisterGlobalHotkey(normalizedAccelerator);
      }

      // Remove registration
      this.registeredHotkeys.delete(normalizedAccelerator);

      // Emit event
      this.emit('hotkey-unregistered', {
        accelerator: normalizedAccelerator,
        actionId: registration.definition.actionId,
        timestamp: new Date()
      } as HotkeyEventData);

      logger.info('Hotkey unregistered successfully', { 
        accelerator: normalizedAccelerator,
        actionId: registration.definition.actionId
      });

      return true;
    } catch (error) {
      logger.error('Failed to unregister hotkey', { error, accelerator });
      this.emit('error', error);
      throw new HotkeyManagerError('Failed to unregister hotkey', toError(error));
    }
  }

  /**
   * Update a hotkey
   */
  async updateHotkey(oldAccelerator: string, newAccelerator: string): Promise<boolean> {
    try {
      const registration = this.registeredHotkeys.get(this.acceleratorParser.normalize(oldAccelerator));
      
      if (!registration) {
        throw new HotkeyManagerError(`Hotkey not found: ${oldAccelerator}`);
      }

      // Create new hotkey definition with updated accelerator
      const newDefinition: HotkeyDefinition = {
        ...registration.definition,
        accelerator: newAccelerator
      };

      // Unregister old hotkey
      await this.unregisterHotkey(oldAccelerator);

      // Register new hotkey
      return await this.registerHotkey(newDefinition);
    } catch (error) {
      logger.error('Failed to update hotkey', { error, oldAccelerator, newAccelerator });
      throw new HotkeyManagerError('Failed to update hotkey', toError(error));
    }
  }

  /**
   * Register an action handler
   */
  async registerAction(actionId: string, handler: ActionHandler): Promise<void> {
    try {
      this.actionHandlers.set(actionId, handler);
      
      logger.info('Action handler registered', { 
        actionId, 
        description: handler.description 
      });
    } catch (error) {
      logger.error('Failed to register action handler', { error, actionId });
      throw new HotkeyManagerError('Failed to register action handler', toError(error));
    }
  }

  /**
   * Trigger an action
   */
  async triggerAction(actionId: string, context?: ActionContext): Promise<void> {
    const startTime = Date.now();
    
    try {
      const handler = this.actionHandlers.get(actionId);
      
      if (!handler) {
        throw new ActionExecutionError(`Action handler not found: ${actionId}`);
      }

      // Check if action can be executed
      if (handler.canExecute && !handler.canExecute(context)) {
        logger.warn('Action execution blocked by canExecute check', { actionId, context });
        return;
      }

      // Create context if not provided
      const actionContext: ActionContext = {
        timestamp: new Date(),
        ...context
      };

      // Execute action
      await handler.execute(actionContext);

      // Record analytics
      if (this.config.enableAnalytics) {
        const responseTime = Date.now() - startTime;
        this.analyticsManager.recordActionExecution(actionId, responseTime, true);
      }

      // Emit event
      this.emit('action-executed', {
        actionId,
        context: actionContext,
        timestamp: new Date()
      } as HotkeyEventData);

      logger.debug('Action executed successfully', { 
        actionId, 
        responseTime: Date.now() - startTime 
      });
    } catch (error) {
      // Record analytics for failed execution
      if (this.config.enableAnalytics) {
        const responseTime = Date.now() - startTime;
        this.analyticsManager.recordActionExecution(actionId, responseTime, false);
      }

      logger.error('Failed to execute action', { error, actionId, context });
      this.emit('error', new ActionExecutionError('Failed to execute action', error));
      throw error;
    }
  }

  /**
   * Get all registered hotkeys
   */
  getRegisteredHotkeys(): HotkeyRegistration[] {
    return Array.from(this.registeredHotkeys.values());
  }

  /**
   * Check if hotkey is available
   */
  isHotkeyAvailable(accelerator: string): boolean {
    const normalizedAccelerator = this.acceleratorParser.normalize(accelerator);
    return !this.registeredHotkeys.has(normalizedAccelerator);
  }

  /**
   * Check for hotkey conflicts
   */
  async checkConflicts(accelerator: string): Promise<ConflictResult> {
    try {
      return await this.conflictResolver.checkConflicts(accelerator);
    } catch (error) {
      logger.error('Failed to check conflicts', { error, accelerator });
      throw new HotkeyManagerError('Failed to check conflicts', toError(error));
    }
  }

  /**
   * Get analytics data
   */
  getAnalytics(): HotkeyAnalytics {
    return this.analyticsManager.getAnalytics();
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<HotkeyManagerConfig>): void {
    this.config = { ...this.config, ...config };
    
    // Update analytics manager
    this.analyticsManager.setEnabled(this.config.enableAnalytics);
    
    logger.info('Hotkey manager configuration updated');
  }

  /**
   * Get current configuration
   */
  getConfig(): HotkeyManagerConfig {
    return { ...this.config };
  }

  /**
   * Handle hotkey trigger
   */
  private async handleHotkeyTrigger(accelerator: string): Promise<void> {
    try {
      const registration = this.registeredHotkeys.get(accelerator);
      
      if (!registration || !registration.isActive) {
        return;
      }

      // Update trigger statistics
      registration.triggerCount++;
      registration.lastTriggered = new Date();

      // Record analytics
      if (this.config.enableAnalytics) {
        this.analyticsManager.recordHotkeyUsage(accelerator, registration.definition.actionId);
      }

      // Create action context
      const context: ActionContext = {
        timestamp: new Date(),
        hotkeyAccelerator: accelerator
      };

      // Emit hotkey triggered event
      this.emit('hotkey-triggered', {
        accelerator,
        actionId: registration.definition.actionId,
        context,
        timestamp: new Date()
      } as HotkeyEventData);

      // Execute associated action
      await this.triggerAction(registration.definition.actionId, context);

      logger.debug('Hotkey triggered successfully', { 
        accelerator,
        actionId: registration.definition.actionId,
        triggerCount: registration.triggerCount
      });
    } catch (error) {
      logger.error('Failed to handle hotkey trigger', { error, accelerator });
      this.emit('error', error);
    }
  }

  /**
   * Validate hotkey definition
   */
  private validateHotkeyDefinition(hotkey: HotkeyDefinition): void {
    if (!hotkey.accelerator || hotkey.accelerator.trim().length === 0) {
      throw new HotkeyRegistrationError('Accelerator is required');
    }

    if (!hotkey.actionId || hotkey.actionId.trim().length === 0) {
      throw new HotkeyRegistrationError('Action ID is required');
    }

    if (!hotkey.description || hotkey.description.trim().length === 0) {
      throw new HotkeyRegistrationError('Description is required');
    }

    // Validate accelerator format
    const parsed = this.acceleratorParser.parse(hotkey.accelerator);
    if (!parsed.isValid) {
      throw new HotkeyRegistrationError(`Invalid accelerator format: ${hotkey.accelerator}`);
    }
  }

  /**
   * Register built-in actions
   */
  private async registerBuiltInActions(): Promise<void> {
    const builtInActions = {
      'show-floating-window': {
        execute: async () => {
          // Placeholder - would show floating window
          logger.info('Show floating window action triggered');
        },
        description: 'Show floating prompt window'
      },
      'capture-screen': {
        execute: async () => {
          // Placeholder - would capture screen
          logger.info('Capture screen action triggered');
        },
        description: 'Capture screen and extract text'
      },
      'start-voice-input': {
        execute: async () => {
          // Placeholder - would start voice input
          logger.info('Start voice input action triggered');
        },
        description: 'Start voice recording'
      },
      'show-help': {
        execute: async () => {
          // Placeholder - would show help
          logger.info('Show help action triggered');
        },
        description: 'Show help and shortcuts'
      },
      'quit-application': {
        execute: async () => {
          // Placeholder - would quit application
          logger.info('Quit application action triggered');
        },
        description: 'Quit application'
      }
    };

    for (const [actionId, handler] of Object.entries(builtInActions)) {
      await this.registerAction(actionId, handler);
    }

    logger.info('Built-in actions registered', { count: Object.keys(builtInActions).length });
  }

  /**
   * Load default hotkeys
   */
  private async loadDefaultHotkeys(): Promise<void> {
    const defaultHotkeys: HotkeyDefinition[] = [
      {
        accelerator: 'Ctrl+Shift+P',
        actionId: 'show-floating-window',
        description: 'Show floating prompt window',
        category: 'Window Management',
        enabled: true,
        global: true
      },
      {
        accelerator: 'Ctrl+Shift+O',
        actionId: 'capture-screen',
        description: 'Capture screen and extract text',
        category: 'Context Extraction',
        enabled: true,
        global: true
      },
      {
        accelerator: 'Ctrl+Shift+V',
        actionId: 'start-voice-input',
        description: 'Start voice recording',
        category: 'Voice Input',
        enabled: true,
        global: true
      },
      {
        accelerator: 'Ctrl+Shift+H',
        actionId: 'show-help',
        description: 'Show help and shortcuts',
        category: 'Help',
        enabled: true,
        global: true
      }
    ];

    for (const hotkey of defaultHotkeys) {
      try {
        await this.registerHotkey(hotkey);
      } catch (error) {
        logger.warn('Failed to register default hotkey', { error, hotkey });
      }
    }

    logger.info('Default hotkeys loaded', { count: defaultHotkeys.length });
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    try {
      // Unregister all hotkeys
      const accelerators = Array.from(this.registeredHotkeys.keys());
      for (const accelerator of accelerators) {
        await this.unregisterHotkey(accelerator);
      }

      // Clear handlers
      this.actionHandlers.clear();

      // Remove all listeners
      this.removeAllListeners();

      logger.info('Hotkey manager cleaned up');
    } catch (error) {
      logger.error('Error during hotkey manager cleanup', { error });
    }
  }
}

// Global instance management
let globalHotkeyManager: HotkeyManager | null = null;

export const getHotkeyManager = (): HotkeyManager => {
  if (!globalHotkeyManager) {
    globalHotkeyManager = new HotkeyManager();
  }
  return globalHotkeyManager;
};

export const initializeHotkeyManager = async (config?: Partial<HotkeyManagerConfig>): Promise<HotkeyManager> => {
  globalHotkeyManager = new HotkeyManager(config);
  await globalHotkeyManager.initialize();
  return globalHotkeyManager;
};
