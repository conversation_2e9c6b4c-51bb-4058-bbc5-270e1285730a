# Module Dependency Map

## 🗺️ Overview

This document outlines the dependency relationships between all modules in the PromptPilot Desktop application, ensuring clear separation of concerns and maintainable architecture.

## 📊 Dependency Hierarchy

### Level 0: Foundation Layer
```
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   Logger        │  │  Configuration  │  │   Security      │
└─────────────────┘  └─────────────────┘  └─────────────────┘
```

### Level 1: Core Utilities
```
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│  File System    │  │  Error Handler  │  │  Data Models    │
└─────────────────┘  └─────────────────┘  └─────────────────┘
```

### Level 2: Storage & Communication
```
┌─────────────────┐  ┌─────────────────┐
│  Storage Module │  │ IPC Communication│
└─────────────────┘  └─────────────────┘
```

### Level 3: Service Layer
```
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│ Context Extractor│  │  Voice Module   │  │ Prompt Library  │
└─────────────────┘  └─────────────────┘  └─────────────────┘
┌─────────────────┐  ┌─────────────────┐
│ AI API Bridge   │  │  STT Bridge     │
└─────────────────┘  └─────────────────┘
```

### Level 4: Business Logic
```
┌─────────────────┐  ┌─────────────────┐
│  Prompt Engine  │  │ Hotkey Manager  │
└─────────────────┘  └─────────────────┘
```

### Level 5: Application Layer
```
┌─────────────────┐  ┌─────────────────┐
│  Main Process   │  │Renderer Process │
└─────────────────┘  └─────────────────┘
```

## 🔗 Detailed Dependencies

### Main Process Dependencies
```typescript
MainProcess {
  depends_on: [
    "IPC Communication",
    "Hotkey Manager", 
    "Window Manager",
    "Configuration",
    "Logger"
  ]
}
```

### Renderer Process Dependencies
```typescript
RendererProcess {
  depends_on: [
    "IPC Communication",
    "Data Models",
    "Configuration",
    "Logger"
  ]
}
```

### Prompt Engine Dependencies
```typescript
PromptEngine {
  depends_on: [
    "AI API Bridge",
    "Prompt Library",
    "Context Extractor",
    "Storage Module",
    "Configuration",
    "Logger"
  ]
}
```

### Context Extractor Dependencies
```typescript
ContextExtractor {
  depends_on: [
    "File System",
    "OCR Engine",
    "Configuration",
    "Logger",
    "Error Handler"
  ]
}
```

### Voice Module Dependencies
```typescript
VoiceModule {
  depends_on: [
    "STT Bridge",
    "File System",
    "Configuration",
    "Logger",
    "Error Handler"
  ]
}
```

### Prompt Library Dependencies
```typescript
PromptLibrary {
  depends_on: [
    "Storage Module",
    "File System",
    "Data Models",
    "Configuration",
    "Logger"
  ]
}
```

### Storage Module Dependencies
```typescript
StorageModule {
  depends_on: [
    "File System",
    "Security",
    "Configuration",
    "Logger",
    "Data Models"
  ]
}
```

### AI API Bridge Dependencies
```typescript
AIAPIBridge {
  depends_on: [
    "Configuration",
    "Security",
    "Logger",
    "Error Handler",
    "Data Models"
  ]
}
```

### STT Bridge Dependencies
```typescript
STTBridge {
  depends_on: [
    "Configuration",
    "Security", 
    "Logger",
    "Error Handler",
    "File System"
  ]
}
```

### Hotkey Manager Dependencies
```typescript
HotkeyManager {
  depends_on: [
    "IPC Communication",
    "Configuration",
    "Logger",
    "Error Handler"
  ]
}
```

## 🚫 Forbidden Dependencies

### Circular Dependencies Prevention
- **Renderer Process** ❌ cannot depend on **Main Process**
- **Service Layer** ❌ cannot depend on **Application Layer**
- **Storage Module** ❌ cannot depend on **Business Logic Layer**

### Cross-Layer Restrictions
- **Level N** modules can only depend on **Level N-1** or lower
- No direct dependencies between modules at the same level (use mediator pattern)

## 🔄 Communication Patterns

### Event-Driven Communication
```typescript
// Publisher-Subscriber pattern for loose coupling
EventBus {
  publishers: ["Prompt Engine", "Voice Module", "Context Extractor"]
  subscribers: ["UI Components", "Logger", "Storage Module"]
}
```

### Request-Response Pattern
```typescript
// For synchronous operations
RequestResponse {
  requesters: ["Renderer Process", "Prompt Engine"]
  responders: ["Storage Module", "AI API Bridge", "STT Bridge"]
}
```

### Command Pattern
```typescript
// For user actions and system commands
CommandPattern {
  invokers: ["Hotkey Manager", "UI Components"]
  commands: ["Prompt Enhancement", "Voice Recording", "Screen Capture"]
  receivers: ["Prompt Engine", "Voice Module", "Context Extractor"]
}
```

## 🔧 Dependency Injection

### Service Container
```typescript
interface ServiceContainer {
  register<T>(token: string, factory: () => T): void;
  resolve<T>(token: string): T;
  singleton<T>(token: string, factory: () => T): void;
}
```

### Module Registration
```typescript
// Example service registration
container.singleton('logger', () => new Logger(config.logging));
container.singleton('storage', () => new StorageModule(config.database));
container.register('promptEngine', () => new PromptEngine(
  container.resolve('storage'),
  container.resolve('aiApiBridge'),
  container.resolve('logger')
));
```

## 📋 Dependency Validation

### Build-Time Checks
- Circular dependency detection
- Missing dependency validation
- Version compatibility checks

### Runtime Checks
- Service availability verification
- Dependency health monitoring
- Graceful degradation handling

## 🔄 Module Lifecycle

### Initialization Order
1. **Foundation Layer**: Logger, Configuration, Security
2. **Core Utilities**: File System, Error Handler, Data Models
3. **Storage & Communication**: Storage Module, IPC Communication
4. **Service Layer**: All service modules in parallel
5. **Business Logic**: Prompt Engine, Hotkey Manager
6. **Application Layer**: Main Process, Renderer Process

### Shutdown Order
- Reverse of initialization order
- Graceful service termination
- Resource cleanup and persistence
