/**
 * Clipboard Monitor
 * Monitors clipboard changes and maintains history
 */

import { IClipboardMonitor, ClipboardEntry } from './types';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('ClipboardMonitor');

export interface ClipboardConfig {
  monitoringEnabled: boolean;
  monitoringInterval: number;
  historySize: number;
  enableHistory: boolean;
}

/**
 * Clipboard Monitor implementation
 */
export class ClipboardMonitor implements IClipboardMonitor {
  private config: ClipboardConfig;
  private isMonitoring: boolean = false;
  private monitoringInterval?: NodeJS.Timeout;
  private currentContent: string = '';
  private contentHistory: ClipboardEntry[] = [];
  private changeListeners: Array<(content: string) => void> = [];

  constructor(config: ClipboardConfig) {
    this.config = config;
  }

  /**
   * Start monitoring clipboard changes
   */
  startMonitoring(intervalMs: number = 1000): void {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.checkClipboardChange();
      } catch (error) {
        logger.error('Error checking clipboard', { error });
      }
    }, intervalMs);

    logger.info('Clipboard monitoring started', { intervalMs });
  }

  /**
   * Stop monitoring clipboard changes
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }

    logger.info('Clipboard monitoring stopped');
  }

  /**
   * Get current clipboard content
   */
  getCurrentContent(): string {
    try {
      // In a real implementation, this would use platform-specific APIs:
      // - Windows: GetClipboardData
      // - macOS: NSPasteboard
      // - Linux: X11 clipboard or Wayland
      // - Or use libraries like clipboardy, electron clipboard, etc.

      // For now, return the cached content
      return this.currentContent;
    } catch (error) {
      logger.error('Failed to get clipboard content', { error });
      return '';
    }
  }

  /**
   * Get clipboard content history
   */
  getContentHistory(): ClipboardEntry[] {
    return [...this.contentHistory];
  }

  /**
   * Register content change listener
   */
  onContentChange(listener: (content: string) => void): () => void {
    this.changeListeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.changeListeners.indexOf(listener);
      if (index > -1) {
        this.changeListeners.splice(index, 1);
      }
    };
  }

  /**
   * Clear content history
   */
  clearHistory(): void {
    this.contentHistory = [];
    logger.info('Clipboard history cleared');
  }

  /**
   * Check for clipboard changes
   */
  private async checkClipboardChange(): Promise<void> {
    try {
      const newContent = await this.readClipboardContent();
      
      if (newContent !== this.currentContent) {
        const oldContent = this.currentContent;
        this.currentContent = newContent;

        // Add to history if enabled
        if (this.config.enableHistory && newContent.trim().length > 0) {
          this.addToHistory(newContent);
        }

        // Notify listeners
        this.notifyListeners(newContent);

        logger.debug('Clipboard content changed', { 
          oldLength: oldContent.length,
          newLength: newContent.length
        });
      }
    } catch (error) {
      logger.error('Failed to check clipboard change', { error });
    }
  }

  /**
   * Read clipboard content (platform-specific implementation)
   */
  private async readClipboardContent(): Promise<string> {
    try {
      // In a real implementation, this would use platform-specific APIs
      // For now, simulate clipboard content changes
      
      const simulatedClipboard = [
        'Sample clipboard text content',
        'Another clipboard entry',
        'Text copied from application',
        'Code snippet from editor',
        'URL from browser',
        'Document excerpt',
        this.currentContent // Sometimes no change
      ];

      // Randomly return content to simulate changes
      if (Math.random() < 0.1) { // 10% chance of change
        return simulatedClipboard[Math.floor(Math.random() * (simulatedClipboard.length - 1))];
      }

      return this.currentContent;
    } catch (error) {
      logger.error('Failed to read clipboard content', { error });
      return this.currentContent;
    }
  }

  /**
   * Add content to history
   */
  private addToHistory(content: string): void {
    const entry: ClipboardEntry = {
      content,
      timestamp: new Date(),
      type: this.detectContentType(content),
      source: 'clipboard'
    };

    this.contentHistory.unshift(entry);

    // Limit history size
    if (this.contentHistory.length > this.config.historySize) {
      this.contentHistory = this.contentHistory.slice(0, this.config.historySize);
    }
  }

  /**
   * Detect content type
   */
  private detectContentType(content: string): 'text' | 'image' | 'file' | 'html' {
    // Simple content type detection
    if (content.includes('<html') || content.includes('<!DOCTYPE')) {
      return 'html';
    }
    
    if (content.startsWith('file://') || content.includes('\\') || content.includes('/')) {
      return 'file';
    }

    // Default to text
    return 'text';
  }

  /**
   * Notify change listeners
   */
  private notifyListeners(content: string): void {
    for (const listener of this.changeListeners) {
      try {
        listener(content);
      } catch (error) {
        logger.error('Error in clipboard change listener', { error });
      }
    }
  }

  /**
   * Get clipboard statistics
   */
  getStats() {
    return {
      isMonitoring: this.isMonitoring,
      historySize: this.contentHistory.length,
      currentContentLength: this.currentContent.length,
      listenerCount: this.changeListeners.length,
      lastChange: this.contentHistory.length > 0 ? this.contentHistory[0].timestamp : null
    };
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<ClipboardConfig>): void {
    this.config = { ...this.config, ...config };
    
    // Restart monitoring if interval changed
    if (config.monitoringInterval && this.isMonitoring) {
      this.stopMonitoring();
      this.startMonitoring(config.monitoringInterval);
    }

    // Trim history if size reduced
    if (config.historySize && this.contentHistory.length > config.historySize) {
      this.contentHistory = this.contentHistory.slice(0, config.historySize);
    }

    logger.info('Clipboard monitor configuration updated');
  }
}
