/**
 * Security module type definitions
 * Provides comprehensive security features including encryption, secure storage, and privacy protection
 */

export interface SecurityConfig {
  encryption: {
    algorithm: 'AES-256-GCM' | 'ChaCha20-Poly1305';
    keyDerivation: 'PBKDF2' | 'Argon2' | 'scrypt';
    iterations: number;
    saltLength: number;
  };
  storage: {
    encryptSensitiveData: boolean;
    secureDelete: boolean;
    memoryProtection: boolean;
  };
  authentication: {
    enabled: boolean;
    method: 'password' | 'biometric' | 'token';
    sessionTimeout: number;
    maxAttempts: number;
  };
  privacy: {
    dataMinimization: boolean;
    anonymizeAnalytics: boolean;
    autoCleanup: boolean;
    retentionPeriod: number;
  };
}

export interface EncryptionContext {
  purpose: 'storage' | 'transmission' | 'backup';
  sensitivity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  metadata?: Record<string, any>;
}

export interface EncryptedData {
  algorithm: string;
  iv: string;
  authTag: string;
  data: string;
  metadata: EncryptionMetadata;
}

export interface EncryptionMetadata {
  timestamp: Date;
  keyId: string;
  version: number;
  context?: EncryptionContext;
}

export interface SecurityContext {
  userId?: string;
  sessionId?: string;
  operation: string;
  resource?: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface KeyInfo {
  id: string;
  purpose: string;
  algorithm: string;
  createdAt: Date;
  lastUsed: Date;
  usageCount: number;
  deprecated?: boolean;
  replacedBy?: string;
  context?: KeyContext;
}

export interface KeyContext {
  userId?: string;
  application?: string;
  environment?: string;
  [key: string]: any;
}

export interface SecurityError extends Error {
  code: string;
  details?: any;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface EncryptionService {
  encrypt(data: string | Buffer, key: Buffer, context?: EncryptionContext): Promise<EncryptedData>;
  decrypt(encryptedData: EncryptedData, key: Buffer): Promise<Buffer>;
  generateKey(password?: string, salt?: Buffer): Promise<Buffer>;
}

export interface SecureKeyStore {
  store(keyId: string, encryptedKey: EncryptedData, keyInfo: KeyInfo): Promise<void>;
  retrieve(keyId: string): Promise<EncryptedData | null>;
  delete(keyId: string): Promise<void>;
  listKeys(): Promise<KeyInfo[]>;
  storeMasterKey(key: Buffer): Promise<void>;
  retrieveMasterKey(): Promise<Buffer | null>;
}

export interface DataAnonymizer {
  anonymize(data: any): Promise<any>;
  pseudonymize(data: any, userId: string): Promise<any>;
}

export interface RetentionPolicy {
  dataType: string;
  retentionDays: number;
  cleanup(cutoffDate: Date): Promise<void>;
}

export interface SecurityAuditEvent {
  id: string;
  timestamp: Date;
  type: 'encryption' | 'decryption' | 'key_access' | 'authentication' | 'authorization' | 'data_access';
  severity: 'info' | 'warning' | 'error' | 'critical';
  userId?: string;
  sessionId?: string;
  operation: string;
  resource?: string;
  success: boolean;
  details?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
}

export interface SecurityPolicy {
  id: string;
  name: string;
  description: string;
  rules: SecurityRule[];
  enabled: boolean;
  priority: number;
}

export interface SecurityRule {
  id: string;
  condition: SecurityCondition;
  action: SecurityAction;
  enabled: boolean;
}

export interface SecurityCondition {
  type: 'user' | 'resource' | 'operation' | 'time' | 'location' | 'custom';
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'matches' | 'custom';
  value: any;
  customValidator?: (context: SecurityContext) => boolean;
}

export interface SecurityAction {
  type: 'allow' | 'deny' | 'require_auth' | 'log' | 'alert' | 'custom';
  parameters?: Record<string, any>;
  customHandler?: (context: SecurityContext) => Promise<boolean>;
}

export interface SecureStorageOptions {
  encrypt?: boolean;
  compress?: boolean;
  backup?: boolean;
  ttl?: number; // Time to live in seconds
  metadata?: Record<string, any>;
}

export interface HashOptions {
  algorithm: 'sha256' | 'sha512' | 'bcrypt' | 'argon2' | 'scrypt';
  saltRounds?: number;
  salt?: string | Buffer;
  iterations?: number;
  memory?: number; // For Argon2
  parallelism?: number; // For Argon2
}

export interface TokenOptions {
  length?: number;
  charset?: 'alphanumeric' | 'hex' | 'base64' | 'custom';
  customCharset?: string;
  prefix?: string;
  suffix?: string;
}

// Default security configuration
export const DEFAULT_SECURITY_CONFIG: SecurityConfig = {
  encryption: {
    algorithm: 'AES-256-GCM',
    keyDerivation: 'PBKDF2',
    iterations: 100000,
    saltLength: 32
  },
  storage: {
    encryptSensitiveData: true,
    secureDelete: true,
    memoryProtection: true
  },
  authentication: {
    enabled: false,
    method: 'password',
    sessionTimeout: 3600, // 1 hour
    maxAttempts: 3
  },
  privacy: {
    dataMinimization: true,
    anonymizeAnalytics: true,
    autoCleanup: true,
    retentionPeriod: 90 // days
  }
};
