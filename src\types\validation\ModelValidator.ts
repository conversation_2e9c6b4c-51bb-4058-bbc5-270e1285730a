/**
 * Model validation implementation
 * Comprehensive validation system for all data models
 */

import {
  ValidationRule,
  ValidationResult,
  ValidationError,
  ValidationWarning,
  ValidationContext,
  Validator,
  Schema,
  createValidationError,
  createValidationResult
} from './index';

export class ModelValidator implements Validator {
  private readonly emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  private readonly urlRegex = /^https?:\/\/[^\s/$.?#].[^\s]*$/i;

  validate(value: unknown, schema: Schema, context?: ValidationContext): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    try {
      this.validateValue(value, schema, context || this.createDefaultContext(), errors, warnings);
    } catch (error) {
      errors.push(createValidationError(
        context?.field || 'root',
        `Validation failed: ${error instanceof Error ? error.message : String(error)}`,
        'VALIDATION_ERROR',
        value,
        { type: 'custom', message: 'Internal validation error', severity: 'error' }
      ));
    }

    return createValidationResult(errors.length === 0, errors, warnings);
  }

  async validateAsync(value: unknown, schema: Schema, context?: ValidationContext): Promise<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    try {
      await this.validateValueAsync(value, schema, context || this.createDefaultContext(), errors, warnings);
    } catch (error) {
      errors.push(createValidationError(
        context?.field || 'root',
        `Async validation failed: ${error instanceof Error ? error.message : String(error)}`,
        'ASYNC_VALIDATION_ERROR',
        value,
        { type: 'custom', message: 'Internal async validation error', severity: 'error' }
      ));
    }

    return createValidationResult(errors.length === 0, errors, warnings);
  }

  private validateValue(
    value: unknown,
    schema: Schema,
    context: ValidationContext,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): void {
    // Type validation
    this.validateType(value, schema, context, errors);

    // Custom validation rules
    if (schema.validation) {
      for (const rule of schema.validation) {
        this.validateRule(value, rule, context, errors, warnings);
      }
    }

    // Schema-specific validation
    if (schema.type === 'object' && typeof value === 'object' && value !== null) {
      this.validateObject(value as Record<string, unknown>, schema, context, errors, warnings);
    } else if (schema.type === 'array' && Array.isArray(value)) {
      this.validateArray(value, schema, context, errors, warnings);
    }
  }

  private async validateValueAsync(
    value: unknown,
    schema: Schema,
    context: ValidationContext,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): Promise<void> {
    // Sync validation first
    this.validateValue(value, schema, context, errors, warnings);

    // Async validation rules
    if (schema.validation) {
      for (const rule of schema.validation) {
        if (rule.async && rule.validator) {
          try {
            const isValid = await rule.validator(value, context);
            if (!isValid) {
              const error = createValidationError(
                context.field,
                rule.message,
                `ASYNC_${rule.type.toUpperCase()}`,
                value,
                rule
              );

              if (rule.severity === 'error') {
                errors.push(error);
              } else {
                warnings.push(error as ValidationWarning);
              }
            }
          } catch (error) {
            errors.push(createValidationError(
              context.field,
              `Async validation error: ${error instanceof Error ? error.message : String(error)}`,
              'ASYNC_VALIDATOR_ERROR',
              value,
              rule
            ));
          }
        }
      }
    }
  }

  private validateType(
    value: unknown,
    schema: Schema,
    context: ValidationContext,
    errors: ValidationError[]
  ): void {
    const actualType = this.getValueType(value);
    
    if (actualType !== schema.type && !(schema.type === 'null' && value === null)) {
      errors.push(createValidationError(
        context.field,
        `Expected ${schema.type}, got ${actualType}`,
        'TYPE_MISMATCH',
        value,
        { type: 'custom', message: 'Type mismatch', severity: 'error' }
      ));
    }
  }

  private validateRule(
    value: unknown,
    rule: ValidationRule,
    context: ValidationContext,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): void {
    let isValid = true;
    let errorCode = rule.type.toUpperCase();

    switch (rule.type) {
      case 'required':
        isValid = value !== null && value !== undefined && value !== '';
        break;

      case 'min':
        if (typeof value === 'number' && typeof rule.value === 'number') {
          isValid = value >= rule.value;
        }
        break;

      case 'max':
        if (typeof value === 'number' && typeof rule.value === 'number') {
          isValid = value <= rule.value;
        }
        break;

      case 'minLength':
        if (typeof value === 'string' && typeof rule.value === 'number') {
          isValid = value.length >= rule.value;
        } else if (Array.isArray(value) && typeof rule.value === 'number') {
          isValid = value.length >= rule.value;
        }
        break;

      case 'maxLength':
        if (typeof value === 'string' && typeof rule.value === 'number') {
          isValid = value.length <= rule.value;
        } else if (Array.isArray(value) && typeof rule.value === 'number') {
          isValid = value.length <= rule.value;
        }
        break;

      case 'pattern':
        if (typeof value === 'string' && rule.value instanceof RegExp) {
          isValid = rule.value.test(value);
        }
        break;

      case 'email':
        if (typeof value === 'string') {
          isValid = this.emailRegex.test(value);
        }
        break;

      case 'url':
        if (typeof value === 'string') {
          isValid = this.urlRegex.test(value);
        }
        break;

      case 'enum':
        if (Array.isArray(rule.value)) {
          isValid = rule.value.includes(value);
        }
        break;

      case 'custom':
        if (rule.validator && !rule.async) {
          try {
            isValid = Boolean(rule.validator(value, context));
          } catch (error) {
            isValid = false;
            errorCode = 'CUSTOM_VALIDATOR_ERROR';
          }
        }
        break;

      default:
        // Unknown rule type, skip validation
        return;
    }

    if (!isValid) {
      const error = createValidationError(
        context.field,
        rule.message,
        errorCode,
        value,
        rule
      );

      if (rule.severity === 'error') {
        errors.push(error);
      } else {
        warnings.push(error as ValidationWarning);
      }
    }
  }

  private validateObject(
    obj: Record<string, unknown>,
    schema: Schema,
    context: ValidationContext,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): void {
    // Check required properties
    if (schema.required) {
      for (const requiredField of schema.required) {
        if (!(requiredField in obj) || obj[requiredField] === undefined) {
          errors.push(createValidationError(
            `${context.field}.${requiredField}`,
            `Required field '${requiredField}' is missing`,
            'REQUIRED_FIELD_MISSING',
            undefined,
            { type: 'required', message: 'Required field missing', severity: 'error' }
          ));
        }
      }
    }

    // Validate properties
    if (schema.properties) {
      for (const [propName, propSchema] of Object.entries(schema.properties)) {
        if (propName in obj) {
          const propContext: ValidationContext = {
            field: context.field ? `${context.field}.${propName}` : propName,
            object: obj,
            path: [...context.path, propName],
            root: context.root
          };

          this.validateValue(obj[propName], propSchema, propContext, errors, warnings);
        }
      }
    }

    // Check additional properties
    if (schema.additionalProperties === false && schema.properties) {
      const allowedProps = new Set(Object.keys(schema.properties));
      for (const propName of Object.keys(obj)) {
        if (!allowedProps.has(propName)) {
          warnings.push(createValidationError(
            `${context.field}.${propName}`,
            `Additional property '${propName}' is not allowed`,
            'ADDITIONAL_PROPERTY',
            obj[propName],
            { type: 'custom', message: 'Additional property not allowed', severity: 'warning' }
          ) as ValidationWarning);
        }
      }
    }
  }

  private validateArray(
    arr: unknown[],
    schema: Schema,
    context: ValidationContext,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): void {
    if (schema.items) {
      for (let i = 0; i < arr.length; i++) {
        const itemContext: ValidationContext = {
          field: `${context.field}[${i}]`,
          object: context.object,
          path: [...context.path, i.toString()],
          root: context.root
        };

        this.validateValue(arr[i], schema.items, itemContext, errors, warnings);
      }
    }
  }

  private getValueType(value: unknown): string {
    if (value === null) return 'null';
    if (Array.isArray(value)) return 'array';
    return typeof value;
  }

  private createDefaultContext(): ValidationContext {
    return {
      field: 'root',
      object: {},
      path: [],
      root: {}
    };
  }
}
