/**
 * Test helper utilities for creating test data
 */

import {
  Prompt,
  Category,
  PromptVariable,
  PromptMetadata,
  PromptUsage
} from '../../src/services/prompt-library/types';

/**
 * Create a complete test prompt with all required properties
 */
export const createTestPrompt = (overrides: Partial<Omit<Prompt, 'id'>> = {}): Omit<Prompt, 'id'> => {
  const now = new Date();

  return {
    title: 'Test Prompt',
    content: 'This is a test prompt content',
    description: 'A test prompt for unit testing',
    category: 'Testing',
    tags: ['test', 'example'],
    variables: [],
    metadata: {
      author: 'Test Author',
      source: 'user' as const,
      language: 'en',
      estimatedTokens: 100,
      complexity: 'simple' as const,
      keywords: ['test'],
      relatedPrompts: [],
      customFields: {}
    },
    version: 1,
    isTemplate: false,
    isFavorite: false,
    isArchived: false,
    usage: {
      count: 0,
      lastUsed: now,
      totalTokens: 0,
      totalCost: 0,
      averageExecutionTime: 0,
      contexts: []
    },
    createdAt: now,
    updatedAt: now,
    ...overrides
  };
};

/**
 * Create a complete test category with all required properties
 */
export const createTestCategory = (overrides: Partial<Omit<Category, 'id'>> = {}): Omit<Category, 'id'> => {
  const now = new Date();

  return {
    name: 'Test Category',
    description: 'A test category for unit testing',
    color: '#007bff',
    sortOrder: 0,
    metadata: {
      createdAt: now,
      promptCount: 0
    },
    ...overrides
  };
};

/**
 * Create test prompt variables with all required properties
 */
export const createTestPromptVariable = (overrides: Partial<PromptVariable> = {}): PromptVariable => {
  return {
    name: 'testVar',
    description: 'A test variable',
    type: 'string',
    required: true,
    defaultValue: 'default',
    validation: [
      {
        type: 'minLength',
        value: 0,
        message: 'Minimum length is 0'
      }
    ],
    ...overrides
  };
};

/**
 * Create test prompt metadata
 */
export const createTestPromptMetadata = (overrides: Partial<PromptMetadata> = {}): PromptMetadata => {
  return {
    author: 'Test Author',
    source: 'user' as const,
    language: 'en',
    estimatedTokens: 100,
    complexity: 'simple' as const,
    keywords: ['test'],
    relatedPrompts: [],
    customFields: {},
    ...overrides
  };
};

/**
 * Create test prompt usage data
 */
export const createTestPromptUsage = (overrides: Partial<PromptUsage> = {}): PromptUsage => {
  return {
    count: 0,
    lastUsed: new Date(),
    totalTokens: 0,
    totalCost: 0,
    averageExecutionTime: 0,
    contexts: [],
    ...overrides
  };
};

/**
 * Create minimal test prompt for basic operations
 */
export const createMinimalTestPrompt = (overrides: Partial<Omit<Prompt, 'id'>> = {}): Omit<Prompt, 'id'> => {
  return createTestPrompt({
    title: 'Minimal Test Prompt',
    content: 'Minimal content',
    description: 'Minimal description',
    category: 'Testing',
    tags: [],
    variables: [],
    ...overrides
  });
};

/**
 * Create minimal test category for basic operations
 */
export const createMinimalTestCategory = (overrides: Partial<Omit<Category, 'id'>> = {}): Omit<Category, 'id'> => {
  return createTestCategory({
    name: 'Minimal Category',
    description: 'Minimal description',
    ...overrides
  });
};
