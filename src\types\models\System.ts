/**
 * System and configuration model definitions
 * System-level models for configuration, errors, logging, and analytics
 */

// Configuration types
export type ConfigSettingType = 
  | 'string' 
  | 'number' 
  | 'boolean' 
  | 'array' 
  | 'object' 
  | 'enum' 
  | 'color' 
  | 'file' 
  | 'directory';

export type SettingCategory = 'basic' | 'advanced' | 'expert' | 'debug';
export type ConfigSource = 'default' | 'user' | 'system' | 'environment' | 'import';

// Error types
export type ErrorType = 
  | 'validation'
  | 'network'
  | 'filesystem'
  | 'database'
  | 'ai_provider'
  | 'stt_provider'
  | 'permission'
  | 'configuration'
  | 'unknown';

export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';
export type LogLevel = 'trace' | 'debug' | 'info' | 'warn' | 'error' | 'fatal';

// Analytics types
export type EventCategory = 
  | 'user_action'
  | 'system_event'
  | 'performance'
  | 'error'
  | 'feature_usage'
  | 'conversion';

export type DeviceType = 'desktop' | 'laptop' | 'tablet' | 'mobile' | 'server';
export type NetworkType = 'wifi' | 'ethernet' | 'cellular' | 'bluetooth' | 'unknown';
export type MetricUnit = 'count' | 'bytes' | 'milliseconds' | 'seconds' | 'percent' | 'rate';
export type MetricType = 'counter' | 'gauge' | 'histogram' | 'timer';

// Configuration option
export interface ConfigOption {
  readonly value: unknown;
  readonly label: string;
  readonly description?: string;
  readonly icon?: string;
  readonly disabled?: boolean;
}

// Configuration setting
export interface ConfigSetting {
  readonly type: ConfigSettingType;
  readonly title: string;
  readonly description: string;
  readonly defaultValue: unknown;
  readonly required: boolean;
  readonly validation?: readonly ValidationRule[];
  readonly options?: readonly ConfigOption[];
  readonly sensitive: boolean;
  readonly restartRequired: boolean;
  readonly category: SettingCategory;
  readonly dependencies?: readonly string[];
  readonly deprecated?: boolean;
  readonly since?: string;
}

// Configuration section
export interface ConfigSection {
  readonly title: string;
  readonly description: string;
  readonly icon?: string;
  readonly order: number;
  readonly settings: Readonly<Record<string, ConfigSetting>>;
}

// Schema metadata
export interface SchemaMetadata {
  readonly version: string;
  readonly createdAt: Date;
  readonly updatedAt: Date;
  readonly author: string;
  readonly description: string;
}

// Configuration schema
export interface ConfigurationSchema {
  readonly version: string;
  readonly sections: Readonly<Record<string, ConfigSection>>;
  readonly metadata: SchemaMetadata;
}

// Configuration value
export interface ConfigValue {
  readonly key: string;
  readonly value: unknown;
  readonly type: ConfigSettingType;
  readonly source: ConfigSource;
  readonly timestamp: Date;
  readonly valid: boolean;
  readonly encrypted: boolean;
}

// Configuration change event
export interface ConfigChangeEvent {
  readonly key: string;
  readonly oldValue: unknown;
  readonly newValue: unknown;
  readonly source: ConfigSource;
  readonly timestamp: Date;
  readonly userId?: string;
  readonly sessionId?: string;
}

// Validation rule (from validation module)
export interface ValidationRule {
  readonly type: string;
  readonly value?: unknown;
  readonly message: string;
  readonly severity: 'error' | 'warning' | 'info';
}

// Error context
export interface ErrorContext {
  readonly component: string;
  readonly action: string;
  readonly userId?: string;
  readonly sessionId?: string;
  readonly requestId?: string;
  readonly userAgent?: string;
  readonly url?: string;
  readonly data?: Readonly<Record<string, unknown>>;
}

// Error metadata
export interface ErrorMetadata {
  readonly appVersion: string;
  readonly platform: string;
  readonly osVersion: string;
  readonly memoryUsage: number;
  readonly cpuUsage: number;
  readonly diskSpace: number;
  readonly networkStatus: string;
}

// Application error model
export interface AppError {
  readonly id: string;
  readonly type: ErrorType;
  readonly code: string;
  readonly message: string;
  readonly stack?: string;
  readonly cause?: AppError;
  readonly context: ErrorContext;
  readonly timestamp: Date;
  readonly severity: ErrorSeverity;
  readonly resolved: boolean;
  readonly metadata: ErrorMetadata;
}

// Log context
export interface LogContext {
  readonly module?: string;
  readonly function?: string;
  readonly userId?: string;
  readonly sessionId?: string;
  readonly requestId?: string;
  readonly correlationId?: string;
  readonly tags?: readonly string[];
}

// Performance information
export interface PerformanceInfo {
  readonly startTime: number;
  readonly endTime: number;
  readonly duration: number;
  readonly memoryBefore?: MemoryUsage;
  readonly memoryAfter?: MemoryUsage;
  readonly cpuUsage?: number;
}

// Memory usage (imported from User.ts to avoid conflicts)
import type { MemoryUsage } from './User';

// Log entry model
export interface LogEntry {
  readonly id: string;
  readonly timestamp: Date;
  readonly level: LogLevel;
  readonly message: string;
  readonly component: string;
  readonly context: LogContext;
  readonly metadata?: Readonly<Record<string, unknown>>;
  readonly error?: AppError;
  readonly performance?: PerformanceInfo;
}

// Application context
export interface AppContext {
  readonly name: string;
  readonly version: string;
  readonly build?: string;
  readonly environment: string;
  readonly installId: string;
}

// Device context
export interface DeviceContext {
  readonly id?: string;
  readonly type: DeviceType;
  readonly manufacturer?: string;
  readonly model?: string;
  readonly memory: number;
  readonly storage: number;
}

// OS context
export interface OSContext {
  readonly name: string;
  readonly version: string;
  readonly architecture: string;
  readonly locale: string;
  readonly timezone: string;
}

// Screen context
export interface ScreenContext {
  readonly width: number;
  readonly height: number;
  readonly density: number;
  readonly colorDepth: number;
}

// Network context
export interface NetworkContext {
  readonly type: NetworkType;
  readonly speed?: string;
  readonly online: boolean;
}

// Event context
export interface EventContext {
  readonly app: AppContext;
  readonly device: DeviceContext;
  readonly os: OSContext;
  readonly screen?: ScreenContext;
  readonly network?: NetworkContext;
}

// Analytics event
export interface AnalyticsEvent {
  readonly id: string;
  readonly name: string;
  readonly category: EventCategory;
  readonly properties: Readonly<Record<string, unknown>>;
  readonly timestamp: Date;
  readonly userId?: string;
  readonly sessionId?: string;
  readonly anonymousId?: string;
  readonly context: EventContext;
}

// Metrics model
export interface Metric {
  readonly name: string;
  readonly value: number;
  readonly unit: MetricUnit;
  readonly type: MetricType;
  readonly tags: Readonly<Record<string, string>>;
  readonly timestamp: Date;
  readonly metadata?: Readonly<Record<string, unknown>>;
}
