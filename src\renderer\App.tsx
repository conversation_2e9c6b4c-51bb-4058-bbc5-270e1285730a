import React, { useEffect, useState } from 'react';

// Declare the global electronAPI
declare global {
  interface Window {
    electronAPI: {
      getVersion: () => Promise<string>;
      getPlatform: () => Promise<string>;
      minimizeWindow: () => Promise<void>;
      maximizeWindow: () => Promise<void>;
      closeWindow: () => Promise<void>;
    };
  }
}

const App: React.FC = () => {
  const [appVersion, setAppVersion] = useState<string>('');
  const [platform, setPlatform] = useState<string>('');

  useEffect(() => {
    // Get app information
    const getAppInfo = async () => {
      try {
        const version = await window.electronAPI.getVersion();
        const platformInfo = await window.electronAPI.getPlatform();
        setAppVersion(version);
        setPlatform(platformInfo);
      } catch (error) {
        console.error('Failed to get app info:', error);
      }
    };

    getAppInfo();
  }, []);

  const handleMinimize = () => {
    window.electronAPI.minimizeWindow();
  };

  const handleMaximize = () => {
    window.electronAPI.maximizeWindow();
  };

  const handleClose = () => {
    window.electronAPI.closeWindow();
  };

  return (
    <div style={{ 
      height: '100vh', 
      display: 'flex', 
      flexDirection: 'column',
      backgroundColor: '#ffffff'
    }}>
      {/* Title Bar */}
      <div style={{
        height: '32px',
        backgroundColor: '#f0f0f0',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '0 16px',
        borderBottom: '1px solid #e0e0e0',
        // @ts-ignore - WebkitAppRegion is a valid CSS property for Electron
        WebkitAppRegion: 'drag'
      }}>
        <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
          PromptPilot Desktop
        </div>
        <div style={{
          display: 'flex',
          gap: '8px',
          // @ts-ignore - WebkitAppRegion is a valid CSS property for Electron
          WebkitAppRegion: 'no-drag'
        }}>
          <button onClick={handleMinimize} style={buttonStyle}>−</button>
          <button onClick={handleMaximize} style={buttonStyle}>□</button>
          <button onClick={handleClose} style={buttonStyle}>×</button>
        </div>
      </div>

      {/* Main Content */}
      <div style={{ 
        flex: 1, 
        display: 'flex', 
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '40px'
      }}>
        <h1 style={{ 
          fontSize: '32px', 
          marginBottom: '16px',
          color: '#333'
        }}>
          Welcome to PromptPilot Desktop
        </h1>
        
        <p style={{ 
          fontSize: '18px', 
          color: '#666',
          textAlign: 'center',
          maxWidth: '600px',
          lineHeight: '1.6'
        }}>
          AI-powered prompt management and enhancement desktop application with 
          privacy-first, local-first design principles.
        </p>

        <div style={{
          marginTop: '32px',
          padding: '20px',
          backgroundColor: '#f8f9fa',
          borderRadius: '8px',
          border: '1px solid #e9ecef'
        }}>
          <h3 style={{ margin: '0 0 12px 0', color: '#495057' }}>System Information</h3>
          <p style={{ margin: '4px 0', color: '#6c757d' }}>Version: {appVersion}</p>
          <p style={{ margin: '4px 0', color: '#6c757d' }}>Platform: {platform}</p>
        </div>

        <div style={{
          marginTop: '32px',
          padding: '20px',
          backgroundColor: '#e8f5e8',
          borderRadius: '8px',
          border: '1px solid #c3e6c3'
        }}>
          <h3 style={{ margin: '0 0 12px 0', color: '#155724' }}>Project Setup Complete</h3>
          <p style={{ margin: '4px 0', color: '#155724' }}>
            ✅ Electron + TypeScript foundation established<br/>
            ✅ Build configuration ready<br/>
            ✅ Development environment configured<br/>
            ✅ Security best practices implemented
          </p>
        </div>
      </div>
    </div>
  );
};

const buttonStyle: React.CSSProperties = {
  width: '24px',
  height: '24px',
  border: 'none',
  backgroundColor: 'transparent',
  cursor: 'pointer',
  fontSize: '16px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  borderRadius: '4px'
};

export default App;
