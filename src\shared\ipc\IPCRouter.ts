/**
 * IPC Router
 * Central routing system for IPC messages with validation, middleware, and error handling
 */

import { EventEmitter } from 'events';
import { 
  IPCHandler, 
  IPCMiddleware, 
  IPCRouterConfig, 
  IPCError, 
  IPCErrorCode,
  PerformanceMetrics,
  QueuedMessage
} from './types';
import { IPCValidator } from './IPCValidator';
import { logger } from '../../core/logger';

export class IPCRouter extends EventEmitter {
  private handlers: Map<string, IPCHandler>;
  private middleware: IPCMiddleware[];
  private validator: IPCValidator;
  private config: IPCRouterConfig;
  private metrics: PerformanceMetrics;
  private messageQueue: QueuedMessage[];
  private processingQueue: boolean;

  constructor(config: Partial<IPCRouterConfig> = {}) {
    super();
    
    this.handlers = new Map();
    this.middleware = [];
    this.validator = new IPCValidator();
    this.messageQueue = [];
    this.processingQueue = false;
    
    this.config = {
      enableValidation: true,
      enableMiddleware: true,
      enableMetrics: true,
      enableLogging: true,
      timeout: 30000,
      ...config
    };

    this.metrics = {
      messageCount: 0,
      averageResponseTime: 0,
      errorRate: 0,
      queueSize: 0,
      lastResetTime: Date.now()
    };

    this.setupErrorHandling();
  }

  /**
   * Register a handler for a specific channel
   */
  registerHandler(channel: string, handler: IPCHandler): void {
    if (this.handlers.has(channel)) {
      logger.warn('Overriding existing handler for channel', { channel });
    }

    this.handlers.set(channel, handler);
    
    if (this.config.enableLogging) {
      logger.debug('IPC handler registered', { channel });
    }
  }

  /**
   * Unregister a handler for a specific channel
   */
  unregisterHandler(channel: string): boolean {
    const removed = this.handlers.delete(channel);
    
    if (removed && this.config.enableLogging) {
      logger.debug('IPC handler unregistered', { channel });
    }
    
    return removed;
  }

  /**
   * Add middleware to the processing pipeline
   */
  addMiddleware(middleware: IPCMiddleware): void {
    this.middleware.push(middleware);
    
    if (this.config.enableLogging) {
      logger.debug('IPC middleware added', { name: middleware.name });
    }
  }

  /**
   * Remove middleware from the processing pipeline
   */
  removeMiddleware(name: string): boolean {
    const index = this.middleware.findIndex(m => m.name === name);
    
    if (index !== -1) {
      this.middleware.splice(index, 1);
      
      if (this.config.enableLogging) {
        logger.debug('IPC middleware removed', { name });
      }
      
      return true;
    }
    
    return false;
  }

  /**
   * Route a message to the appropriate handler
   */
  async routeMessage(
    channel: string, 
    data: any, 
    event?: Electron.IpcMainInvokeEvent
  ): Promise<any> {
    const startTime = Date.now();
    const messageId = this.generateMessageId();

    try {
      // Update metrics
      if (this.config.enableMetrics) {
        this.metrics.messageCount++;
        this.metrics.queueSize = this.messageQueue.length;
      }

      // Validate message if enabled
      if (this.config.enableValidation) {
        const validationError = this.validator.validateMessage(channel, data, 'renderer');
        if (validationError) {
          throw this.createIPCError(validationError.code as IPCErrorCode, validationError.message);
        }
      }

      // Sanitize data
      const sanitizedData = this.validator.sanitizeData(data);

      // Check if handler exists
      const handler = this.handlers.get(channel);
      if (!handler) {
        throw this.createIPCError(
          IPCErrorCode.HANDLER_NOT_FOUND, 
          `No handler registered for channel: ${channel}`
        );
      }

      // Execute middleware pipeline
      let result: any;
      if (this.config.enableMiddleware && this.middleware.length > 0) {
        result = await this.executeMiddleware(channel, sanitizedData, handler, event);
      } else {
        result = await this.executeHandler(handler, sanitizedData, event);
      }

      // Update performance metrics
      if (this.config.enableMetrics) {
        this.updateMetrics(startTime, false);
      }

      // Log successful execution
      if (this.config.enableLogging) {
        logger.debug('IPC message routed successfully', { 
          channel, 
          messageId,
          duration: Date.now() - startTime 
        });
      }

      return result;

    } catch (error) {
      // Update error metrics
      if (this.config.enableMetrics) {
        this.updateMetrics(startTime, true);
      }

      // Log error
      if (this.config.enableLogging) {
        logger.error('IPC message routing failed', { 
          channel, 
          messageId,
          error: error instanceof Error ? error.message : String(error),
          duration: Date.now() - startTime 
        });
      }

      // Emit error event
      this.emit('error', { channel, error, messageId });

      throw error;
    }
  }

  /**
   * Execute middleware pipeline
   */
  private async executeMiddleware(
    channel: string,
    data: any,
    handler: IPCHandler,
    event?: Electron.IpcMainInvokeEvent
  ): Promise<any> {
    let index = 0;

    const next = async (): Promise<any> => {
      if (index >= this.middleware.length) {
        return this.executeHandler(handler, data, event);
      }

      const middleware = this.middleware[index++];
      return middleware.execute(channel, data, next);
    };

    return next();
  }

  /**
   * Execute handler with timeout
   */
  private async executeHandler(
    handler: IPCHandler,
    data: any,
    event?: Electron.IpcMainInvokeEvent
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(this.createIPCError(IPCErrorCode.TIMEOUT, 'Handler execution timed out'));
      }, this.config.timeout);

      Promise.resolve(handler(data, event))
        .then(result => {
          clearTimeout(timeout);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timeout);
          reject(error);
        });
    });
  }

  /**
   * Queue message for processing
   */
  queueMessage(message: QueuedMessage): void {
    this.messageQueue.push(message);
    this.processQueue();
  }

  /**
   * Process message queue
   */
  private async processQueue(): Promise<void> {
    if (this.processingQueue || this.messageQueue.length === 0) {
      return;
    }

    this.processingQueue = true;

    try {
      while (this.messageQueue.length > 0) {
        const message = this.messageQueue.shift()!;
        
        try {
          await this.routeMessage(message.channel, message.data);
        } catch (error) {
          // Handle message retry logic
          if (message.retries < message.maxRetries) {
            message.retries++;
            this.messageQueue.unshift(message); // Retry at front of queue
          } else {
            logger.error('Message processing failed after max retries', { 
              messageId: message.id,
              channel: message.channel,
              retries: message.retries 
            });
          }
        }
      }
    } finally {
      this.processingQueue = false;
    }
  }

  /**
   * Get current performance metrics
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset performance metrics
   */
  resetMetrics(): void {
    this.metrics = {
      messageCount: 0,
      averageResponseTime: 0,
      errorRate: 0,
      queueSize: this.messageQueue.length,
      lastResetTime: Date.now()
    };

    if (this.config.enableLogging) {
      logger.info('IPC metrics reset');
    }
  }

  /**
   * Update performance metrics
   */
  private updateMetrics(startTime: number, isError: boolean): void {
    const duration = Date.now() - startTime;
    
    // Update average response time
    const totalTime = this.metrics.averageResponseTime * (this.metrics.messageCount - 1);
    this.metrics.averageResponseTime = (totalTime + duration) / this.metrics.messageCount;

    // Update error rate
    if (isError) {
      const totalErrors = this.metrics.errorRate * (this.metrics.messageCount - 1);
      this.metrics.errorRate = (totalErrors + 1) / this.metrics.messageCount;
    } else {
      const totalErrors = this.metrics.errorRate * (this.metrics.messageCount - 1);
      this.metrics.errorRate = totalErrors / this.metrics.messageCount;
    }
  }

  /**
   * Create standardized IPC error
   */
  private createIPCError(code: IPCErrorCode, message: string, details?: any): IPCError {
    const error: IPCError = {
      code,
      message
    };

    if (details) {
      error.details = details;
    }

    const stack = new Error().stack;
    if (stack) {
      error.stack = stack;
    }

    return error;
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `ipc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Setup error handling
   */
  private setupErrorHandling(): void {
    this.on('error', (errorInfo) => {
      logger.error('IPC Router error', errorInfo);
    });

    // Handle uncaught errors in handlers
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception in IPC handler', { error: error.message, stack: error.stack });
    });

    process.on('unhandledRejection', (reason) => {
      logger.error('Unhandled rejection in IPC handler', { reason });
    });
  }

  /**
   * Get all registered channels
   */
  getRegisteredChannels(): string[] {
    return Array.from(this.handlers.keys());
  }

  /**
   * Check if handler is registered for channel
   */
  hasHandler(channel: string): boolean {
    return this.handlers.has(channel);
  }

  /**
   * Get validator instance
   */
  getValidator(): IPCValidator {
    return this.validator;
  }

  /**
   * Update router configuration
   */
  updateConfig(updates: Partial<IPCRouterConfig>): void {
    this.config = { ...this.config, ...updates };

    if (this.config.enableLogging) {
      logger.info('IPC Router configuration updated', updates);
    }
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.handlers.clear();
    this.middleware.length = 0;
    this.messageQueue.length = 0;
    this.removeAllListeners();

    if (this.config.enableLogging) {
      logger.info('IPC Router destroyed');
    }
  }
}
