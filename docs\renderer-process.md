# Renderer Process Module Specification

## 🎯 Purpose

The Renderer Process module manages the user interface layer of the application, providing a responsive and intuitive experience for prompt management, enhancement, and execution.

## 📋 Responsibilities

### UI Management
- Component rendering and state management
- User interaction handling
- Real-time UI updates
- Theme and styling management

### Data Presentation
- Prompt library visualization
- Enhancement result display
- Voice input feedback
- System status indicators

### User Experience
- Responsive design implementation
- Accessibility compliance
- Keyboard navigation
- Context-sensitive help

## 🏗️ Architecture

### Component Structure
```typescript
interface AppComponent {
  MainWindow: React.FC;
  FloatingWindow: React.FC;
  SystemTray: React.FC;
}

interface CoreComponents {
  PromptEditor: React.FC<PromptEditorProps>;
  PromptLibrary: React.FC<PromptLibraryProps>;
  VoiceInput: React.FC<VoiceInputProps>;
  ContextViewer: React.FC<ContextViewerProps>;
  SettingsPanel: React.FC<SettingsPanelProps>;
}
```

### State Management
```typescript
interface AppState {
  ui: UIState;
  prompts: PromptsState;
  voice: VoiceState;
  context: ContextState;
  settings: SettingsState;
}

interface UIState {
  activeWindow: 'main' | 'floating' | null;
  theme: 'light' | 'dark' | 'system';
  sidebarCollapsed: boolean;
  loading: boolean;
  notifications: Notification[];
}
```

## 🎨 UI Components

### Main Window Layout
```typescript
const MainWindow: React.FC = () => {
  return (
    <div className="main-window">
      <Header />
      <div className="content-area">
        <Sidebar />
        <MainContent />
      </div>
      <StatusBar />
    </div>
  );
};
```

### Floating Window Layout
```typescript
const FloatingWindow: React.FC = () => {
  return (
    <div className="floating-window">
      <QuickPromptInput />
      <ContextPreview />
      <ActionButtons />
    </div>
  );
};
```

### Prompt Editor Component
```typescript
interface PromptEditorProps {
  prompt: Prompt | null;
  onSave: (prompt: Prompt) => void;
  onCancel: () => void;
  onEnhance: (text: string) => void;
}

const PromptEditor: React.FC<PromptEditorProps> = ({
  prompt,
  onSave,
  onCancel,
  onEnhance
}) => {
  const [content, setContent] = useState(prompt?.content || '');
  const [title, setTitle] = useState(prompt?.title || '');
  const [tags, setTags] = useState(prompt?.tags || []);
  
  return (
    <div className="prompt-editor">
      <input 
        value={title}
        onChange={(e) => setTitle(e.target.value)}
        placeholder="Prompt title..."
      />
      <textarea
        value={content}
        onChange={(e) => setContent(e.target.value)}
        placeholder="Enter your prompt..."
      />
      <TagInput tags={tags} onChange={setTags} />
      <div className="actions">
        <Button onClick={() => onEnhance(content)}>Enhance</Button>
        <Button onClick={() => onSave({...prompt, title, content, tags})}>
          Save
        </Button>
        <Button variant="secondary" onClick={onCancel}>
          Cancel
        </Button>
      </div>
    </div>
  );
};
```

## 🔄 State Management

### Redux Store Configuration
```typescript
import { configureStore } from '@reduxjs/toolkit';

const store = configureStore({
  reducer: {
    ui: uiSlice.reducer,
    prompts: promptsSlice.reducer,
    voice: voiceSlice.reducer,
    context: contextSlice.reducer,
    settings: settingsSlice.reducer
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['voice/audioData']
      }
    })
});
```

### UI State Slice
```typescript
const uiSlice = createSlice({
  name: 'ui',
  initialState: {
    activeWindow: null,
    theme: 'system',
    sidebarCollapsed: false,
    loading: false,
    notifications: []
  },
  reducers: {
    setActiveWindow: (state, action) => {
      state.activeWindow = action.payload;
    },
    setTheme: (state, action) => {
      state.theme = action.payload;
    },
    toggleSidebar: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    addNotification: (state, action) => {
      state.notifications.push(action.payload);
    },
    removeNotification: (state, action) => {
      state.notifications = state.notifications.filter(
        n => n.id !== action.payload
      );
    }
  }
});
```

## 📡 IPC Integration

### IPC Service Layer
```typescript
class IPCService {
  // Prompt operations
  async savePrompt(prompt: Prompt): Promise<void> {
    return window.electronAPI.invoke('save-prompt', prompt);
  }
  
  async loadPrompts(): Promise<Prompt[]> {
    return window.electronAPI.invoke('load-prompts');
  }
  
  async enhancePrompt(text: string): Promise<string> {
    return window.electronAPI.invoke('enhance-prompt', text);
  }
  
  // Voice operations
  async startVoiceRecording(): Promise<void> {
    return window.electronAPI.invoke('start-voice-recording');
  }
  
  async stopVoiceRecording(): Promise<string> {
    return window.electronAPI.invoke('stop-voice-recording');
  }
  
  // Context operations
  async captureScreen(): Promise<string> {
    return window.electronAPI.invoke('capture-screen');
  }
  
  async getClipboardText(): Promise<string> {
    return window.electronAPI.invoke('get-clipboard-text');
  }
}
```

### Event Listeners
```typescript
useEffect(() => {
  // Listen for hotkey events
  const unsubscribeHotkey = window.electronAPI.onHotkeyTriggered(
    (event, hotkeyId) => {
      dispatch(handleHotkeyTriggered(hotkeyId));
    }
  );
  
  // Listen for voice input events
  const unsubscribeVoice = window.electronAPI.onVoiceInput(
    (event, transcript) => {
      dispatch(setVoiceTranscript(transcript));
    }
  );
  
  return () => {
    unsubscribeHotkey();
    unsubscribeVoice();
  };
}, [dispatch]);
```

## 🎨 Theming & Styling

### Theme Configuration
```typescript
interface Theme {
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    error: string;
    warning: string;
    success: string;
  };
  typography: {
    fontFamily: string;
    fontSize: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
    };
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
}
```

### CSS-in-JS Implementation
```typescript
import styled from 'styled-components';

const StyledPromptEditor = styled.div`
  background: ${props => props.theme.colors.surface};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 8px;
  padding: ${props => props.theme.spacing.md};
  
  .title-input {
    font-size: ${props => props.theme.typography.fontSize.lg};
    font-weight: 600;
    border: none;
    background: transparent;
    color: ${props => props.theme.colors.text};
    width: 100%;
    margin-bottom: ${props => props.theme.spacing.sm};
    
    &:focus {
      outline: 2px solid ${props => props.theme.colors.primary};
    }
  }
  
  .content-textarea {
    min-height: 200px;
    resize: vertical;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: ${props => props.theme.typography.fontSize.sm};
  }
`;
```

## ♿ Accessibility

### ARIA Implementation
```typescript
const PromptLibrary: React.FC = () => {
  return (
    <div 
      role="region" 
      aria-label="Prompt Library"
      className="prompt-library"
    >
      <h2 id="library-heading">Your Prompts</h2>
      <div 
        role="list" 
        aria-labelledby="library-heading"
        className="prompt-list"
      >
        {prompts.map(prompt => (
          <div 
            key={prompt.id}
            role="listitem"
            tabIndex={0}
            aria-label={`Prompt: ${prompt.title}`}
            onKeyDown={handleKeyDown}
          >
            {prompt.title}
          </div>
        ))}
      </div>
    </div>
  );
};
```

### Keyboard Navigation
```typescript
const useKeyboardNavigation = () => {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'Escape':
          dispatch(closeModal());
          break;
        case 'Enter':
          if (event.ctrlKey) {
            dispatch(enhancePrompt());
          }
          break;
        case 'Tab':
          // Handle focus management
          break;
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [dispatch]);
};
```

## 📱 Responsive Design

### Breakpoint System
```typescript
const breakpoints = {
  mobile: '480px',
  tablet: '768px',
  desktop: '1024px',
  wide: '1440px'
};

const mediaQueries = {
  mobile: `@media (max-width: ${breakpoints.mobile})`,
  tablet: `@media (max-width: ${breakpoints.tablet})`,
  desktop: `@media (min-width: ${breakpoints.desktop})`,
  wide: `@media (min-width: ${breakpoints.wide})`
};
```

### Responsive Components
```typescript
const ResponsiveLayout = styled.div`
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: ${props => props.theme.spacing.md};
  
  ${mediaQueries.tablet} {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }
  
  ${mediaQueries.mobile} {
    padding: ${props => props.theme.spacing.sm};
  }
`;
```

## 🧪 Testing Strategy

### Component Testing
```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { PromptEditor } from './PromptEditor';

describe('PromptEditor', () => {
  test('should render prompt editor with empty state', () => {
    render(
      <Provider store={mockStore}>
        <PromptEditor prompt={null} onSave={jest.fn()} onCancel={jest.fn()} />
      </Provider>
    );
    
    expect(screen.getByPlaceholderText('Prompt title...')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter your prompt...')).toBeInTheDocument();
  });
  
  test('should call onSave when save button is clicked', () => {
    const onSave = jest.fn();
    render(
      <Provider store={mockStore}>
        <PromptEditor prompt={null} onSave={onSave} onCancel={jest.fn()} />
      </Provider>
    );
    
    fireEvent.click(screen.getByText('Save'));
    expect(onSave).toHaveBeenCalled();
  });
});
```

### Integration Testing
```typescript
describe('IPC Integration', () => {
  test('should load prompts on component mount', async () => {
    const mockPrompts = [{ id: '1', title: 'Test Prompt', content: 'Test' }];
    window.electronAPI.invoke.mockResolvedValue(mockPrompts);
    
    render(<PromptLibrary />);
    
    await waitFor(() => {
      expect(screen.getByText('Test Prompt')).toBeInTheDocument();
    });
  });
});
```
