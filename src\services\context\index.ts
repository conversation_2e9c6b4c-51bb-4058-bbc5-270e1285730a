/**
 * Context Service Module
 * Exports all context extraction components and utilities
 */

// Main service
export { ContextExtractor } from './ContextExtractor';

// Individual services
export { TesseractOCREngine } from './OCREngine';
export { ScreenCaptureService } from './ScreenCapture';
export { ClipboardMonitor } from './ClipboardMonitor';
export { WindowTextExtractor } from './WindowTextExtractor';
export { FileContentReader } from './FileContentReader';

// Types and interfaces
export * from './types';

// Re-export commonly used types for convenience
export type {
  IContextExtractor,
  ContextExtractionOptions,
  ExtractedContext,
  ScreenRegion,
  ScreenCaptureOptions,
  CaptureResult,
  OCROptions,
  OCREngine,
  ClipboardContent,
  WindowContent,
  FileContent,
  ContextExtractorConfig
} from './types';
