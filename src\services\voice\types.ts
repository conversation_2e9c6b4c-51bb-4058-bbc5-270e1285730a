/**
 * Voice Module type definitions
 * Speech-to-text capabilities with real-time transcription and audio processing
 */

// Recording configuration
export interface RecordingOptions {
  language: string;
  maxDuration: number; // seconds
  autoStop: boolean;
  noiseReduction: boolean;
  format: 'wav' | 'mp3' | 'ogg';
  sampleRate: number;
  channels: number;
  bitDepth?: number;
}

export interface RecordingState {
  isRecording: boolean;
  duration: number;
  audioLevel: number;
  language: string;
  confidence: number;
}

// STT Service interfaces
export interface STTOptions {
  language: string;
  model: string;
  punctuation: boolean;
  profanityFilter?: boolean;
  speakerDiarization?: boolean;
  wordTimestamps?: boolean;
}

export interface TranscriptionResult {
  text: string;
  confidence: number;
  language: string;
  duration: number;
  words?: WordTimestamp[];
  speakers?: SpeakerSegment[];
}

export interface TranscriptionChunk {
  text: string;
  isFinal: boolean;
  confidence: number;
  startTime?: number;
  endTime?: number;
}

export interface WordTimestamp {
  word: string;
  startTime: number;
  endTime: number;
  confidence: number;
}

export interface SpeakerSegment {
  speaker: string;
  startTime: number;
  endTime: number;
  text: string;
}

// Voice commands
export interface VoiceCommand {
  name: string;
  trigger: string;
  description: string;
  execute: (fullText: string) => Promise<any>;
}

export interface VoiceCommandResult {
  command: string;
  success: boolean;
  result?: any;
  error?: string;
}

// STT Service interface
export interface ISTTService {
  transcribe(audioBlob: Blob, options?: STTOptions): Promise<TranscriptionResult>;
  transcribeFile(filePath: string, options?: STTOptions): Promise<TranscriptionResult>;
  transcribeStream(audioStream: ReadableStream, options?: STTOptions): Promise<AsyncIterable<TranscriptionChunk>>;
  getSupportedLanguages(): string[];
  detectLanguage(audioBlob: Blob): Promise<string>;
}

// Audio processing
export interface AudioProcessingOptions {
  noiseReduction: boolean;
  volumeNormalization: boolean;
  formatConversion?: 'wav' | 'mp3' | 'ogg';
}

// Voice Module configuration
export interface VoiceModuleConfig {
  stt: {
    provider: 'openai' | 'google' | 'azure' | 'local';
    apiKey?: string;
    baseURL?: string;
    defaultModel?: string;
    defaultLanguage?: string;
  };
  recording: {
    defaultFormat: 'wav' | 'mp3' | 'ogg';
    defaultSampleRate: number;
    defaultChannels: number;
    maxDuration: number;
    autoStop: boolean;
    noiseReduction: boolean;
  };
  commands: {
    enabled: boolean;
    customCommands: VoiceCommand[];
  };
  processing: {
    enableNoiseReduction: boolean;
    enableVolumeNormalization: boolean;
    enableFormatConversion: boolean;
  };
}

// Main Voice Module interface
export interface IVoiceModule {
  // Recording methods
  startRecording(options?: RecordingOptions): Promise<void>;
  stopRecording(): Promise<string>;
  isRecording(): boolean;
  getRecordingState(): RecordingState;
  
  // File processing
  processAudioFile(filePath: string, options?: STTOptions): Promise<string>;
  
  // Continuous listening
  startContinuousListening(): Promise<void>;
  stopContinuousListening(): Promise<void>;
  isContinuousListening(): boolean;
  
  // Voice commands
  registerCommand(command: VoiceCommand): void;
  unregisterCommand(commandName: string): void;
  getRegisteredCommands(): VoiceCommand[];
  
  // Configuration
  updateConfig(config: Partial<VoiceModuleConfig>): void;
  getConfig(): VoiceModuleConfig;
  
  // Events
  on(event: 'transcription', listener: (result: TranscriptionResult) => void): void;
  on(event: 'command', listener: (result: VoiceCommandResult) => void): void;
  on(event: 'recording-start', listener: () => void): void;
  on(event: 'recording-stop', listener: () => void): void;
  on(event: 'error', listener: (error: Error) => void): void;
  off(event: string, listener: Function): void;
}

// STT Service configuration
export interface STTServiceConfig {
  provider: 'openai' | 'google' | 'azure' | 'local';
  apiKey: string;
  baseURL?: string;
  defaultModel: string;
  timeout?: number;
  retries?: number;
}

// Audio recorder interface
export interface IAudioRecorder {
  initialize(): Promise<void>;
  startRecording(options?: RecordingOptions): Promise<void>;
  stopRecording(): Promise<Blob>;
  getRecordingState(): RecordingState;
  isRecording(): boolean;
  cleanup(): void;
}

// Audio processor interface
export interface IAudioProcessor {
  convertFormat(inputBlob: Blob, outputFormat: 'wav' | 'mp3' | 'ogg'): Promise<Blob>;
  reduceNoise(audioBlob: Blob): Promise<Blob>;
  normalizeVolume(audioBlob: Blob): Promise<Blob>;
  processAudio(audioBlob: Blob, options: AudioProcessingOptions): Promise<Blob>;
}

// Voice command processor interface
export interface IVoiceCommandProcessor {
  registerCommand(command: VoiceCommand): void;
  unregisterCommand(commandName: string): void;
  processTranscription(text: string): Promise<VoiceCommandResult | null>;
  getRegisteredCommands(): VoiceCommand[];
}

// Error types
export class VoiceModuleError extends Error {
  constructor(message: string, public readonly cause?: Error) {
    super(message);
    this.name = 'VoiceModuleError';
  }
}

export class AudioRecordingError extends VoiceModuleError {
  constructor(message: string, cause?: Error) {
    super(message, cause);
    this.name = 'AudioRecordingError';
  }
}

export class STTServiceError extends VoiceModuleError {
  constructor(message: string, cause?: Error) {
    super(message, cause);
    this.name = 'STTServiceError';
  }
}

export class VoiceCommandError extends VoiceModuleError {
  constructor(message: string, cause?: Error) {
    super(message, cause);
    this.name = 'VoiceCommandError';
  }
}

// Event types
export type VoiceModuleEvent = 
  | 'transcription'
  | 'command'
  | 'recording-start'
  | 'recording-stop'
  | 'continuous-start'
  | 'continuous-stop'
  | 'error'
  | 'audio-level'
  | 'language-detected';

// Built-in command types
export interface BuiltInCommands {
  enhancePrompt: VoiceCommand;
  savePrompt: VoiceCommand;
  captureScreen: VoiceCommand;
  getClipboard: VoiceCommand;
  startRecording: VoiceCommand;
  stopRecording: VoiceCommand;
  clearText: VoiceCommand;
  repeatLast: VoiceCommand;
}

// Language support
export interface LanguageInfo {
  code: string;
  name: string;
  nativeName: string;
  supported: boolean;
  confidence?: number;
}

// Audio format support
export interface AudioFormatInfo {
  format: string;
  mimeType: string;
  extension: string;
  supported: boolean;
  quality: 'low' | 'medium' | 'high';
}

// Performance metrics
export interface VoiceModuleMetrics {
  totalRecordings: number;
  totalTranscriptions: number;
  totalCommands: number;
  averageTranscriptionTime: number;
  averageConfidence: number;
  errorRate: number;
  lastResetTime: Date;
}

// Streaming transcription
export interface StreamingTranscriptionOptions extends STTOptions {
  chunkSize?: number;
  bufferSize?: number;
  realTime?: boolean;
  partialResults?: boolean;
}

export interface StreamingTranscriptionResult {
  chunks: TranscriptionChunk[];
  finalText: string;
  confidence: number;
  duration: number;
}
