/**
 * Analytics Manager for Prompt Library
 * Tracks usage, generates reports, and provides insights
 */

import {
  PromptAnalytics,
  UsageHistoryEntry,
  VariableUsage,
  UsageTrend,
  ReportOptions,
  AnalyticsReport,
  CategoryAnalytics,
  Prompt,
  PromptLibraryError
} from './types';
import { getStorageManager } from '../../storage';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('AnalyticsManager');

export interface AnalyticsConfig {
  enabled: boolean;
  trackUsage: boolean;
  trackRatings: boolean;
}

/**
 * Analytics Manager implementation
 */
export class AnalyticsManager {
  private config: AnalyticsConfig;

  constructor(config: AnalyticsConfig) {
    this.config = config;
  }

  /**
   * Record prompt usage
   */
  async recordUsage(promptId: string, rating?: number, success?: boolean): Promise<void> {
    if (!this.config.enabled || !this.config.trackUsage) {
      return;
    }

    try {
      const storage = getStorageManager();
      const promptRepository = storage.prompts;
      const usageRepository = storage.system; // Using system repository for usage data

      // Update prompt usage statistics
      const prompt = await promptRepository.findById(promptId);
      if (!prompt) {
        throw new PromptLibraryError(`Prompt not found: ${promptId}`);
      }

      // Update usage count and last used date
      const updatedUsage = {
        count: prompt.usage.count + 1,
        lastUsed: new Date(),
        averageRating: rating !== undefined ? this.calculateNewAverage(
          prompt.usage.averageRating,
          prompt.usage.count,
          rating
        ) : prompt.usage.averageRating,
        successRate: success !== undefined ? this.calculateNewSuccessRate(
          prompt.usage.successRate,
          prompt.usage.count,
          success
        ) : prompt.usage.successRate
      };

      await promptRepository.update(promptId, { usage: updatedUsage as any } as any);

      // Record detailed usage entry
      const usageEntry = {
        id: `${promptId}_${Date.now()}`,
        promptId,
        timestamp: new Date(),
        rating,
        success
      };

      await usageRepository.save({
        key: `usage_${usageEntry.id}`,
        value: usageEntry,
        type: 'object',
        source: 'user',
        timestamp: new Date(),
        valid: true,
        encrypted: false
      } as any);

      logger.debug('Usage recorded', { promptId, rating, success });
    } catch (error) {
      logger.error('Failed to record usage', { error, promptId, rating, success });
      throw new PromptLibraryError('Failed to record usage', error);
    }
  }

  /**
   * Get analytics for a specific prompt
   */
  async getPromptAnalytics(promptId: string): Promise<PromptAnalytics> {
    try {
      const storage = getStorageManager();
      const promptRepository = storage.prompts;
      const usageRepository = storage.system; // Using system repository for usage data

      const prompt = await promptRepository.findById(promptId);
      if (!prompt) {
        throw new PromptLibraryError(`Prompt not found: ${promptId}`);
      }

      // Get usage history
      const usageEntries = await usageRepository.findAll({ promptId });
      const usageHistory = this.aggregateUsageHistory(usageEntries);

      // Calculate recent usage (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const recentUsage = usageEntries.filter(entry => 
        entry.timestamp >= thirtyDaysAgo
      ).length;

      // Get popular variables (if prompt has variables)
      const popularVariables = prompt.variables ? 
        await this.getPopularVariables(promptId) : [];

      return {
        promptId,
        totalUsage: prompt.usage.count,
        recentUsage,
        averageRating: prompt.usage.averageRating || 0,
        successRate: prompt.usage.successRate || 0,
        usageHistory,
        popularVariables
      };
    } catch (error) {
      logger.error('Failed to get prompt analytics', { error, promptId });
      throw new PromptLibraryError('Failed to get prompt analytics', error);
    }
  }

  /**
   * Get top prompts for a period
   */
  async getTopPrompts(period: 'week' | 'month' | 'year', limit: number): Promise<Prompt[]> {
    try {
      const storage = getStorageManager();
      const promptRepository = storage.prompts;
      const usageRepository = storage.system; // Using system repository for usage data

      // Calculate date range
      const now = new Date();
      const startDate = new Date();
      
      switch (period) {
        case 'week':
          startDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(now.getMonth() - 1);
          break;
        case 'year':
          startDate.setFullYear(now.getFullYear() - 1);
          break;
      }

      // Get usage data for the period
      const usageEntries = await usageRepository.findAll();
      const periodUsage = usageEntries.filter(entry => 
        entry.timestamp >= startDate && entry.timestamp <= now
      );

      // Count usage per prompt
      const usageCount = new Map<string, number>();
      for (const entry of periodUsage) {
        const entryData = entry.value as any;
        usageCount.set(entryData.promptId, (usageCount.get(entryData.promptId) || 0) + 1);
      }

      // Get prompts and sort by usage
      const allPrompts = await promptRepository.findAll();
      const promptsWithUsage = allPrompts
        .map(prompt => ({
          prompt,
          periodUsage: usageCount.get(prompt.id) || 0
        }))
        .sort((a, b) => b.periodUsage - a.periodUsage)
        .slice(0, limit)
        .map(item => item.prompt);

      return promptsWithUsage as any;
    } catch (error) {
      logger.error('Failed to get top prompts', { error, period, limit });
      throw new PromptLibraryError('Failed to get top prompts', error);
    }
  }

  /**
   * Get usage trends
   */
  async getUsageTrends(): Promise<UsageTrend[]> {
    try {
      const storage = getStorageManager();
      const usageRepository = storage.system; // Using system repository for usage data
      const promptRepository = storage.prompts;

      // Get last 30 days of data
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const usageEntries = await usageRepository.findAll();
      const recentUsage = usageEntries.filter(entry => entry.timestamp >= thirtyDaysAgo);

      const allPrompts = await promptRepository.findAll();
      const recentPrompts = allPrompts.filter(prompt => 
        (prompt.metadata as any).createdAt >= thirtyDaysAgo
      );

      // Group by date
      const trends = new Map<string, { usage: number; newPrompts: number }>();

      // Initialize all dates
      for (let i = 0; i < 30; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dateKey = date.toISOString().split('T')[0];
        trends.set(dateKey, { usage: 0, newPrompts: 0 });
      }

      // Count usage per day
      for (const entry of recentUsage) {
        const dateKey = entry.timestamp.toISOString().split('T')[0];
        const trend = trends.get(dateKey);
        if (trend) {
          trend.usage++;
        }
      }

      // Count new prompts per day
      for (const prompt of recentPrompts) {
        const dateKey = (prompt.metadata as any).createdAt.toISOString().split('T')[0];
        const trend = trends.get(dateKey);
        if (trend) {
          trend.newPrompts++;
        }
      }

      // Convert to array and sort by date
      return Array.from(trends.entries())
        .map(([dateStr, data]) => ({
          date: new Date(dateStr),
          usage: data.usage,
          newPrompts: data.newPrompts
        }))
        .sort((a, b) => a.date.getTime() - b.date.getTime());
    } catch (error) {
      logger.error('Failed to get usage trends', { error });
      throw new PromptLibraryError('Failed to get usage trends', error);
    }
  }

  /**
   * Generate analytics report
   */
  async generateReport(options: ReportOptions): Promise<AnalyticsReport> {
    try {
      const storage = getStorageManager();
      const promptRepository = storage.prompts;
      const categoryRepository = storage.categories;

      const allPrompts = await promptRepository.findAll();
      const allCategories = await categoryRepository.findAll();

      // Calculate total usage
      const totalUsage = allPrompts.reduce((sum, prompt) => sum + prompt.usage.count, 0);

      // Get top prompts
      const topPrompts = await this.getTopPrompts(options.period, 10);

      // Get usage trends
      const trends = await this.getUsageTrends();

      // Calculate category analytics
      const categoryAnalytics: CategoryAnalytics[] = [];
      for (const category of allCategories) {
        const categoryPrompts = allPrompts.filter(p => p.categoryId === category.id);
        const categoryUsage = categoryPrompts.reduce((sum, p) => sum + p.usage.count, 0);
        
        categoryAnalytics.push({
          category: category as any,
          promptCount: categoryPrompts.length,
          usage: categoryUsage,
          growth: 0 // TODO: Calculate growth rate
        });
      }

      const report: AnalyticsReport = {
        period: options.period,
        totalPrompts: allPrompts.length,
        totalUsage,
        topPrompts,
        trends,
        categories: categoryAnalytics
      };

      logger.info('Analytics report generated', { 
        period: options.period, 
        totalPrompts: report.totalPrompts,
        totalUsage: report.totalUsage
      });

      return report;
    } catch (error) {
      logger.error('Failed to generate report', { error, options });
      throw new PromptLibraryError('Failed to generate report', error);
    }
  }

  /**
   * Calculate new average rating
   */
  private calculateNewAverage(currentAverage: number | undefined, count: number, newRating: number): number {
    if (!currentAverage || count === 0) {
      return newRating;
    }
    
    return ((currentAverage * count) + newRating) / (count + 1);
  }

  /**
   * Calculate new success rate
   */
  private calculateNewSuccessRate(currentRate: number | undefined, count: number, success: boolean): number {
    if (!currentRate || count === 0) {
      return success ? 1.0 : 0.0;
    }
    
    const currentSuccesses = currentRate * count;
    const newSuccesses = currentSuccesses + (success ? 1 : 0);
    
    return newSuccesses / (count + 1);
  }

  /**
   * Aggregate usage history by date
   */
  private aggregateUsageHistory(usageEntries: any[]): UsageHistoryEntry[] {
    const dailyUsage = new Map<string, { count: number; ratings: number[]; successes: number; total: number }>();

    for (const entry of usageEntries) {
      const dateKey = entry.timestamp.toISOString().split('T')[0];
      
      if (!dailyUsage.has(dateKey)) {
        dailyUsage.set(dateKey, { count: 0, ratings: [], successes: 0, total: 0 });
      }
      
      const daily = dailyUsage.get(dateKey)!;
      daily.count++;
      daily.total++;
      
      if (entry.rating !== undefined) {
        daily.ratings.push(entry.rating);
      }
      
      if (entry.success === true) {
        daily.successes++;
      }
    }

    return Array.from(dailyUsage.entries())
      .map(([dateStr, data]) => ({
        date: new Date(dateStr),
        count: data.count,
        rating: data.ratings.length > 0 ? 
          data.ratings.reduce((sum, r) => sum + r, 0) / data.ratings.length : undefined,
        success: data.total > 0 ? data.successes / data.total > 0.5 : undefined
      }))
      .sort((a, b) => a.date.getTime() - b.date.getTime());
  }

  /**
   * Get popular variables for a prompt
   */
  private async getPopularVariables(promptId: string): Promise<VariableUsage[]> {
    // This would typically analyze variable usage from execution logs
    // For now, return empty array as it requires more complex tracking
    return [];
  }
}
