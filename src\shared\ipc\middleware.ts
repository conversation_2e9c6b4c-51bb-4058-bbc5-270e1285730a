/**
 * IPC Middleware
 * Common middleware functions for IPC message processing
 */

import { IPCMiddleware, IPCErrorCode } from './types';
import { logger } from '../../core/logger';

/**
 * Logging middleware - logs all IPC messages
 */
export const loggingMiddleware: IPCMiddleware = {
  name: 'logging',
  execute: async (channel: string, data: any, next: () => Promise<any>) => {
    const startTime = Date.now();
    
    logger.debug('IPC message received', { channel, data });
    
    try {
      const result = await next();
      const duration = Date.now() - startTime;
      
      logger.debug('IPC message processed successfully', { 
        channel, 
        duration,
        resultType: typeof result
      });
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      logger.error('IPC message processing failed', { 
        channel, 
        duration,
        error: error instanceof Error ? error.message : String(error)
      });
      
      throw error;
    }
  }
};

/**
 * Performance monitoring middleware
 */
export const performanceMiddleware: IPCMiddleware = {
  name: 'performance',
  execute: async (channel: string, data: any, next: () => Promise<any>) => {
    const startTime = process.hrtime.bigint();
    const startMemory = process.memoryUsage();
    
    try {
      const result = await next();
      
      const endTime = process.hrtime.bigint();
      const endMemory = process.memoryUsage();
      const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
      
      const memoryDelta = {
        rss: endMemory.rss - startMemory.rss,
        heapTotal: endMemory.heapTotal - startMemory.heapTotal,
        heapUsed: endMemory.heapUsed - startMemory.heapUsed,
        external: endMemory.external - startMemory.external
      };
      
      // Log performance metrics for slow operations
      if (duration > 1000) { // More than 1 second
        logger.warn('Slow IPC operation detected', {
          channel,
          duration,
          memoryDelta
        });
      }
      
      return result;
    } catch (error) {
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000;
      
      logger.error('IPC operation failed', {
        channel,
        duration,
        error: error instanceof Error ? error.message : String(error)
      });
      
      throw error;
    }
  }
};

/**
 * Authentication middleware - validates user permissions
 */
export const authenticationMiddleware: IPCMiddleware = {
  name: 'authentication',
  execute: async (channel: string, data: any, next: () => Promise<any>) => {
    // Check if channel requires authentication
    const protectedChannels = [
      'settings:set',
      'settings:reset',
      'system:hotkey-register',
      'system:hotkey-unregister',
      'file:write'
    ];
    
    if (protectedChannels.includes(channel)) {
      // In a real implementation, you would validate user session/token here
      // For now, we'll just log the attempt
      logger.info('Protected channel access attempt', { channel });
      
      // You could throw an error here if authentication fails
      // throw new Error('Authentication required');
    }
    
    return next();
  }
};

/**
 * Rate limiting middleware
 */
export const rateLimitingMiddleware: IPCMiddleware = {
  name: 'rate-limiting',
  execute: async (channel: string, data: any, next: () => Promise<any>) => {
    // This would typically use a more sophisticated rate limiting algorithm
    // For now, we'll just track and log high-frequency channels
    
    const highFrequencyChannels = [
      'voice:start-recording',
      'voice:stop-recording',
      'context:capture-screen',
      'ai:generate'
    ];
    
    if (highFrequencyChannels.includes(channel)) {
      logger.debug('High-frequency channel accessed', { channel });
      
      // In a real implementation, you would check rate limits here
      // and potentially throw an error if limits are exceeded
    }
    
    return next();
  }
};

/**
 * Data validation middleware
 */
export const validationMiddleware: IPCMiddleware = {
  name: 'validation',
  execute: async (channel: string, data: any, next: () => Promise<any>) => {
    // Define validation rules for specific channels
    const validationRules: Record<string, (data: any) => boolean> = {
      'prompt:save': (data) => {
        return data && typeof data.title === 'string' && typeof data.content === 'string';
      },
      'category:save': (data) => {
        return data && typeof data.name === 'string';
      },
      'settings:set': (data) => {
        return data && typeof data.key === 'string' && data.value !== undefined;
      },
      'user:update-preferences': (data) => {
        return data && typeof data === 'object';
      }
    };
    
    const validator = validationRules[channel];
    if (validator && !validator(data)) {
      const error = new Error(`Invalid data for channel: ${channel}`);
      (error as any).code = IPCErrorCode.VALIDATION_FAILED;
      throw error;
    }
    
    return next();
  }
};

/**
 * Error handling middleware
 */
export const errorHandlingMiddleware: IPCMiddleware = {
  name: 'error-handling',
  execute: async (channel: string, data: any, next: () => Promise<any>) => {
    try {
      return await next();
    } catch (error) {
      // Transform and standardize errors
      if (error instanceof Error) {
        // Add additional context to errors
        const enhancedError = new Error(error.message);
        (enhancedError as any).code = (error as any).code || IPCErrorCode.INTERNAL_ERROR;
        (enhancedError as any).channel = channel;
        (enhancedError as any).timestamp = new Date().toISOString();
        (enhancedError as any).originalStack = error.stack;
        
        throw enhancedError;
      }
      
      // Handle non-Error objects
      const standardError = new Error(String(error));
      (standardError as any).code = IPCErrorCode.INTERNAL_ERROR;
      (standardError as any).channel = channel;
      (standardError as any).timestamp = new Date().toISOString();
      
      throw standardError;
    }
  }
};

/**
 * Caching middleware - caches responses for specific channels
 */
export const cachingMiddleware: IPCMiddleware = {
  name: 'caching',
  execute: async (channel: string, data: any, next: () => Promise<any>) => {
    // Define cacheable channels
    const cacheableChannels = [
      'system:get-info',
      'ai:providers-list',
      'ai:models-list',
      'user:get-statistics'
    ];
    
    if (!cacheableChannels.includes(channel)) {
      return next();
    }
    
    // Simple in-memory cache (in production, you might use Redis or similar)
    const cacheKey = `${channel}:${JSON.stringify(data)}`;
    const cached = (cachingMiddleware as any).cache?.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < 60000) { // 1 minute cache
      logger.debug('Returning cached result', { channel, cacheKey });
      return cached.result;
    }
    
    const result = await next();
    
    // Store in cache
    if (!(cachingMiddleware as any).cache) {
      (cachingMiddleware as any).cache = new Map();
    }
    
    (cachingMiddleware as any).cache.set(cacheKey, {
      result,
      timestamp: Date.now()
    });
    
    logger.debug('Result cached', { channel, cacheKey });
    
    return result;
  }
};

/**
 * Get all default middleware in recommended order
 */
export function getDefaultMiddleware(): IPCMiddleware[] {
  return [
    loggingMiddleware,
    performanceMiddleware,
    authenticationMiddleware,
    rateLimitingMiddleware,
    validationMiddleware,
    cachingMiddleware,
    errorHandlingMiddleware
  ];
}

/**
 * Create custom middleware
 */
export function createMiddleware(
  name: string,
  execute: (channel: string, data: any, next: () => Promise<any>) => Promise<any>
): IPCMiddleware {
  return { name, execute };
}
