/**
 * Error handling module exports
 * Centralized error handling with classification, recovery, and reporting
 */

// Core classes
export { Error<PERSON><PERSON><PERSON>, getErrorHandler, initializeErrorHandler, ErrorBoundary, RetryRecoveryHandler } from './ErrorHandler';
export {
  ApplicationError,
  ValidationError,
  NetworkError,
  FileSystemError,
  DatabaseError,
  AIProviderError,
  STTProviderError,
  ConfigurationError,
  BusinessLogicError,
  SystemError,
  ErrorClassifier
} from './ErrorTypes';

// Import for local use
import { getErrorHandler, initializeErrorHandler, ErrorBoundary } from './ErrorHandler';
import {
  ApplicationError,
  ValidationError,
  NetworkError,
  FileSystemError,
  DatabaseError,
  AIProviderError,
  STTProviderError,
  ConfigurationError,
  BusinessLogicError,
  SystemError,
  ErrorClassifier
} from './ErrorTypes';
export {
  ConsoleErrorReporter,
  FileErrorReporter,
  RemoteErrorReporter,
  ErrorAnalyticsTracker,
  ErrorNotificationManager
} from './ErrorReporter';

// Import reporters for local use
import { ConsoleErrorReporter, FileErrorReporter } from './ErrorReporter';
import { getFileSystemManager } from '../filesystem';

// Types and interfaces
export * from './types';

// Convenience functions
export const handleError = async (error: Error | any, context?: any): Promise<void> => {
  return getErrorHandler().handle(error, context);
};

export const createError = (details: any, context?: any, cause?: Error): any => {
  return getErrorHandler().createError(details, context, cause);
};

export const createValidationError = (message: string, field?: string, value?: any, context?: any): ValidationError => {
  return new ValidationError(message, field, value, context);
};

export const createNetworkError = (message: string, statusCode?: number, endpoint?: string, context?: any): NetworkError => {
  return new NetworkError(message, statusCode, endpoint, context);
};

export const createFileSystemError = (message: string, path?: string, operation?: string, context?: any): FileSystemError => {
  return new FileSystemError(message, path, operation, context);
};

export const createDatabaseError = (message: string, query?: string, context?: any): DatabaseError => {
  return new DatabaseError(message, query, context);
};

export const createAIProviderError = (message: string, provider?: string, model?: string, context?: any): AIProviderError => {
  return new AIProviderError(message, provider, model, context);
};

export const createSTTProviderError = (message: string, provider?: string, language?: string, context?: any): STTProviderError => {
  return new STTProviderError(message, provider, language, context);
};

export const createConfigurationError = (message: string, configKey?: string, context?: any): ConfigurationError => {
  return new ConfigurationError(message, configKey, context);
};

export const createBusinessLogicError = (message: string, rule?: string, context?: any): BusinessLogicError => {
  return new BusinessLogicError(message, rule, context);
};

export const createSystemError = (message: string, systemInfo?: any, context?: any): SystemError => {
  return new SystemError(message, systemInfo, context);
};

// Error boundary factory
export const createErrorBoundary = (componentName: string, options?: any): ErrorBoundary => {
  return new ErrorBoundary(componentName, getErrorHandler(), options);
};

// Utility functions
export const isAppError = (error: any): boolean => {
  return error instanceof ApplicationError;
};

export const isRecoverableError = (error: Error): boolean => {
  return ErrorClassifier.isRecoverable(error);
};

export const classifyError = (error: Error): { category: any; severity: any } => {
  return ErrorClassifier.classify(error);
};

// Error handling decorators
export const withErrorHandling = (componentName: string) => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      try {
        return await originalMethod.apply(this, args);
      } catch (error) {
        await handleError(error, {
          component: componentName,
          operation: propertyKey,
          metadata: { args }
        });
        throw error;
      }
    };
    
    return descriptor;
  };
};

export const withErrorBoundary = (componentName: string, options?: any) => {
  return (target: any) => {
    const boundary = createErrorBoundary(componentName, options);
    
    // Wrap constructor
    const originalConstructor = target;
    const wrappedConstructor = function (...args: any[]) {
      try {
        return new originalConstructor(...args);
      } catch (error) {
        boundary.catch(error);
        throw error;
      }
    };
    
    // Copy prototype
    wrappedConstructor.prototype = originalConstructor.prototype;
    
    return wrappedConstructor;
  };
};

// Global error handlers
export const setupGlobalErrorHandling = (config?: any): void => {
  const errorHandler = initializeErrorHandler(config);

  // Set up global process listeners
  errorHandler.setupGlobalListeners();

  // Set up file reporter if filesystem is available
  try {
    const { getFileSystemManager } = require('../filesystem');
    const fileReporter = new FileErrorReporter(getFileSystemManager());
    errorHandler.setReporter(fileReporter);
  } catch {
    // Fallback to console reporter
    errorHandler.setReporter(new ConsoleErrorReporter());
  }
};

// Error recovery helpers
export const retryOperation = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxRetries) {
        throw lastError;
      }
      
      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }
  
  throw lastError!;
};

export const withRetry = <T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  maxRetries: number = 3,
  delay: number = 1000
) => {
  return async (...args: T): Promise<R> => {
    return retryOperation(() => fn(...args), maxRetries, delay);
  };
};
