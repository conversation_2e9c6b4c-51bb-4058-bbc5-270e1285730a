/**
 * Linux Platform Adapter
 * Handles Linux-specific hotkey registration using X11 APIs
 */

import { IPlatformAdapter, LinuxHotkeyData } from './types';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('LinuxPlatformAdapter');

/**
 * Linux Platform Adapter implementation
 */
export class LinuxPlatformAdapter implements IPlatformAdapter {
  private registeredKeys: Map<string, LinuxHotkeyData> = new Map();
  private hotkeyCallbacks: Map<string, () => void> = new Map();

  /**
   * Register global hotkey
   */
  async registerGlobalHotkey(accelerator: string, callback: () => void): Promise<boolean> {
    try {
      const hotkeyId = `hotkey_${Date.now()}_${Math.random()}`;
      const hotkeyData: LinuxHotkeyData = { id: hotkeyId };

      // In a real implementation, this would use X11 APIs
      const success = await this.registerLinuxHotkey(accelerator, hotkeyData);
      
      if (success) {
        this.registeredKeys.set(accelerator, hotkeyData);
        this.hotkeyCallbacks.set(hotkeyId, callback);
        
        logger.info('Linux hotkey registered', { accelerator, hotkeyId });
        return true;
      }

      return false;
    } catch (error) {
      logger.error('Failed to register Linux hotkey', { error, accelerator });
      return false;
    }
  }

  /**
   * Unregister global hotkey
   */
  async unregisterGlobalHotkey(accelerator: string): Promise<boolean> {
    try {
      const hotkeyData = this.registeredKeys.get(accelerator);
      if (!hotkeyData) {
        return false;
      }

      const success = await this.unregisterLinuxHotkey(hotkeyData);
      
      if (success) {
        this.registeredKeys.delete(accelerator);
        this.hotkeyCallbacks.delete(hotkeyData.id);
        
        logger.info('Linux hotkey unregistered', { accelerator, hotkeyId: hotkeyData.id });
        return true;
      }

      return false;
    } catch (error) {
      logger.error('Failed to unregister Linux hotkey', { error, accelerator });
      return false;
    }
  }

  /**
   * Check for hotkey conflicts
   */
  async isHotkeyConflict(accelerator: string): Promise<boolean> {
    return this.registeredKeys.has(accelerator);
  }

  /**
   * Get supported modifiers
   */
  getSupportedModifiers(): string[] {
    return ['Ctrl', 'Alt', 'Shift', 'Super'];
  }

  /**
   * Get supported keys
   */
  getSupportedKeys(): string[] {
    return [
      'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',
      'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
      '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
      'F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12',
      'Space', 'Return', 'Escape', 'Tab', 'BackSpace', 'Delete',
      'Left', 'Up', 'Right', 'Down'
    ];
  }

  /**
   * Normalize accelerator for Linux
   */
  normalizeAccelerator(accelerator: string): string {
    return accelerator
      .split('+')
      .map(part => {
        const trimmed = part.trim();
        // Normalize modifier names for Linux
        switch (trimmed.toLowerCase()) {
          case 'control':
          case 'ctrl':
            return 'Ctrl';
          case 'alt':
            return 'Alt';
          case 'shift':
            return 'Shift';
          case 'super':
          case 'meta':
          case 'cmd':
            return 'Super';
          case 'enter':
            return 'Return';
          case 'backspace':
            return 'BackSpace';
          default:
            return trimmed.charAt(0).toUpperCase() + trimmed.slice(1).toLowerCase();
        }
      })
      .join('+');
  }

  /**
   * Register hotkey with Linux X11 APIs (placeholder)
   */
  private async registerLinuxHotkey(accelerator: string, hotkeyData: LinuxHotkeyData): Promise<boolean> {
    try {
      // In a real implementation, this would use:
      // - X11's XGrabKey function
      // - or libxkbcommon for modern Wayland
      // - or a native Node.js addon
      // - or Electron's globalShortcut API if available
      
      logger.debug('Simulating Linux hotkey registration', { accelerator, hotkeyData });
      
      // Simulate registration
      return true;
    } catch (error) {
      logger.error('Linux hotkey registration failed', { error, accelerator });
      return false;
    }
  }

  /**
   * Unregister hotkey with Linux X11 APIs (placeholder)
   */
  private async unregisterLinuxHotkey(hotkeyData: LinuxHotkeyData): Promise<boolean> {
    try {
      // In a real implementation, this would use:
      // - X11's XUngrabKey function
      // - or libxkbcommon for modern Wayland
      // - or a native Node.js addon
      
      logger.debug('Simulating Linux hotkey unregistration', { hotkeyData });
      return true;
    } catch (error) {
      logger.error('Linux hotkey unregistration failed', { error, hotkeyData });
      return false;
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    try {
      const accelerators = Array.from(this.registeredKeys.keys());
      for (const accelerator of accelerators) {
        await this.unregisterGlobalHotkey(accelerator);
      }

      this.registeredKeys.clear();
      this.hotkeyCallbacks.clear();
      
      logger.info('Linux platform adapter cleaned up');
    } catch (error) {
      logger.error('Error during Linux platform adapter cleanup', { error });
    }
  }
}
