/**
 * Context Extractor module type definitions
 * Screen capture, OCR, clipboard monitoring, and context gathering capabilities
 */

// Core interfaces
export interface IContextExtractor {
  captureScreen(options?: ScreenCaptureOptions): Promise<CaptureResult>;
  captureRegion(region: ScreenRegion, options?: ScreenCaptureOptions): Promise<CaptureResult>;
  captureActiveWindow(): Promise<CaptureResult>;
  getClipboardText(): Promise<string>;
  getActiveWindowText(): Promise<string>;
  getActiveWindowInfo(): Promise<WindowInfo>;
  getSelectedText(): Promise<string>;
  getSystemInfo(): Promise<SystemInfo>;
  extractFromFile(filePath: string): Promise<string>;
  extractFromUrl(url: string): Promise<string>;
}

// Screen capture types
export interface ScreenRegion {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface ScreenCaptureOptions {
  region?: ScreenRegion;
  format?: 'png' | 'jpg' | 'webp';
  quality?: number;
  includeOCR?: boolean;
  ocrLanguage?: string;
  saveToFile?: boolean;
  filename?: string;
}

export interface CaptureResult {
  imagePath?: string;
  imageBuffer?: Buffer;
  extractedText?: string;
  metadata: CaptureMetadata;
}

export interface CaptureMetadata {
  timestamp: Date;
  region: ScreenRegion;
  displayInfo: DisplayInfo;
  ocrLanguage?: string;
  confidence?: number;
}

export interface DisplayInfo {
  displays: DisplayData[];
  primaryDisplay: number;
  totalWidth: number;
  totalHeight: number;
}

export interface DisplayData {
  id: number;
  bounds: ScreenRegion;
  workArea: ScreenRegion;
  scaleFactor: number;
  isPrimary: boolean;
}

// OCR types
export interface IOCREngine {
  extractText(imagePath: string, options?: OCROptions): Promise<string>;
  extractTextFromBuffer(imageBuffer: Buffer, options?: OCROptions): Promise<string>;
  getSupportedLanguages(): string[];
  detectLanguage(imagePath: string): Promise<string>;
  isAvailable(): Promise<boolean>;
}

export interface OCROptions {
  language?: string;
  pageSegMode?: number;
  ocrEngineMode?: number;
  whitelist?: string;
  blacklist?: string;
  preserveInterwordSpaces?: boolean;
  confidence?: number;
}

export interface OCRResult {
  text: string;
  confidence: number;
  language: string;
  words?: OCRWord[];
  lines?: OCRLine[];
}

export interface OCRWord {
  text: string;
  confidence: number;
  bbox: BoundingBox;
}

export interface OCRLine {
  text: string;
  confidence: number;
  bbox: BoundingBox;
  words: OCRWord[];
}

export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

// Clipboard monitoring
export interface IClipboardMonitor {
  startMonitoring(intervalMs?: number): void;
  stopMonitoring(): void;
  getCurrentContent(): string;
  getContentHistory(): ClipboardEntry[];
  onContentChange(listener: (content: string) => void): () => void;
  clearHistory(): void;
}

export interface ClipboardEntry {
  content: string;
  timestamp: Date;
  type: 'text' | 'image' | 'file' | 'html';
  source?: string;
}

// Window management
export interface WindowInfo {
  title: string;
  application: string;
  processId: number;
  bounds: ScreenRegion;
  isVisible: boolean;
  isActive: boolean;
  url?: string;
  text?: string;
}

export interface IWindowManager {
  getActiveWindow(): Promise<WindowInfo | null>;
  getAllWindows(): Promise<WindowInfo[]>;
  getWindowText(windowId: number): Promise<string>;
  getWindowInfo(windowId: number): Promise<WindowInfo | null>;
}

// File reading
export interface IFileReader {
  readFile(filePath: string): Promise<string>;
  readMultipleFiles(filePaths: string[]): Promise<Record<string, string>>;
  getSupportedExtensions(): string[];
  isSupported(filePath: string): boolean;
}

export interface FileReadResult {
  content: string;
  metadata: FileMetadata;
}

export interface FileMetadata {
  path: string;
  size: number;
  extension: string;
  mimeType: string;
  encoding: string;
  lastModified: Date;
}

// Web content extraction
export interface IWebExtractor {
  extractFromUrl(url: string): Promise<WebContent>;
  extractFromHtml(html: string): Promise<WebContent>;
  extractFromActiveTab(): Promise<WebContent>;
}

export interface WebContent {
  title: string;
  text: string;
  url: string;
  metadata: WebMetadata;
}

export interface WebMetadata {
  description?: string;
  keywords?: string[];
  author?: string;
  publishedDate?: Date;
  language?: string;
  images?: string[];
  links?: string[];
}

// System information
export interface SystemInfo {
  platform: string;
  version: string;
  arch: string;
  hostname: string;
  username: string;
  currentTime: Date;
  timezone: string;
  locale: string;
  memory: MemoryInfo;
  cpu: CPUInfo;
}

export interface MemoryInfo {
  total: number;
  free: number;
  used: number;
  percentage: number;
}

export interface CPUInfo {
  model: string;
  cores: number;
  usage: number;
}

// Configuration
export interface ContextExtractorConfig {
  ocr: {
    engine: 'tesseract' | 'google' | 'azure' | 'aws';
    defaultLanguage: string;
    confidence: number;
    enableCache: boolean;
  };
  clipboard: {
    monitoringEnabled: boolean;
    monitoringInterval: number;
    historySize: number;
    enableHistory: boolean;
  };
  screen: {
    defaultFormat: 'png' | 'jpg' | 'webp';
    defaultQuality: number;
    tempDirectory: string;
    autoCleanup: boolean;
  };
  files: {
    maxFileSize: number;
    supportedExtensions: string[];
    encoding: string;
  };
  web: {
    timeout: number;
    userAgent: string;
    enableJavaScript: boolean;
  };
}

// Context processing
export interface ContextProcessor {
  cleanText(text: string): string;
  detectLanguage(text: string): string;
  summarizeText(text: string, maxLength?: number): string;
  extractKeywords(text: string): string[];
  formatContent(content: string, type: ContentType): string;
}

export type ContentType = 'text' | 'code' | 'json' | 'csv' | 'html' | 'markdown';

// Analytics and metrics
export interface ContextExtractorMetrics {
  totalCaptures: number;
  totalOCROperations: number;
  totalClipboardReads: number;
  totalFileReads: number;
  averageOCRTime: number;
  averageCaptureTime: number;
  ocrSuccessRate: number;
  lastResetTime: Date;
}

// Error types
export class ContextExtractorError extends Error {
  constructor(message: string, public readonly cause?: Error) {
    super(message);
    this.name = 'ContextExtractorError';
  }
}

export class ScreenCaptureError extends ContextExtractorError {
  constructor(message: string, cause?: Error) {
    super(message, cause);
    this.name = 'ScreenCaptureError';
  }
}

export class OCRError extends ContextExtractorError {
  constructor(message: string, cause?: Error) {
    super(message, cause);
    this.name = 'OCRError';
  }
}

export class FileReadError extends ContextExtractorError {
  constructor(message: string, cause?: Error) {
    super(message, cause);
    this.name = 'FileReadError';
  }
}

export class WebExtractionError extends ContextExtractorError {
  constructor(message: string, cause?: Error) {
    super(message, cause);
    this.name = 'WebExtractionError';
  }
}

// Event types
export type ContextExtractorEvent = 
  | 'capture-complete'
  | 'ocr-complete'
  | 'clipboard-change'
  | 'file-read'
  | 'error';

export interface ContextExtractorEventData {
  type: ContextExtractorEvent;
  data?: any;
  error?: Error;
  timestamp: Date;
}

// Cache types
export interface ContextCache {
  get(key: string): any;
  set(key: string, value: any, ttl?: number): void;
  has(key: string): boolean;
  delete(key: string): boolean;
  clear(): void;
  size(): number;
}

// Language detection
export interface LanguageDetector {
  detect(text: string): string;
  detectWithConfidence(text: string): { language: string; confidence: number };
  getSupportedLanguages(): string[];
}

// Text processing
export interface TextProcessor {
  clean(text: string): string;
  normalize(text: string): string;
  extractSentences(text: string): string[];
  extractParagraphs(text: string): string[];
  removeFormatting(text: string): string;
  preserveStructure(text: string): string;
}
