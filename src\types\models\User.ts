/**
 * User and session model definitions
 * User management, preferences, and session tracking models
 */

// Theme and appearance types
export type ThemeMode = 'light' | 'dark' | 'system' | 'auto';
export type FontSize = 'small' | 'medium' | 'large' | 'extra-large';
export type NotificationPosition = 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
export type NotificationType = 'success' | 'error' | 'warning' | 'info' | 'update';

// Subscription types
export type SubscriptionPlan = 'free' | 'basic' | 'pro' | 'enterprise';
export type SubscriptionStatus = 'active' | 'inactive' | 'cancelled' | 'expired' | 'trial';

// Activity types
export type ActivityType = 
  | 'prompt_created'
  | 'prompt_used'
  | 'prompt_enhanced'
  | 'voice_command'
  | 'screen_capture'
  | 'search_performed'
  | 'settings_changed'
  | 'error_occurred';

// Appearance preferences
export interface AppearancePreferences {
  readonly theme: ThemeMode;
  readonly language: string;
  readonly fontSize: FontSize;
  readonly colorScheme?: string;
  readonly compactMode: boolean;
  readonly animations: boolean;
}

// Behavior preferences
export interface BehaviorPreferences {
  readonly autoSave: boolean;
  readonly autoBackup: boolean;
  readonly confirmDeletions: boolean;
  readonly rememberWindowState: boolean;
  readonly startMinimized: boolean;
  readonly checkUpdates: boolean;
}

// Privacy preferences
export interface PrivacyPreferences {
  readonly analytics: boolean;
  readonly crashReports: boolean;
  readonly usageStatistics: boolean;
  readonly dataSharing: boolean;
  readonly anonymizeData: boolean;
  readonly retentionPeriod: number; // days
}

// AI preferences
export interface AIPreferences {
  readonly defaultProvider: string;
  readonly defaultModel: string;
  readonly temperature: number;
  readonly maxTokens: number;
  readonly autoEnhance: boolean;
  readonly contextInjection: boolean;
  readonly costWarnings: boolean;
  readonly customInstructions?: string;
}

// Voice preferences
export interface VoicePreferences {
  readonly defaultLanguage: string;
  readonly provider: string;
  readonly sensitivity: number;
  readonly noiseReduction: boolean;
  readonly autoStop: boolean;
  readonly maxDuration: number; // seconds
  readonly commands: boolean;
}

// Hotkey preferences
export interface HotkeyPreferences {
  readonly showFloatingWindow: string;
  readonly captureScreen: string;
  readonly startVoiceInput: string;
  readonly quickSearch: string;
  readonly enhanceClipboard: string;
  readonly customHotkeys: Readonly<Record<string, string>>;
}

// Notification preferences
export interface NotificationPreferences {
  readonly enabled: boolean;
  readonly sound: boolean;
  readonly desktop: boolean;
  readonly duration: number;
  readonly position: NotificationPosition;
  readonly types: readonly NotificationType[];
}

// User preferences
export interface UserPreferences {
  readonly appearance: AppearancePreferences;
  readonly behavior: BehaviorPreferences;
  readonly privacy: PrivacyPreferences;
  readonly ai: AIPreferences;
  readonly voice: VoicePreferences;
  readonly hotkeys: HotkeyPreferences;
  readonly notifications: NotificationPreferences;
}

// Subscription limits
export interface SubscriptionLimits {
  readonly maxPrompts: number;
  readonly maxTokensPerMonth: number;
  readonly maxVoiceMinutesPerMonth: number;
  readonly maxStorageGB: number;
  readonly aiProviders: readonly string[];
  readonly advancedFeatures: boolean;
}

// Billing information
export interface BillingInfo {
  readonly customerId?: string;
  readonly subscriptionId?: string;
  readonly nextBillingDate?: Date;
  readonly amount: number;
  readonly currency: string;
  readonly interval: 'monthly' | 'yearly';
}

// Subscription model
export interface Subscription {
  readonly id: string;
  readonly plan: SubscriptionPlan;
  readonly status: SubscriptionStatus;
  readonly startDate: Date;
  readonly endDate?: Date;
  readonly features: readonly string[];
  readonly limits: SubscriptionLimits;
  readonly billing: BillingInfo;
}

// User metadata
export interface UserMetadata {
  readonly createdAt: Date;
  readonly lastLoginAt?: Date;
  readonly lastActiveAt?: Date;
  readonly loginCount: number;
  readonly appVersion: string;
  readonly platform: string;
  readonly timezone: string;
  readonly locale: string;
}

// User model
export interface User {
  readonly id: string;
  readonly username?: string;
  readonly email?: string;
  readonly displayName?: string;
  readonly avatar?: string;
  readonly preferences: UserPreferences;
  readonly subscription?: Subscription;
  readonly metadata: UserMetadata;
  
  // Computed properties
  readonly isActive: boolean;
  readonly membershipDuration: number; // days
  readonly totalPrompts: number;
}

// Memory usage tracking
export interface MemoryUsage {
  readonly rss: number;
  readonly heapTotal: number;
  readonly heapUsed: number;
  readonly external: number;
}

// Performance metrics
export interface PerformanceMetrics {
  readonly startupTime: number;
  readonly averageResponseTime: number;
  readonly peakMemoryUsage: number;
  readonly cpuUsage: number;
}

// Session metadata
export interface SessionMetadata {
  readonly appVersion: string;
  readonly platform: string;
  readonly osVersion: string;
  readonly screenResolution: string;
  readonly memoryUsage: MemoryUsage;
  readonly performanceMetrics: PerformanceMetrics;
}

// Session activity
export interface SessionActivity {
  readonly id: string;
  readonly type: ActivityType;
  readonly timestamp: Date;
  readonly data: Readonly<Record<string, unknown>>;
  readonly duration?: number;
  readonly success?: boolean;
}

// Session model
export interface Session {
  readonly id: string;
  readonly userId?: string;
  readonly startedAt: Date;
  endedAt?: Date;
  readonly activities: readonly SessionActivity[];
  readonly metadata: SessionMetadata;
  
  // Computed properties
  readonly duration: number; // milliseconds
  readonly isActive: boolean;
  readonly promptsUsed: number;
  readonly errorsEncountered: number;
}

// User creation/update DTOs
export interface CreateUserRequest {
  username?: string;
  email?: string;
  displayName?: string;
  preferences?: Partial<UserPreferences>;
}

export interface UpdateUserRequest {
  username?: string;
  email?: string;
  displayName?: string;
  avatar?: string;
  preferences?: Partial<UserPreferences>;
}

// Session operations
export interface CreateSessionRequest {
  userId?: string;
  metadata?: Partial<SessionMetadata>;
}

export interface UpdateSessionRequest {
  endedAt?: Date;
  metadata?: Partial<SessionMetadata>;
}

// User analytics
export interface UserAnalytics {
  readonly userId: string;
  readonly totalSessions: number;
  readonly totalDuration: number;
  readonly averageSessionDuration: number;
  readonly promptsCreated: number;
  readonly promptsUsed: number;
  readonly favoriteCategories: readonly string[];
  readonly mostUsedFeatures: readonly string[];
  readonly activityTrend: readonly {
    date: Date;
    sessions: number;
    duration: number;
    prompts: number;
  }[];
}
