/**
 * Settings repository implementation
 * Handles application settings with type-safe value handling
 */

import { BaseRepository } from '../BaseRepository';
import { DatabaseConnection, SettingsEntity } from '../types';

/**
 * Settings value types
 */
export type SettingValue = string | number | boolean | object;

/**
 * Settings interface
 */
export interface Setting {
  key: string;
  value: SettingValue;
  valueType: 'string' | 'number' | 'boolean' | 'json';
  category: string;
  description?: string;
  isSensitive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Repository for settings
 */
export class SettingsRepository extends BaseRepository<Setting> {
  constructor(db: DatabaseConnection) {
    super(db, 'settings');
  }

  /**
   * Get setting value by key
   */
  async getValue<T = SettingValue>(key: string): Promise<T | null> {
    const setting = await this.findById(key);
    if (!setting) {
      return null;
    }
    return setting.value as T;
  }

  /**
   * Set setting value
   */
  async setValue(key: string, value: SettingValue, category: string = 'general', description?: string): Promise<void> {
    const valueType = this.getValueType(value);
    const serializedValue = this.serializeSettingValue(value);

    const existing = await this.findById(key);
    
    if (existing) {
      const updateData: Partial<Setting> = {
        value: serializedValue,
        valueType,
        category
      };
      if (description) {
        updateData.description = description;
      } else if (existing.description) {
        updateData.description = existing.description;
      }
      await this.update(key, updateData);
    } else {
      const newSetting: Setting = {
        key,
        value: serializedValue,
        valueType,
        category,
        isSensitive: false,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      if (description) {
        newSetting.description = description;
      }
      await this.save(newSetting);
    }
  }

  /**
   * Get settings by category
   */
  async getByCategory(category: string): Promise<Setting[]> {
    return this.findAll({ category });
  }

  /**
   * Get all categories
   */
  async getCategories(): Promise<string[]> {
    const sql = `SELECT DISTINCT category FROM ${this.tableName} ORDER BY category`;
    const rows = await this.query<{ category: string }>(sql);
    return rows.map(row => row.category);
  }

  /**
   * Get sensitive settings
   */
  async getSensitiveSettings(): Promise<Setting[]> {
    return this.findAll({ isSensitive: true });
  }

  /**
   * Mark setting as sensitive
   */
  async markAsSensitive(key: string, sensitive: boolean = true): Promise<void> {
    await this.update(key, { isSensitive: sensitive });
  }

  /**
   * Get settings as key-value map
   */
  async getAsMap(category?: string): Promise<Record<string, SettingValue>> {
    const settings = category ? await this.getByCategory(category) : await this.findAll();
    const map: Record<string, SettingValue> = {};
    
    for (const setting of settings) {
      map[setting.key] = setting.value;
    }
    
    return map;
  }

  /**
   * Bulk update settings
   */
  async bulkUpdate(settings: Record<string, SettingValue>, category: string = 'general'): Promise<void> {
    await this.db.transaction(async () => {
      for (const [key, value] of Object.entries(settings)) {
        await this.setValue(key, value, category);
      }
    });
  }

  /**
   * Reset settings to defaults
   */
  async resetToDefaults(category?: string): Promise<void> {
    const defaults = this.getDefaultSettings();
    
    if (category) {
      const categoryDefaults = defaults.filter(setting => setting.category === category);
      for (const setting of categoryDefaults) {
        await this.setValue(setting.key, setting.value, setting.category, setting.description);
      }
    } else {
      await this.db.transaction(async () => {
        // Clear all settings
        await this.db.exec(`DELETE FROM ${this.tableName}`);
        
        // Insert defaults
        for (const setting of defaults) {
          await this.save(setting);
        }
      });
    }
  }

  /**
   * Export settings
   */
  async exportSettings(includeDefaults: boolean = false): Promise<Record<string, any>> {
    const settings = await this.findAll();
    const exported: Record<string, any> = {};
    
    for (const setting of settings) {
      if (!includeDefaults && this.isDefaultSetting(setting.key)) {
        continue;
      }
      
      if (!exported[setting.category]) {
        exported[setting.category] = {};
      }
      
      exported[setting.category][setting.key] = {
        value: setting.value,
        type: setting.valueType,
        description: setting.description
      };
    }
    
    return exported;
  }

  /**
   * Import settings
   */
  async importSettings(settingsData: Record<string, any>, overwrite: boolean = false): Promise<void> {
    await this.db.transaction(async () => {
      for (const [category, categorySettings] of Object.entries(settingsData)) {
        for (const [key, settingData] of Object.entries(categorySettings as Record<string, any>)) {
          const existing = await this.findById(key);
          
          if (existing && !overwrite) {
            continue;
          }
          
          await this.setValue(
            key,
            settingData.value,
            category,
            settingData.description
          );
        }
      }
    });
  }

  /**
   * Get setting statistics
   */
  async getStatistics(): Promise<{
    total: number;
    byCategory: Record<string, number>;
    sensitive: number;
    byType: Record<string, number>;
  }> {
    const [total, sensitive] = await Promise.all([
      this.count(),
      this.count({ isSensitive: true })
    ]);

    const byCategoryRows = await this.query<{ category: string; count: number }>(
      `SELECT category, COUNT(*) as count FROM ${this.tableName} GROUP BY category`
    );

    const byTypeRows = await this.query<{ value_type: string; count: number }>(
      `SELECT value_type, COUNT(*) as count FROM ${this.tableName} GROUP BY value_type`
    );

    const byCategory: Record<string, number> = {};
    byCategoryRows.forEach(row => {
      byCategory[row.category] = row.count;
    });

    const byType: Record<string, number> = {};
    byTypeRows.forEach(row => {
      byType[row.value_type] = row.count;
    });

    return {
      total,
      byCategory,
      sensitive,
      byType
    };
  }

  /**
   * Map database row to Setting entity
   */
  protected mapRowToEntity(row: any): Setting {
    return {
      key: row.key,
      value: this.deserializeSettingValue(row.value, row.value_type),
      valueType: row.value_type,
      category: row.category,
      description: row.description,
      isSensitive: this.deserializeValue(row.is_sensitive, 'boolean'),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }

  /**
   * Override save to handle settings-specific fields
   */
  async save(entity: Setting): Promise<string> {
    const settingsEntity: SettingsEntity = {
      key: entity.key,
      value: this.serializeSettingValue(entity.value),
      valueType: entity.valueType,
      category: entity.category,
      isSensitive: entity.isSensitive,
      createdAt: entity.createdAt || new Date(),
      updatedAt: entity.updatedAt || new Date(),
      ...(entity.description && { description: entity.description })
    };

    const columns = Object.keys(settingsEntity).filter(key => settingsEntity[key as keyof SettingsEntity] !== undefined);
    const placeholders = columns.map(() => '?').join(', ');
    const values = columns.map(col => this.serializeValue(settingsEntity[col as keyof SettingsEntity]));

    const sql = `INSERT OR REPLACE INTO ${this.tableName} (${columns.map(col => this.camelToSnake(col)).join(', ')}) VALUES (${placeholders})`;

    await this.db.run(sql, values);
    return entity.key;
  }

  /**
   * Get value type from value
   */
  private getValueType(value: SettingValue): 'string' | 'number' | 'boolean' | 'json' {
    if (typeof value === 'string') return 'string';
    if (typeof value === 'number') return 'number';
    if (typeof value === 'boolean') return 'boolean';
    return 'json';
  }

  /**
   * Serialize setting value for storage
   */
  private serializeSettingValue(value: SettingValue): string {
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    return String(value);
  }

  /**
   * Deserialize setting value from storage
   */
  private deserializeSettingValue(value: string, type: string): SettingValue {
    switch (type) {
      case 'number':
        return Number(value);
      case 'boolean':
        return value === 'true' || value === '1';
      case 'json':
        try {
          return JSON.parse(value);
        } catch {
          return value;
        }
      default:
        return value;
    }
  }

  /**
   * Check if setting is a default setting
   */
  private isDefaultSetting(key: string): boolean {
    const defaultKeys = this.getDefaultSettings().map(s => s.key);
    return defaultKeys.includes(key);
  }

  /**
   * Get default settings
   */
  private getDefaultSettings(): Setting[] {
    return [
      {
        key: 'app.version',
        value: '1.0.0',
        valueType: 'string',
        category: 'system',
        description: 'Application version',
        isSensitive: false,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        key: 'app.first_run',
        value: true,
        valueType: 'boolean',
        category: 'system',
        description: 'First time running the app',
        isSensitive: false,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        key: 'ui.theme',
        value: 'system',
        valueType: 'string',
        category: 'appearance',
        description: 'UI theme preference',
        isSensitive: false,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        key: 'hotkeys.show_floating_window',
        value: 'Ctrl+Shift+P',
        valueType: 'string',
        category: 'hotkeys',
        description: 'Show floating window hotkey',
        isSensitive: false,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        key: 'ai.default_provider',
        value: 'openai',
        valueType: 'string',
        category: 'ai',
        description: 'Default AI provider',
        isSensitive: false,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        key: 'voice.default_language',
        value: 'en',
        valueType: 'string',
        category: 'voice',
        description: 'Default voice recognition language',
        isSensitive: false,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
  }
}
