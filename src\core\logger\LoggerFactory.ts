/**
 * Logger Factory for creating and managing logger instances
 * Provides centralized logger configuration and instance management
 */

import * as path from 'path';
import { Logger } from './Logger';
import { FileTransport } from './FileTransport';
import { ConsoleTransport } from './ConsoleTransport';
import { PerformanceLogger } from './PerformanceLogger';
import {
  LoggerConfig,
  LogContext,
  LogLevel,
  LogTransport,
  LogFilter
} from './types';
import { LevelFilter } from './filters';
import { DefaultFormatter, JSONFormatter } from './formatters';

export class LoggerFactory {
  private static instance: LoggerFactory;
  private loggers: Map<string, Logger> = new Map();
  private defaultConfig: LoggerConfig | null = null;
  private performanceLoggers: Map<string, PerformanceLogger> = new Map();

  static getInstance(): LoggerFactory {
    if (!LoggerFactory.instance) {
      LoggerFactory.instance = new LoggerFactory();
    }
    return LoggerFactory.instance;
  }

  /**
   * Configure the default logger settings
   */
  configure(config: Partial<LoggerConfig>): void {
    this.defaultConfig = {
      level: config.level || LogLevel.INFO,
      transports: config.transports || this.createDefaultTransports(),
      filters: config.filters || [new LevelFilter(config.level || LogLevel.INFO)]
    };
  }

  /**
   * Get or create a logger instance
   */
  getLogger(name: string, context?: LogContext): Logger {
    const key = `${name}:${JSON.stringify(context || {})}`;

    if (!this.loggers.has(key)) {
      const logger = this.createLogger(name, context);
      this.loggers.set(key, logger);
    }

    return this.loggers.get(key)!;
  }

  /**
   * Get or create a performance logger instance
   */
  getPerformanceLogger(name: string, context?: LogContext): PerformanceLogger {
    const key = `perf:${name}:${JSON.stringify(context || {})}`;

    if (!this.performanceLoggers.has(key)) {
      const logger = this.getLogger(name, context);
      const perfLogger = new PerformanceLogger(logger);
      this.performanceLoggers.set(key, perfLogger);
    }

    return this.performanceLoggers.get(key)!;
  }

  /**
   * Create a logger with development-friendly settings
   */
  createDevelopmentLogger(name: string, context?: LogContext): Logger {
    const logger = new Logger({
      level: LogLevel.DEBUG,
      context: { module: name, ...context },
      transports: [
        new ConsoleTransport({
          colorize: true,
          formatter: new DefaultFormatter()
        })
      ],
      filters: [new LevelFilter(LogLevel.DEBUG)]
    });

    return logger;
  }

  /**
   * Create a logger with production-friendly settings
   */
  createProductionLogger(name: string, context?: LogContext): Logger {
    const logDir = path.join(process.cwd(), 'logs');
    
    const logger = new Logger({
      level: LogLevel.INFO,
      context: { module: name, ...context },
      transports: [
        new FileTransport({
          filePath: path.join(logDir, 'app.log'),
          maxFileSize: 50 * 1024 * 1024, // 50MB
          maxFiles: 10,
          format: new JSONFormatter()
        }),
        new FileTransport({
          filePath: path.join(logDir, 'error.log'),
          maxFileSize: 10 * 1024 * 1024, // 10MB
          maxFiles: 5,
          format: new JSONFormatter()
        })
      ],
      filters: [new LevelFilter(LogLevel.INFO)]
    });

    return logger;
  }

  /**
   * Close all logger instances
   */
  async closeAll(): Promise<void> {
    const closePromises: Promise<void>[] = [];

    for (const logger of Array.from(this.loggers.values())) {
      closePromises.push(logger.close());
    }

    await Promise.allSettled(closePromises);
    
    this.loggers.clear();
    this.performanceLoggers.clear();
  }

  /**
   * Create a logger instance with default or custom configuration
   */
  private createLogger(name: string, context?: LogContext): Logger {
    const logger = new Logger();

    // Set context
    if (context) {
      logger.setContext({ module: name, ...context });
    } else {
      logger.setContext({ module: name });
    }

    // Apply default configuration if available
    if (this.defaultConfig) {
      logger.setLevel(this.defaultConfig.level);

      this.defaultConfig.transports.forEach(transport => {
        logger.addTransport(transport);
      });

      this.defaultConfig.filters.forEach(filter => {
        logger.addFilter(filter);
      });
    } else {
      // Use basic console transport if no configuration is set
      logger.addTransport(new ConsoleTransport());
    }

    return logger;
  }

  /**
   * Create default transports for basic logging
   */
  private createDefaultTransports(): LogTransport[] {
    const isDevelopment = process.env.NODE_ENV === 'development';

    if (isDevelopment) {
      return [
        new ConsoleTransport({
          colorize: true,
          formatter: new DefaultFormatter()
        })
      ];
    } else {
      const logDir = path.join(process.cwd(), 'logs');
      
      return [
        new FileTransport({
          filePath: path.join(logDir, 'app.log'),
          maxFileSize: 10 * 1024 * 1024, // 10MB
          maxFiles: 5,
          format: new JSONFormatter()
        }),
        new ConsoleTransport({
          colorize: false,
          formatter: new JSONFormatter()
        })
      ];
    }
  }
}
