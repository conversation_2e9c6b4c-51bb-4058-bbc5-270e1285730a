/**
 * Validation schemas and utilities
 * Comprehensive validation system for all data models
 */

// Validation types
export type ValidationType = 
  | 'required'
  | 'min'
  | 'max'
  | 'minLength'
  | 'maxLength'
  | 'pattern'
  | 'email'
  | 'url'
  | 'enum'
  | 'custom';

export type ValidationSeverity = 'error' | 'warning' | 'info';
export type SchemaType = 'object' | 'array' | 'string' | 'number' | 'boolean' | 'null';

// Validation function type
export type ValidationFunction = (value: unknown, context?: ValidationContext) => boolean | Promise<boolean>;

// Validation context
export interface ValidationContext {
  readonly field: string;
  readonly object: Readonly<Record<string, unknown>>;
  readonly path: readonly string[];
  readonly root: Readonly<Record<string, unknown>>;
}

// Validation rule
export interface ValidationRule {
  readonly type: ValidationType;
  readonly value?: unknown;
  readonly message: string;
  readonly severity: ValidationSeverity;
  readonly validator?: ValidationFunction;
  readonly async?: boolean;
}

// Validation error
export interface ValidationError {
  readonly field: string;
  readonly message: string;
  readonly code: string;
  readonly value: unknown;
  readonly rule: ValidationRule;
}

// Validation warning
export interface ValidationWarning {
  readonly field: string;
  readonly message: string;
  readonly code: string;
  readonly value: unknown;
  readonly rule: ValidationRule;
}

// Validation result
export interface ValidationResult {
  readonly valid: boolean;
  readonly errors: readonly ValidationError[];
  readonly warnings: readonly ValidationWarning[];
  readonly metadata?: Readonly<Record<string, unknown>>;
}

// Schema definition
export interface Schema {
  readonly $schema?: string;
  readonly type: SchemaType;
  readonly properties?: Readonly<Record<string, Schema>>;
  readonly items?: Schema;
  readonly required?: readonly string[];
  readonly additionalProperties?: boolean | Schema;
  readonly enum?: readonly unknown[];
  readonly const?: unknown;
  readonly validation?: readonly ValidationRule[];
}

// Validator interface
export interface Validator {
  validate(value: unknown, schema: Schema, context?: ValidationContext): ValidationResult;
  validateAsync(value: unknown, schema: Schema, context?: ValidationContext): Promise<ValidationResult>;
}

// Common validation rules
export const CommonValidationRules = {
  required: (): ValidationRule => ({
    type: 'required',
    message: 'This field is required',
    severity: 'error'
  }),

  minLength: (min: number): ValidationRule => ({
    type: 'minLength',
    value: min,
    message: `Must be at least ${min} characters long`,
    severity: 'error'
  }),

  maxLength: (max: number): ValidationRule => ({
    type: 'maxLength',
    value: max,
    message: `Must be no more than ${max} characters long`,
    severity: 'error'
  }),

  min: (min: number): ValidationRule => ({
    type: 'min',
    value: min,
    message: `Must be at least ${min}`,
    severity: 'error'
  }),

  max: (max: number): ValidationRule => ({
    type: 'max',
    value: max,
    message: `Must be no more than ${max}`,
    severity: 'error'
  }),

  email: (): ValidationRule => ({
    type: 'email',
    message: 'Must be a valid email address',
    severity: 'error'
  }),

  url: (): ValidationRule => ({
    type: 'url',
    message: 'Must be a valid URL',
    severity: 'error'
  }),

  pattern: (pattern: RegExp, message?: string): ValidationRule => ({
    type: 'pattern',
    value: pattern,
    message: message || 'Invalid format',
    severity: 'error'
  }),

  enum: (values: readonly unknown[], message?: string): ValidationRule => ({
    type: 'enum',
    value: values,
    message: message || `Must be one of: ${values.join(', ')}`,
    severity: 'error'
  }),

  custom: (validator: ValidationFunction, message: string): ValidationRule => ({
    type: 'custom',
    validator,
    message,
    severity: 'error'
  })
};

// Schema builders for common models
export const PromptSchema: Schema = {
  type: 'object',
  required: ['id', 'title', 'content', 'categoryId', 'createdAt', 'updatedAt'],
  properties: {
    id: {
      type: 'string',
      validation: [CommonValidationRules.required()]
    },
    title: {
      type: 'string',
      validation: [
        CommonValidationRules.required(),
        CommonValidationRules.minLength(1),
        CommonValidationRules.maxLength(200)
      ]
    },
    content: {
      type: 'string',
      validation: [
        CommonValidationRules.required(),
        CommonValidationRules.minLength(1),
        CommonValidationRules.maxLength(50000)
      ]
    },
    description: {
      type: 'string',
      validation: [CommonValidationRules.maxLength(1000)]
    },
    categoryId: {
      type: 'string',
      validation: [CommonValidationRules.required()]
    },
    tags: {
      type: 'array',
      items: {
        type: 'string',
        validation: [CommonValidationRules.minLength(1), CommonValidationRules.maxLength(50)]
      }
    },
    isTemplate: {
      type: 'boolean'
    },
    isFavorite: {
      type: 'boolean'
    },
    isArchived: {
      type: 'boolean'
    }
  }
};

export const CategorySchema: Schema = {
  type: 'object',
  required: ['id', 'name', 'description'],
  properties: {
    id: {
      type: 'string',
      validation: [CommonValidationRules.required()]
    },
    name: {
      type: 'string',
      validation: [
        CommonValidationRules.required(),
        CommonValidationRules.minLength(1),
        CommonValidationRules.maxLength(100)
      ]
    },
    description: {
      type: 'string',
      validation: [
        CommonValidationRules.required(),
        CommonValidationRules.maxLength(500)
      ]
    },
    parentId: {
      type: 'string'
    },
    color: {
      type: 'string',
      validation: [CommonValidationRules.pattern(/^#[0-9A-Fa-f]{6}$/, 'Must be a valid hex color')]
    },
    sortOrder: {
      type: 'number',
      validation: [CommonValidationRules.min(0)]
    }
  }
};

export const UserSchema: Schema = {
  type: 'object',
  required: ['id'],
  properties: {
    id: {
      type: 'string',
      validation: [CommonValidationRules.required()]
    },
    username: {
      type: 'string',
      validation: [
        CommonValidationRules.minLength(3),
        CommonValidationRules.maxLength(50),
        CommonValidationRules.pattern(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, underscores, and hyphens')
      ]
    },
    email: {
      type: 'string',
      validation: [CommonValidationRules.email()]
    },
    displayName: {
      type: 'string',
      validation: [
        CommonValidationRules.minLength(1),
        CommonValidationRules.maxLength(100)
      ]
    }
  }
};

// Type guards
export const isValidationError = (obj: unknown): obj is ValidationError => {
  return typeof obj === 'object' && obj !== null &&
    'field' in obj && 'message' in obj && 'code' in obj && 'value' in obj && 'rule' in obj;
};

export const isValidationResult = (obj: unknown): obj is ValidationResult => {
  return typeof obj === 'object' && obj !== null &&
    'valid' in obj && 'errors' in obj && 'warnings' in obj;
};

export const isSchema = (obj: unknown): obj is Schema => {
  return typeof obj === 'object' && obj !== null && 'type' in obj;
};

// Utility functions
export const createValidationError = (
  field: string,
  message: string,
  code: string,
  value: unknown,
  rule: ValidationRule
): ValidationError => ({
  field,
  message,
  code,
  value,
  rule
});

export const createValidationResult = (
  valid: boolean,
  errors: ValidationError[] = [],
  warnings: ValidationWarning[] = [],
  metadata?: Record<string, unknown>
): ValidationResult => ({
  valid,
  errors,
  warnings,
  ...(metadata && { metadata })
});

export const combineValidationResults = (...results: ValidationResult[]): ValidationResult => {
  const allErrors = results.flatMap(r => r.errors);
  const allWarnings = results.flatMap(r => r.warnings);
  const valid = results.every(r => r.valid);

  return createValidationResult(valid, allErrors, allWarnings);
};

// Export ModelValidator
export { ModelValidator } from './ModelValidator';
