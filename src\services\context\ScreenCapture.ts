/**
 * Screen Capture Service
 * Handles screen capture functionality with OCR integration
 */

import { screen, desktopCapturer, BrowserWindow } from 'electron';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as os from 'os';
import { 
  IScreenCaptureService, 
  ScreenRegion, 
  ScreenCaptureOptions, 
  CaptureResult, 
  DisplayInfo,
  ContextExtractionError,
  ContextErrorCode,
  OCREngine
} from './types';
import { logger } from '../../core/logger';

export class ScreenCaptureService implements IScreenCaptureService {
  private displays: Electron.Display[] = [];
  private tempDirectory: string;

  constructor(
    private ocrEngine: OCREngine,
    tempDir?: string
  ) {
    this.tempDirectory = tempDir || path.join(os.tmpdir(), 'promptpilot-screenshots');
    this.updateDisplays();
    this.ensureTempDirectory();

    // Update displays when screen configuration changes
    screen.on('display-added', () => this.updateDisplays());
    screen.on('display-removed', () => this.updateDisplays());
    screen.on('display-metrics-changed', () => this.updateDisplays());
  }

  /**
   * Capture full screen
   */
  async captureFullScreen(options: ScreenCaptureOptions = {}): Promise<CaptureResult> {
    try {
      const primaryDisplay = screen.getPrimaryDisplay();
      const region: ScreenRegion = {
        x: 0,
        y: 0,
        width: primaryDisplay.bounds.width,
        height: primaryDisplay.bounds.height
      };

      logger.debug('Capturing full screen', { region, options });
      return await this.captureRegion(region, options);
    } catch (error) {
      logger.error('Full screen capture failed', { 
        error: error instanceof Error ? error.message : String(error) 
      });
      
      throw new ContextExtractionError(
        'Full screen capture failed',
        ContextErrorCode.SCREEN_CAPTURE_FAILED,
        'ScreenCaptureService',
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Capture specific screen region
   */
  async captureRegion(region: ScreenRegion, options: ScreenCaptureOptions = {}): Promise<CaptureResult> {
    try {
      logger.debug('Capturing screen region', { region, options });

      // Get screen sources
      const sources = await desktopCapturer.getSources({
        types: ['screen'],
        thumbnailSize: {
          width: region.width,
          height: region.height
        }
      });

      if (sources.length === 0) {
        throw new Error('No screen sources available');
      }

      // Use the primary screen source
      const primarySource = sources[0];
      let thumbnail = primarySource.thumbnail;

      // Crop if region is specified and different from full screen
      if (options.region && this.isRegionCropped(options.region, region)) {
        thumbnail = this.cropImage(thumbnail, options.region, region);
      }

      // Save screenshot
      const imagePath = await this.saveScreenshot(thumbnail, options);

      // Extract text if OCR is enabled
      let extractedText: string | undefined;
      if (options.includeOCR) {
        try {
          extractedText = await this.ocrEngine.extractText(imagePath, {
            language: options.ocrLanguage || 'eng'
          });
          logger.debug('OCR text extracted from screenshot', { 
            imagePath, 
            textLength: extractedText.length 
          });
        } catch (error) {
          logger.warn('OCR extraction failed for screenshot', { 
            imagePath, 
            error: error instanceof Error ? error.message : String(error) 
          });
        }
      }

      const result: CaptureResult = {
        imagePath,
        metadata: {
          timestamp: new Date(),
          region,
          displayInfo: this.getDisplays()
        },
        ...(extractedText && { extractedText })
      };

      logger.info('Screen capture completed successfully', { 
        imagePath, 
        hasOCR: !!extractedText,
        region 
      });

      return result;
    } catch (error) {
      logger.error('Screen region capture failed', { 
        region, 
        error: error instanceof Error ? error.message : String(error) 
      });
      
      throw new ContextExtractionError(
        `Screen region capture failed: ${error instanceof Error ? error.message : String(error)}`,
        ContextErrorCode.SCREEN_CAPTURE_FAILED,
        'ScreenCaptureService',
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Capture active window
   */
  async captureActiveWindow(options: ScreenCaptureOptions = {}): Promise<CaptureResult> {
    try {
      const activeWindow = BrowserWindow.getFocusedWindow();
      
      if (!activeWindow) {
        throw new Error('No active window found');
      }

      const bounds = activeWindow.getBounds();
      const region: ScreenRegion = {
        x: bounds.x,
        y: bounds.y,
        width: bounds.width,
        height: bounds.height
      };

      logger.debug('Capturing active window', { windowTitle: activeWindow.getTitle(), region });
      return await this.captureRegion(region, options);
    } catch (error) {
      logger.error('Active window capture failed', { 
        error: error instanceof Error ? error.message : String(error) 
      });
      
      throw new ContextExtractionError(
        'Active window capture failed',
        ContextErrorCode.SCREEN_CAPTURE_FAILED,
        'ScreenCaptureService',
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Get display information
   */
  getDisplays(): DisplayInfo {
    return {
      displays: this.displays.map(display => ({
        id: display.id,
        bounds: display.bounds,
        workArea: display.workArea,
        scaleFactor: display.scaleFactor
      }))
    };
  }

  /**
   * Save screenshot to file
   */
  private async saveScreenshot(thumbnail: Electron.NativeImage, options: ScreenCaptureOptions): Promise<string> {
    const format = options.format || 'png';
    const quality = options.quality || 90;

    let buffer: Buffer;
    switch (format) {
      case 'jpg':
        buffer = thumbnail.toJPEG(quality);
        break;
      case 'webp':
        // Convert to WebP via data URL (Electron limitation)
        const dataUrl = thumbnail.toDataURL();
        buffer = Buffer.from(dataUrl.split(',')[1], 'base64');
        break;
      default:
        buffer = thumbnail.toPNG();
    }

    const filename = `screenshot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.${format}`;
    const filepath = path.join(this.tempDirectory, filename);

    await fs.writeFile(filepath, buffer);
    logger.debug('Screenshot saved', { filepath, format, size: buffer.length });

    return filepath;
  }

  /**
   * Crop image to specified region
   */
  private cropImage(image: Electron.NativeImage, cropRegion: ScreenRegion, fullRegion: ScreenRegion): Electron.NativeImage {
    const cropRect = {
      x: Math.max(0, cropRegion.x - fullRegion.x),
      y: Math.max(0, cropRegion.y - fullRegion.y),
      width: Math.min(cropRegion.width, fullRegion.width),
      height: Math.min(cropRegion.height, fullRegion.height)
    };

    return image.crop(cropRect);
  }

  /**
   * Check if region needs cropping
   */
  private isRegionCropped(cropRegion: ScreenRegion, fullRegion: ScreenRegion): boolean {
    return cropRegion.x !== fullRegion.x ||
           cropRegion.y !== fullRegion.y ||
           cropRegion.width !== fullRegion.width ||
           cropRegion.height !== fullRegion.height;
  }

  /**
   * Update display information
   */
  private updateDisplays(): void {
    this.displays = screen.getAllDisplays();
    logger.debug('Display information updated', { displayCount: this.displays.length });
  }

  /**
   * Ensure temp directory exists
   */
  private async ensureTempDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.tempDirectory, { recursive: true });
    } catch (error) {
      logger.error('Failed to create temp directory for screenshots', { 
        tempDirectory: this.tempDirectory, 
        error 
      });
    }
  }

  /**
   * Clean up old screenshots
   */
  async cleanupOldScreenshots(maxAgeMs: number = 24 * 60 * 60 * 1000): Promise<void> {
    try {
      const files = await fs.readdir(this.tempDirectory);
      const now = Date.now();

      for (const file of files) {
        if (file.startsWith('screenshot_')) {
          const filePath = path.join(this.tempDirectory, file);
          const stats = await fs.stat(filePath);
          
          if (now - stats.mtime.getTime() > maxAgeMs) {
            await fs.unlink(filePath);
            logger.debug('Cleaned up old screenshot', { filePath });
          }
        }
      }
    } catch (error) {
      logger.warn('Failed to cleanup old screenshots', { 
        tempDirectory: this.tempDirectory, 
        error 
      });
    }
  }

  /**
   * Cleanup resources
   */
  async destroy(): Promise<void> {
    // Clean up all screenshots
    await this.cleanupOldScreenshots(0);
    logger.debug('ScreenCaptureService destroyed');
  }
}
