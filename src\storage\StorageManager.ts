/**
 * Main storage manager
 * Coordinates database operations and provides unified storage interface
 */

import * as path from 'path';
import * as os from 'os';
import { 
  StorageManager, 
  DatabaseConfig, 
  DatabaseConnection, 
  Transaction, 
  DatabaseStats 
} from './types';
import { SQLiteDatabaseConnection } from './DatabaseConnection';
import { MigrationManager } from './MigrationManager';
import { PromptRepository } from './repositories/PromptRepository';
import { CategoryRepository } from './repositories/CategoryRepository';
import { SettingsRepository } from './repositories/SettingsRepository';
import { UserRepository } from './repositories/UserRepository';
import { SystemRepository } from './repositories/SystemRepository';
import { SessionRepository } from './repositories/SessionRepository';
import { LoggerFactory } from '../core/logger';
import { getFileSystemManager } from '../core/filesystem';

const logger = LoggerFactory.getInstance().getLogger('StorageManager');

/**
 * Main storage manager implementation
 */
export class SQLiteStorageManager implements StorageManager {
  private connection: SQLiteDatabaseConnection;
  private migrationManager: MigrationManager;
  private config: DatabaseConfig;
  private isInitialized: boolean = false;

  // Repository instances
  private _promptRepository?: PromptRepository;
  private _categoryRepository?: CategoryRepository;
  private _settingsRepository?: SettingsRepository;
  private _userRepository?: UserRepository;
  private _systemRepository?: SystemRepository;
  private _sessionRepository?: SessionRepository;

  constructor(config?: Partial<DatabaseConfig>) {
    this.config = this.createDefaultConfig(config);
    this.connection = new SQLiteDatabaseConnection(this.config);
    this.migrationManager = new MigrationManager(this.connection);
  }

  /**
   * Initialize the storage manager
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Ensure database directory exists
      await this.ensureDatabaseDirectory();

      // Initialize database connection
      await this.connection.initialize();

      // Run migrations
      await this.migrationManager.migrate();

      // Initialize repositories
      this.initializeRepositories();

      // Set up backup schedule if enabled
      if (this.config.backup.enabled) {
        this.scheduleBackups();
      }

      this.isInitialized = true;
      logger.info('Storage manager initialized successfully', { 
        path: this.config.path,
        backupEnabled: this.config.backup.enabled 
      });
    } catch (error) {
      logger.error('Failed to initialize storage manager', error);
      throw error;
    }
  }

  /**
   * Close the storage manager
   */
  async close(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    try {
      await this.connection.close();
      this.isInitialized = false;
      logger.info('Storage manager closed');
    } catch (error) {
      logger.error('Failed to close storage manager', error);
      throw error;
    }
  }

  /**
   * Get database connection
   */
  getConnection(): DatabaseConnection {
    this.ensureInitialized();
    return this.connection;
  }

  /**
   * Execute operations within a transaction
   */
  async transaction<T>(fn: (tx: Transaction) => Promise<T>): Promise<T> {
    this.ensureInitialized();
    return this.connection.transaction(fn);
  }

  /**
   * Create database backup
   */
  async backup(destination?: string): Promise<string> {
    this.ensureInitialized();
    
    const backupPath = destination || this.generateBackupPath();
    
    try {
      await this.connection.backup(backupPath);
      logger.info('Database backup created', { path: backupPath });
      return backupPath;
    } catch (error) {
      logger.error('Database backup failed', { path: backupPath, error });
      throw error;
    }
  }

  /**
   * Restore database from backup
   */
  async restore(source: string): Promise<void> {
    this.ensureInitialized();
    
    try {
      const fileSystem = getFileSystemManager();
      
      // Validate backup file exists
      if (!await fileSystem.exists(source)) {
        throw new Error(`Backup file not found: ${source}`);
      }

      // Close current connection
      await this.connection.close();

      // Copy backup to database location
      await fileSystem.copyFile(source, this.config.path, { overwrite: true });

      // Reinitialize connection
      await this.connection.initialize();
      
      logger.info('Database restored from backup', { source });
    } catch (error) {
      logger.error('Database restore failed', { source, error });
      throw error;
    }
  }

  /**
   * Vacuum database to optimize storage
   */
  async vacuum(): Promise<void> {
    this.ensureInitialized();
    
    try {
      await this.connection.exec('VACUUM');
      logger.info('Database vacuum completed');
    } catch (error) {
      logger.error('Database vacuum failed', error);
      throw error;
    }
  }

  /**
   * Get database statistics
   */
  async getStats(): Promise<DatabaseStats> {
    this.ensureInitialized();
    
    try {
      const [
        pageCountResult,
        pageSizeResult,
        freePagesResult,
        schemaVersionResult,
        userVersionResult
      ] = await Promise.all([
        this.connection.get<{ page_count: number }>('PRAGMA page_count'),
        this.connection.get<{ page_size: number }>('PRAGMA page_size'),
        this.connection.get<{ freelist_count: number }>('PRAGMA freelist_count'),
        this.connection.get<{ schema_version: number }>('PRAGMA schema_version'),
        this.connection.get<{ user_version: number }>('PRAGMA user_version')
      ]);

      const pageCount = pageCountResult?.page_count || 0;
      const pageSize = pageSizeResult?.page_size || 0;
      const freePages = freePagesResult?.freelist_count || 0;

      // Get table statistics
      const tables = await this.getTableStats();

      return {
        size: pageCount * pageSize,
        pageCount,
        pageSize,
        freePages,
        schemaVersion: schemaVersionResult?.schema_version || 0,
        userVersion: userVersionResult?.user_version || 0,
        tables
      };
    } catch (error) {
      logger.error('Failed to get database statistics', error);
      throw error;
    }
  }

  /**
   * Get repository instances
   */
  get prompts(): PromptRepository {
    this.ensureInitialized();
    return this._promptRepository!;
  }

  get categories(): CategoryRepository {
    this.ensureInitialized();
    return this._categoryRepository!;
  }

  get settings(): SettingsRepository {
    this.ensureInitialized();
    return this._settingsRepository!;
  }

  get users(): UserRepository {
    this.ensureInitialized();
    return this._userRepository!;
  }

  get system(): SystemRepository {
    this.ensureInitialized();
    return this._systemRepository!;
  }

  get sessions(): SessionRepository {
    this.ensureInitialized();
    return this._sessionRepository!;
  }

  /**
   * Create default configuration
   */
  private createDefaultConfig(config?: Partial<DatabaseConfig>): DatabaseConfig {
    const appDataPath = this.getAppDataPath();
    
    return {
      path: path.join(appDataPath, 'promptpilot.db'),
      encryption: {
        enabled: false
      },
      performance: {
        journalMode: 'WAL',
        synchronous: 'NORMAL',
        cacheSize: 10000,
        mmapSize: 268435456 // 256MB
      },
      backup: {
        enabled: true,
        interval: 60, // 1 hour
        maxBackups: 10
      },
      ...config
    };
  }

  /**
   * Get application data path
   */
  private getAppDataPath(): string {
    const platform = os.platform();
    const home = os.homedir();

    switch (platform) {
      case 'win32':
        return path.join(process.env.APPDATA || path.join(home, 'AppData', 'Roaming'), 'PromptPilot');
      case 'darwin':
        return path.join(home, 'Library', 'Application Support', 'PromptPilot');
      default:
        return path.join(process.env.XDG_CONFIG_HOME || path.join(home, '.config'), 'PromptPilot');
    }
  }

  /**
   * Ensure database directory exists
   */
  private async ensureDatabaseDirectory(): Promise<void> {
    const fileSystem = getFileSystemManager();
    const dbDir = path.dirname(this.config.path);
    
    if (!await fileSystem.exists(dbDir)) {
      await fileSystem.createDirectory(dbDir, { recursive: true });
      logger.info('Database directory created', { path: dbDir });
    }
  }

  /**
   * Initialize repository instances
   */
  private initializeRepositories(): void {
    this._promptRepository = new PromptRepository(this.connection);
    this._categoryRepository = new CategoryRepository(this.connection);
    this._settingsRepository = new SettingsRepository(this.connection);
    this._userRepository = new UserRepository(this.connection);
    this._systemRepository = new SystemRepository(this.connection);
    this._sessionRepository = new SessionRepository(this.connection);
  }

  /**
   * Schedule automatic backups
   */
  private scheduleBackups(): void {
    const intervalMs = this.config.backup.interval * 60 * 1000;
    
    setInterval(async () => {
      try {
        const backupPath = await this.backup();
        await this.cleanupOldBackups();
        logger.info('Scheduled backup completed', { path: backupPath });
      } catch (error) {
        logger.error('Scheduled backup failed', error);
      }
    }, intervalMs);

    logger.info('Backup schedule configured', { 
      intervalMinutes: this.config.backup.interval,
      maxBackups: this.config.backup.maxBackups 
    });
  }

  /**
   * Generate backup file path
   */
  private generateBackupPath(): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = path.join(path.dirname(this.config.path), 'backups');
    return path.join(backupDir, `promptpilot-backup-${timestamp}.db`);
  }

  /**
   * Clean up old backup files
   */
  private async cleanupOldBackups(): Promise<void> {
    try {
      const fileSystem = getFileSystemManager();
      const backupDir = path.join(path.dirname(this.config.path), 'backups');
      
      if (!await fileSystem.exists(backupDir)) {
        return;
      }

      const files = await fileSystem.listDirectory(backupDir);
      const backupFiles = files
        .filter(file => file.name.startsWith('promptpilot-backup-') && file.name.endsWith('.db'))
        .sort((a, b) => b.modifiedAt.getTime() - a.modifiedAt.getTime());

      if (backupFiles.length > this.config.backup.maxBackups) {
        const filesToDelete = backupFiles.slice(this.config.backup.maxBackups);
        
        for (const file of filesToDelete) {
          await fileSystem.deleteFile(file.path);
          logger.debug('Old backup deleted', { path: file.path });
        }
        
        logger.info('Old backups cleaned up', { deleted: filesToDelete.length });
      }
    } catch (error) {
      logger.warn('Failed to cleanup old backups', error);
    }
  }

  /**
   * Get table statistics
   */
  private async getTableStats(): Promise<any[]> {
    const tables = await this.connection.all<{ name: string }>(
      "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
    );

    const stats = [];
    for (const table of tables) {
      const countResult = await this.connection.get<{ count: number }>(
        `SELECT COUNT(*) as count FROM ${table.name}`
      );
      
      stats.push({
        name: table.name,
        rowCount: countResult?.count || 0,
        size: 0, // SQLite doesn't provide easy table size info
        indexes: [] // Would need additional queries to get index info
      });
    }

    return stats;
  }

  /**
   * Ensure storage manager is initialized
   */
  private ensureInitialized(): void {
    if (!this.isInitialized) {
      throw new Error('Storage manager not initialized');
    }
  }
}

// Global storage manager instance
let globalStorageManager: SQLiteStorageManager | null = null;

/**
 * Get the global storage manager instance
 */
export const getStorageManager = (): SQLiteStorageManager => {
  if (!globalStorageManager) {
    globalStorageManager = new SQLiteStorageManager();
  }
  return globalStorageManager;
};

/**
 * Initialize the global storage manager
 */
export const initializeStorage = async (config?: Partial<DatabaseConfig>): Promise<SQLiteStorageManager> => {
  globalStorageManager = new SQLiteStorageManager(config);
  await globalStorageManager.initialize();
  return globalStorageManager;
};
