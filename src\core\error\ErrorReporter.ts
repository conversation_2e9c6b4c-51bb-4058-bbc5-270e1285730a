/**
 * Error reporting and analytics
 * Handles error reporting, analytics, and notification systems
 */

import { EventEmitter } from 'events';
import { AppError, ErrorReporter, ErrorAnalytics, ErrorNotification, ErrorAction } from './types';
import { LoggerFactory } from '../logger';

const logger = LoggerFactory.getInstance().getLogger('ErrorReporter');

/**
 * Console error reporter for development
 */
export class ConsoleErrorReporter implements ErrorReporter {
  private config: any = {};

  configure(config: any): void {
    this.config = config;
  }

  async report(error: AppError): Promise<void> {
    console.group(`🚨 Error Report [${error.severity.toUpperCase()}]`);
    console.error('ID:', error.id);
    console.error('Code:', error.code);
    console.error('Category:', error.category);
    console.error('Message:', error.message);
    console.error('Context:', error.context);
    console.error('Stack:', error.stackTrace);
    if (error.cause) {
      console.error('Cause:', error.cause);
    }
    console.groupEnd();
  }

  async reportBatch(errors: AppError[]): Promise<void> {
    console.group(`🚨 Batch Error Report (${errors.length} errors)`);
    for (const error of errors) {
      await this.report(error);
    }
    console.groupEnd();
  }
}

/**
 * File-based error reporter
 */
export class FileErrorReporter implements ErrorReporter {
  private config: any = {};
  private fileSystemManager: any;

  constructor(fileSystemManager: any) {
    this.fileSystemManager = fileSystemManager;
  }

  configure(config: any): void {
    this.config = config;
  }

  async report(error: AppError): Promise<void> {
    try {
      const logEntry = {
        timestamp: new Date().toISOString(),
        error: error.toJSON()
      };

      const logPath = this.config.logPath || 'logs/errors.log';
      const logLine = JSON.stringify(logEntry) + '\n';

      await this.fileSystemManager.writeFile(logPath, logLine, { 
        flag: 'a',
        atomic: false 
      });
    } catch (reportError) {
      logger.error('Failed to write error to file', reportError);
    }
  }

  async reportBatch(errors: AppError[]): Promise<void> {
    const logEntries = errors.map(error => ({
      timestamp: new Date().toISOString(),
      error: error.toJSON()
    }));

    try {
      const logPath = this.config.logPath || 'logs/errors.log';
      const logContent = logEntries.map(entry => JSON.stringify(entry)).join('\n') + '\n';

      await this.fileSystemManager.writeFile(logPath, logContent, { 
        flag: 'a',
        atomic: false 
      });
    } catch (reportError) {
      logger.error('Failed to write batch errors to file', reportError);
    }
  }
}

/**
 * Remote error reporter (placeholder for future implementation)
 */
export class RemoteErrorReporter implements ErrorReporter {
  private config: any = {};
  private endpoint: string = '';

  configure(config: any): void {
    this.config = config;
    this.endpoint = config.endpoint || '';
  }

  async report(error: AppError): Promise<void> {
    if (!this.endpoint) {
      logger.warn('Remote error reporting endpoint not configured');
      return;
    }

    try {
      // Placeholder for actual HTTP request
      logger.info('Would report error to remote endpoint', { 
        endpoint: this.endpoint,
        errorId: error.id 
      });
    } catch (reportError) {
      logger.error('Failed to report error to remote endpoint', reportError);
    }
  }

  async reportBatch(errors: AppError[]): Promise<void> {
    if (!this.endpoint) {
      logger.warn('Remote error reporting endpoint not configured');
      return;
    }

    try {
      // Placeholder for actual HTTP request
      logger.info('Would report batch errors to remote endpoint', { 
        endpoint: this.endpoint,
        errorCount: errors.length 
      });
    } catch (reportError) {
      logger.error('Failed to report batch errors to remote endpoint', reportError);
    }
  }
}

/**
 * Error analytics tracker
 */
export class ErrorAnalyticsTracker extends EventEmitter {
  private errorStats: Map<string, ErrorAnalytics> = new Map();
  private config: any = {};

  configure(config: any): void {
    this.config = config;
  }

  /**
   * Track an error occurrence
   */
  track(error: AppError): void {
    const key = `${error.code}_${error.category}`;
    const existing = this.errorStats.get(key);

    if (existing) {
      // Update existing analytics
      const updated: ErrorAnalytics = {
        ...existing,
        frequency: existing.frequency + 1,
        lastOccurrence: error.timestamp,
        affectedUsers: this.updateAffectedUsers(existing, error.context.userId)
      };
      this.errorStats.set(key, updated);
    } else {
      // Create new analytics entry
      const analytics: ErrorAnalytics = {
        errorId: key,
        frequency: 1,
        firstOccurrence: error.timestamp,
        lastOccurrence: error.timestamp,
        affectedUsers: error.context.userId ? 1 : 0,
        resolutionRate: 0,
        averageRecoveryTime: 0,
        commonPatterns: [error.context.component]
      };
      this.errorStats.set(key, analytics);
    }

    this.emit('error-tracked', { error, analytics: this.errorStats.get(key) });
  }

  /**
   * Get analytics for a time range
   */
  getAnalytics(timeRange?: { start: Date; end: Date }): ErrorAnalytics[] {
    let analytics = Array.from(this.errorStats.values());

    if (timeRange) {
      analytics = analytics.filter(stat => 
        stat.firstOccurrence >= timeRange.start && 
        stat.lastOccurrence <= timeRange.end
      );
    }

    return analytics.sort((a, b) => b.frequency - a.frequency);
  }

  /**
   * Get top errors by frequency
   */
  getTopErrors(limit: number = 10): ErrorAnalytics[] {
    return this.getAnalytics()
      .slice(0, limit);
  }

  /**
   * Clear analytics data
   */
  clear(): void {
    this.errorStats.clear();
    this.emit('analytics-cleared');
  }

  private updateAffectedUsers(existing: ErrorAnalytics, userId?: string): number {
    if (!userId) return existing.affectedUsers;
    
    // This is a simplified implementation
    // In a real system, you'd track unique user IDs
    return existing.affectedUsers + 1;
  }
}

/**
 * Error notification manager
 */
export class ErrorNotificationManager extends EventEmitter {
  private notifications: Map<string, ErrorNotification> = new Map();
  private config: any = {};

  configure(config: any): void {
    this.config = config;
  }

  /**
   * Create and show an error notification
   */
  notify(error: AppError): void {
    if (!this.shouldNotify(error)) {
      return;
    }

    const notification: ErrorNotification = {
      id: error.id,
      title: this.getNotificationTitle(error),
      message: error.details.userMessage || error.message,
      severity: error.severity,
      actions: this.getNotificationActions(error),
      dismissible: true,
      autoHide: this.getAutoHideDelay(error)
    };

    this.notifications.set(notification.id, notification);
    this.emit('notification-created', notification);

    // Auto-hide if configured
    if (notification.autoHide) {
      setTimeout(() => {
        this.dismiss(notification.id);
      }, notification.autoHide);
    }
  }

  /**
   * Dismiss a notification
   */
  dismiss(notificationId: string): void {
    const notification = this.notifications.get(notificationId);
    if (notification) {
      this.notifications.delete(notificationId);
      this.emit('notification-dismissed', notification);
    }
  }

  /**
   * Get all active notifications
   */
  getNotifications(): ErrorNotification[] {
    return Array.from(this.notifications.values());
  }

  /**
   * Clear all notifications
   */
  clearAll(): void {
    const notifications = Array.from(this.notifications.values());
    this.notifications.clear();
    this.emit('notifications-cleared', notifications);
  }

  private shouldNotify(error: AppError): boolean {
    // Don't notify for low severity errors unless configured
    if (error.severity === 'low' && !this.config.notifyLowSeverity) {
      return false;
    }

    // Don't notify for validation errors unless configured
    if (error.category === 'validation' && !this.config.notifyValidationErrors) {
      return false;
    }

    return true;
  }

  private getNotificationTitle(error: AppError): string {
    switch (error.severity) {
      case 'critical':
        return 'Critical Error';
      case 'high':
        return 'Error';
      case 'medium':
        return 'Warning';
      case 'low':
        return 'Notice';
      default:
        return 'Error';
    }
  }

  private getNotificationActions(error: AppError): ErrorAction[] {
    const actions: ErrorAction[] = [];

    // Add retry action for recoverable errors
    if (error.recoverable) {
      actions.push({
        id: 'retry',
        label: 'Retry',
        action: async () => {
          this.emit('retry-requested', error);
        }
      });
    }

    // Add help action if documentation is available
    if (error.details.documentation) {
      actions.push({
        id: 'help',
        label: 'Help',
        action: async () => {
          this.emit('help-requested', error);
        }
      });
    }

    return actions;
  }

  private getAutoHideDelay(error: AppError): number | undefined {
    switch (error.severity) {
      case 'low':
        return 3000; // 3 seconds
      case 'medium':
        return 5000; // 5 seconds
      case 'high':
      case 'critical':
        return undefined; // Don't auto-hide
      default:
        return 5000;
    }
  }
}
