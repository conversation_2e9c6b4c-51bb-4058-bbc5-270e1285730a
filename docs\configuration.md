# Configuration Module Specification

## 🎯 Purpose

The Configuration module provides centralized settings management for the PromptPilot Desktop application, handling user preferences, system settings, and application state with validation, persistence, and real-time updates.

## 📋 Responsibilities

### Settings Management
- Hierarchical configuration structure
- Type-safe setting definitions
- Default value management
- Setting validation and constraints

### Persistence
- Local configuration file storage
- Automatic backup and recovery
- Migration between configuration versions
- Import/export functionality

### Real-time Updates
- Configuration change notifications
- Hot-reloading of settings
- Validation before applying changes
- Rollback capabilities

## 🏗️ Architecture

### Core Configuration Manager
```typescript
class ConfigurationManager {
  private config: ConfigurationStore;
  private validators: Map<string, ConfigValidator> = new Map();
  private listeners: Map<string, ConfigChangeListener[]> = new Map();
  private schema: ConfigurationSchema;
  
  async load(): Promise<void>;
  async save(): Promise<void>;
  get<T>(key: string): T;
  set<T>(key: string, value: T): Promise<void>;
  reset(key?: string): Promise<void>;
  validate(key: string, value: any): ValidationResult;
  
  onChange(key: string, listener: ConfigChangeListener): () => void;
  export(options?: ExportOptions): Promise<string>;
  import(data: string, options?: ImportOptions): Promise<void>;
}
```

### Configuration Schema
```typescript
interface ConfigurationSchema {
  version: string;
  sections: {
    [sectionName: string]: ConfigSection;
  };
}

interface ConfigSection {
  title: string;
  description: string;
  settings: {
    [settingKey: string]: ConfigSetting;
  };
}

interface ConfigSetting {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'enum';
  title: string;
  description: string;
  defaultValue: any;
  required: boolean;
  validation?: ValidationRule[];
  options?: any[]; // For enum types
  sensitive?: boolean; // For passwords, API keys
  restart_required?: boolean;
  category: 'basic' | 'advanced' | 'expert';
}

interface ValidationRule {
  type: 'min' | 'max' | 'pattern' | 'custom';
  value?: any;
  message: string;
  validator?: (value: any) => boolean;
}
```

## ⚙️ Default Configuration

### Application Settings
```typescript
const DEFAULT_CONFIG: ConfigurationSchema = {
  version: '1.0.0',
  sections: {
    general: {
      title: 'General Settings',
      description: 'Basic application preferences',
      settings: {
        theme: {
          type: 'enum',
          title: 'Theme',
          description: 'Application color theme',
          defaultValue: 'system',
          required: true,
          options: ['light', 'dark', 'system'],
          category: 'basic'
        },
        language: {
          type: 'enum',
          title: 'Language',
          description: 'Application interface language',
          defaultValue: 'en',
          required: true,
          options: ['en', 'es', 'fr', 'de', 'zh', 'ja'],
          category: 'basic'
        },
        startup: {
          type: 'object',
          title: 'Startup Behavior',
          description: 'Application startup preferences',
          defaultValue: {
            autoStart: false,
            startMinimized: false,
            checkUpdates: true,
            restoreSession: true
          },
          required: true,
          category: 'basic'
        },
        notifications: {
          type: 'object',
          title: 'Notifications',
          description: 'Notification preferences',
          defaultValue: {
            enabled: true,
            sound: true,
            desktop: true,
            duration: 5000
          },
          required: true,
          category: 'basic'
        }
      }
    },
    
    hotkeys: {
      title: 'Hotkeys',
      description: 'Global keyboard shortcuts',
      settings: {
        showFloatingWindow: {
          type: 'string',
          title: 'Show Floating Window',
          description: 'Hotkey to show the floating prompt window',
          defaultValue: 'Ctrl+Shift+P',
          required: true,
          validation: [
            {
              type: 'pattern',
              value: /^(Ctrl|Alt|Shift|Cmd)(\+(Ctrl|Alt|Shift|Cmd))*\+[A-Z0-9]$/,
              message: 'Invalid hotkey format'
            }
          ],
          category: 'basic'
        },
        captureScreen: {
          type: 'string',
          title: 'Capture Screen',
          description: 'Hotkey to capture screen and extract text',
          defaultValue: 'Ctrl+Shift+O',
          required: true,
          category: 'basic'
        },
        startVoiceInput: {
          type: 'string',
          title: 'Start Voice Input',
          description: 'Hotkey to start voice recording',
          defaultValue: 'Ctrl+Shift+V',
          required: true,
          category: 'basic'
        },
        quickSearch: {
          type: 'string',
          title: 'Quick Search',
          description: 'Hotkey to open quick search',
          defaultValue: 'Ctrl+Shift+S',
          required: true,
          category: 'basic'
        }
      }
    },
    
    ai: {
      title: 'AI Settings',
      description: 'AI service configuration',
      settings: {
        defaultProvider: {
          type: 'enum',
          title: 'Default AI Provider',
          description: 'Primary AI service provider',
          defaultValue: 'openai',
          required: true,
          options: ['openai', 'anthropic', 'local'],
          category: 'basic'
        },
        openai: {
          type: 'object',
          title: 'OpenAI Configuration',
          description: 'OpenAI API settings',
          defaultValue: {
            apiKey: '',
            model: 'gpt-3.5-turbo',
            temperature: 0.7,
            maxTokens: 1000,
            timeout: 30000
          },
          required: false,
          category: 'advanced'
        },
        anthropic: {
          type: 'object',
          title: 'Anthropic Configuration',
          description: 'Anthropic API settings',
          defaultValue: {
            apiKey: '',
            model: 'claude-3-sonnet',
            temperature: 0.7,
            maxTokens: 1000,
            timeout: 30000
          },
          required: false,
          category: 'advanced'
        },
        enhancement: {
          type: 'object',
          title: 'Prompt Enhancement',
          description: 'Default enhancement settings',
          defaultValue: {
            style: 'professional',
            length: 'detailed',
            tone: 'friendly',
            autoEnhance: false,
            contextInjection: true
          },
          required: true,
          category: 'basic'
        }
      }
    },
    
    voice: {
      title: 'Voice Settings',
      description: 'Speech-to-text configuration',
      settings: {
        defaultProvider: {
          type: 'enum',
          title: 'STT Provider',
          description: 'Speech-to-text service provider',
          defaultValue: 'openai-whisper',
          required: true,
          options: ['openai-whisper', 'google', 'azure', 'local'],
          category: 'basic'
        },
        language: {
          type: 'enum',
          title: 'Recognition Language',
          description: 'Primary language for speech recognition',
          defaultValue: 'en',
          required: true,
          options: ['en', 'es', 'fr', 'de', 'zh', 'ja', 'auto'],
          category: 'basic'
        },
        recording: {
          type: 'object',
          title: 'Recording Settings',
          description: 'Audio recording preferences',
          defaultValue: {
            maxDuration: 300, // 5 minutes
            autoStop: true,
            noiseReduction: true,
            format: 'wav',
            sampleRate: 16000,
            channels: 1
          },
          required: true,
          category: 'advanced'
        },
        commands: {
          type: 'object',
          title: 'Voice Commands',
          description: 'Voice command settings',
          defaultValue: {
            enabled: true,
            sensitivity: 0.8,
            timeout: 5000,
            confirmationRequired: false
          },
          required: true,
          category: 'basic'
        }
      }
    },
    
    storage: {
      title: 'Storage & Sync',
      description: 'Data storage and synchronization',
      settings: {
        localPath: {
          type: 'string',
          title: 'Local Data Path',
          description: 'Directory for local data storage',
          defaultValue: '',
          required: false,
          category: 'advanced'
        },
        backup: {
          type: 'object',
          title: 'Backup Settings',
          description: 'Automatic backup configuration',
          defaultValue: {
            enabled: true,
            interval: 60, // minutes
            maxBackups: 10,
            compress: true,
            location: 'local'
          },
          required: true,
          category: 'advanced'
        },
        sync: {
          type: 'object',
          title: 'Cloud Sync',
          description: 'Cloud synchronization settings',
          defaultValue: {
            enabled: false,
            provider: 'none',
            autoSync: false,
            conflictResolution: 'manual'
          },
          required: false,
          category: 'expert'
        },
        encryption: {
          type: 'object',
          title: 'Data Encryption',
          description: 'Local data encryption settings',
          defaultValue: {
            enabled: false,
            algorithm: 'AES-256-GCM',
            keyDerivation: 'PBKDF2'
          },
          required: false,
          category: 'expert'
        }
      }
    },
    
    privacy: {
      title: 'Privacy & Security',
      description: 'Privacy and security preferences',
      settings: {
        analytics: {
          type: 'object',
          title: 'Analytics',
          description: 'Usage analytics and telemetry',
          defaultValue: {
            enabled: false,
            anonymous: true,
            crashReports: true,
            performanceMetrics: false
          },
          required: true,
          category: 'basic'
        },
        dataRetention: {
          type: 'object',
          title: 'Data Retention',
          description: 'How long to keep various types of data',
          defaultValue: {
            prompts: 0, // 0 = forever
            usage: 90, // days
            logs: 30, // days
            cache: 7 // days
          },
          required: true,
          category: 'advanced'
        },
        security: {
          type: 'object',
          title: 'Security Settings',
          description: 'Application security preferences',
          defaultValue: {
            requireAuth: false,
            sessionTimeout: 0, // 0 = no timeout
            clearClipboard: true,
            secureDelete: false
          },
          required: true,
          category: 'expert'
        }
      }
    },
    
    advanced: {
      title: 'Advanced Settings',
      description: 'Advanced configuration options',
      settings: {
        performance: {
          type: 'object',
          title: 'Performance',
          description: 'Performance optimization settings',
          defaultValue: {
            maxMemoryUsage: 512, // MB
            cacheSize: 100, // MB
            workerThreads: 0, // 0 = auto
            hardwareAcceleration: true
          },
          required: true,
          category: 'expert'
        },
        logging: {
          type: 'object',
          title: 'Logging',
          description: 'Application logging configuration',
          defaultValue: {
            level: 'info',
            file: true,
            console: false,
            maxFileSize: 10, // MB
            maxFiles: 5
          },
          required: true,
          category: 'expert'
        },
        experimental: {
          type: 'object',
          title: 'Experimental Features',
          description: 'Beta and experimental functionality',
          defaultValue: {
            enabled: false,
            features: []
          },
          required: false,
          category: 'expert'
        }
      }
    }
  }
};
```

## 🔧 Configuration Store

### File-based Storage
```typescript
class FileConfigurationStore implements ConfigurationStore {
  private configPath: string;
  private data: any = {};
  private watchers: fs.FSWatcher[] = [];
  
  constructor(configPath: string) {
    this.configPath = configPath;
  }
  
  async load(): Promise<any> {
    try {
      if (await this.exists()) {
        const content = await fs.readFile(this.configPath, 'utf-8');
        this.data = JSON.parse(content);
      } else {
        this.data = this.getDefaultConfig();
        await this.save();
      }
      
      this.setupFileWatcher();
      return this.data;
    } catch (error) {
      console.error('Failed to load configuration:', error);
      this.data = this.getDefaultConfig();
      return this.data;
    }
  }
  
  async save(): Promise<void> {
    try {
      const dir = path.dirname(this.configPath);
      await fs.mkdir(dir, { recursive: true });
      
      // Create backup before saving
      if (await this.exists()) {
        await this.createBackup();
      }
      
      const content = JSON.stringify(this.data, null, 2);
      await fs.writeFile(this.configPath, content, 'utf-8');
    } catch (error) {
      console.error('Failed to save configuration:', error);
      throw new ConfigurationError('Failed to save configuration', error);
    }
  }
  
  get(key: string): any {
    return this.getNestedValue(this.data, key);
  }
  
  set(key: string, value: any): void {
    this.setNestedValue(this.data, key, value);
  }
  
  has(key: string): boolean {
    return this.getNestedValue(this.data, key) !== undefined;
  }
  
  delete(key: string): void {
    this.deleteNestedValue(this.data, key);
  }
  
  private async exists(): Promise<boolean> {
    try {
      await fs.access(this.configPath);
      return true;
    } catch {
      return false;
    }
  }
  
  private async createBackup(): Promise<void> {
    const backupPath = `${this.configPath}.backup`;
    await fs.copyFile(this.configPath, backupPath);
  }
  
  private setupFileWatcher(): void {
    const watcher = fs.watch(this.configPath, (eventType) => {
      if (eventType === 'change') {
        this.handleFileChange();
      }
    });
    
    this.watchers.push(watcher);
  }
  
  private async handleFileChange(): Promise<void> {
    try {
      const content = await fs.readFile(this.configPath, 'utf-8');
      const newData = JSON.parse(content);
      
      // Compare and emit change events
      const changes = this.detectChanges(this.data, newData);
      this.data = newData;
      
      // Notify listeners of changes
      this.notifyChanges(changes);
    } catch (error) {
      console.error('Failed to handle configuration file change:', error);
    }
  }
  
  private getNestedValue(obj: any, key: string): any {
    return key.split('.').reduce((current, prop) => current?.[prop], obj);
  }
  
  private setNestedValue(obj: any, key: string, value: any): void {
    const keys = key.split('.');
    const lastKey = keys.pop()!;
    const target = keys.reduce((current, prop) => {
      if (!(prop in current)) {
        current[prop] = {};
      }
      return current[prop];
    }, obj);
    
    target[lastKey] = value;
  }
  
  private deleteNestedValue(obj: any, key: string): void {
    const keys = key.split('.');
    const lastKey = keys.pop()!;
    const target = keys.reduce((current, prop) => current?.[prop], obj);
    
    if (target && lastKey in target) {
      delete target[lastKey];
    }
  }
  
  private detectChanges(oldData: any, newData: any): ConfigChange[] {
    // Implementation to detect configuration changes
    return [];
  }
  
  private notifyChanges(changes: ConfigChange[]): void {
    // Implementation to notify configuration change listeners
  }
  
  private getDefaultConfig(): any {
    return this.flattenConfig(DEFAULT_CONFIG);
  }
  
  private flattenConfig(schema: ConfigurationSchema): any {
    const flattened: any = {};
    
    for (const [sectionName, section] of Object.entries(schema.sections)) {
      for (const [settingKey, setting] of Object.entries(section.settings)) {
        const fullKey = `${sectionName}.${settingKey}`;
        flattened[fullKey] = setting.defaultValue;
      }
    }
    
    return flattened;
  }
}
```

## 🧪 Testing Strategy

### Unit Tests
```typescript
describe('ConfigurationManager', () => {
  let configManager: ConfigurationManager;
  let mockStore: jest.Mocked<ConfigurationStore>;
  
  beforeEach(() => {
    mockStore = {
      load: jest.fn(),
      save: jest.fn(),
      get: jest.fn(),
      set: jest.fn(),
      has: jest.fn(),
      delete: jest.fn()
    };
    
    configManager = new ConfigurationManager(mockStore);
  });
  
  test('should load configuration on initialization', async () => {
    const mockConfig = { 'general.theme': 'dark' };
    mockStore.load.mockResolvedValue(mockConfig);
    
    await configManager.load();
    
    expect(mockStore.load).toHaveBeenCalled();
  });
  
  test('should validate setting values', async () => {
    const result = configManager.validate('hotkeys.showFloatingWindow', 'Ctrl+Shift+P');
    
    expect(result.valid).toBe(true);
  });
  
  test('should reject invalid hotkey format', async () => {
    const result = configManager.validate('hotkeys.showFloatingWindow', 'InvalidHotkey');
    
    expect(result.valid).toBe(false);
    expect(result.errors).toContain('Invalid hotkey format');
  });
});
```
