/**
 * Theme Manager
 * Manages window themes and styling
 */

import { WindowTheme } from './types';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('ThemeManager');

/**
 * Theme Manager implementation
 */
export class ThemeManager {
  private currentTheme: string;
  private themes: Map<string, WindowTheme> = new Map();

  constructor(initialTheme: string = 'light') {
    this.currentTheme = initialTheme;
    this.initializeBuiltInThemes();
  }

  /**
   * Initialize the theme manager
   */
  async initialize(): Promise<void> {
    await this.applyTheme(this.currentTheme);
    logger.info('Theme manager initialized', { theme: this.currentTheme });
  }

  /**
   * Set current theme
   */
  async setTheme(themeName: string): Promise<void> {
    if (!this.themes.has(themeName)) {
      logger.warn('Theme not found, using default', { themeName });
      themeName = 'light';
    }

    this.currentTheme = themeName;
    await this.applyTheme(themeName);
    
    logger.info('Theme changed', { theme: themeName });
  }

  /**
   * Get current theme
   */
  getCurrentTheme(): string {
    return this.currentTheme;
  }

  /**
   * Get theme by name
   */
  getTheme(themeName: string): WindowTheme | null {
    return this.themes.get(themeName) || null;
  }

  /**
   * Register custom theme
   */
  registerTheme(name: string, theme: WindowTheme): void {
    this.themes.set(name, theme);
    logger.info('Custom theme registered', { name });
  }

  /**
   * Get available themes
   */
  getAvailableThemes(): string[] {
    return Array.from(this.themes.keys());
  }

  /**
   * Apply theme to document
   */
  private async applyTheme(themeName: string): Promise<void> {
    const theme = this.themes.get(themeName);
    if (!theme) {
      logger.error('Theme not found', { themeName });
      return;
    }

    // Apply CSS custom properties
    const root = document.documentElement;
    
    // Colors
    root.style.setProperty('--color-primary', theme.colors.primary);
    root.style.setProperty('--color-secondary', theme.colors.secondary);
    root.style.setProperty('--color-background', theme.colors.background);
    root.style.setProperty('--color-surface', theme.colors.surface);
    root.style.setProperty('--color-text', theme.colors.text);
    root.style.setProperty('--color-text-secondary', theme.colors.textSecondary);
    root.style.setProperty('--color-border', theme.colors.border);
    root.style.setProperty('--color-shadow', theme.colors.shadow);
    root.style.setProperty('--color-accent', theme.colors.accent);
    root.style.setProperty('--color-success', theme.colors.success);
    root.style.setProperty('--color-warning', theme.colors.warning);
    root.style.setProperty('--color-error', theme.colors.error);

    // Typography
    root.style.setProperty('--font-family', theme.typography.fontFamily);
    root.style.setProperty('--font-size-small', theme.typography.fontSize.small);
    root.style.setProperty('--font-size-medium', theme.typography.fontSize.medium);
    root.style.setProperty('--font-size-large', theme.typography.fontSize.large);
    root.style.setProperty('--font-size-xlarge', theme.typography.fontSize.xlarge);

    // Spacing
    root.style.setProperty('--spacing-xs', theme.spacing.xs);
    root.style.setProperty('--spacing-sm', theme.spacing.sm);
    root.style.setProperty('--spacing-md', theme.spacing.md);
    root.style.setProperty('--spacing-lg', theme.spacing.lg);
    root.style.setProperty('--spacing-xl', theme.spacing.xl);

    // Border radius
    root.style.setProperty('--border-radius-sm', theme.borderRadius.sm);
    root.style.setProperty('--border-radius-md', theme.borderRadius.md);
    root.style.setProperty('--border-radius-lg', theme.borderRadius.lg);
    root.style.setProperty('--border-radius-full', theme.borderRadius.full);

    // Shadows
    root.style.setProperty('--shadow-sm', theme.shadows.sm);
    root.style.setProperty('--shadow-md', theme.shadows.md);
    root.style.setProperty('--shadow-lg', theme.shadows.lg);
    root.style.setProperty('--shadow-xl', theme.shadows.xl);
  }

  /**
   * Initialize built-in themes
   */
  private initializeBuiltInThemes(): void {
    // Light theme
    this.themes.set('light', {
      name: 'Light',
      colors: {
        primary: '#007bff',
        secondary: '#6c757d',
        background: '#ffffff',
        surface: '#f8f9fa',
        text: '#212529',
        textSecondary: '#6c757d',
        border: '#dee2e6',
        shadow: 'rgba(0, 0, 0, 0.1)',
        accent: '#007bff',
        success: '#28a745',
        warning: '#ffc107',
        error: '#dc3545'
      },
      typography: {
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        fontSize: {
          small: '12px',
          medium: '14px',
          large: '16px',
          xlarge: '18px'
        },
        fontWeight: {
          normal: '400',
          medium: '500',
          bold: '600'
        },
        lineHeight: {
          tight: '1.2',
          normal: '1.5',
          relaxed: '1.8'
        }
      },
      spacing: {
        xs: '4px',
        sm: '8px',
        md: '16px',
        lg: '24px',
        xl: '32px'
      },
      borderRadius: {
        sm: '2px',
        md: '4px',
        lg: '8px',
        full: '50%'
      },
      shadows: {
        sm: '0 1px 2px rgba(0, 0, 0, 0.05)',
        md: '0 4px 6px rgba(0, 0, 0, 0.1)',
        lg: '0 10px 15px rgba(0, 0, 0, 0.1)',
        xl: '0 20px 25px rgba(0, 0, 0, 0.15)'
      }
    });

    // Dark theme
    this.themes.set('dark', {
      name: 'Dark',
      colors: {
        primary: '#0d6efd',
        secondary: '#6c757d',
        background: '#1a1a1a',
        surface: '#2d2d2d',
        text: '#ffffff',
        textSecondary: '#adb5bd',
        border: '#495057',
        shadow: 'rgba(0, 0, 0, 0.3)',
        accent: '#0d6efd',
        success: '#198754',
        warning: '#fd7e14',
        error: '#dc3545'
      },
      typography: {
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        fontSize: {
          small: '12px',
          medium: '14px',
          large: '16px',
          xlarge: '18px'
        },
        fontWeight: {
          normal: '400',
          medium: '500',
          bold: '600'
        },
        lineHeight: {
          tight: '1.2',
          normal: '1.5',
          relaxed: '1.8'
        }
      },
      spacing: {
        xs: '4px',
        sm: '8px',
        md: '16px',
        lg: '24px',
        xl: '32px'
      },
      borderRadius: {
        sm: '2px',
        md: '4px',
        lg: '8px',
        full: '50%'
      },
      shadows: {
        sm: '0 1px 2px rgba(0, 0, 0, 0.2)',
        md: '0 4px 6px rgba(0, 0, 0, 0.3)',
        lg: '0 10px 15px rgba(0, 0, 0, 0.3)',
        xl: '0 20px 25px rgba(0, 0, 0, 0.4)'
      }
    });

    // Auto theme (system preference)
    this.themes.set('auto', this.getSystemTheme());
  }

  /**
   * Get system theme based on user preference
   */
  private getSystemTheme(): WindowTheme {
    const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
    return this.themes.get(prefersDark ? 'dark' : 'light')!;
  }
}
