/**
 * Conflict Resolver
 * Detects and resolves hotkey conflicts
 */

import { ConflictResult, ConflictInfo } from './types';
import { LoggerFactory } from '../../core/logger';

// const logger = LoggerFactory.getInstance().getLogger('ConflictResolver');

/**
 * Conflict Resolver implementation
 */
export class ConflictResolver {
  private platform: string;
  private systemHotkeys: Map<string, ConflictInfo> = new Map();

  constructor(platform: string) {
    this.platform = platform;
    this.initializeSystemHotkeys();
  }

  /**
   * Check for conflicts
   */
  async checkConflicts(accelerator: string): Promise<ConflictResult> {
    const conflicts: ConflictInfo[] = [];
    const suggestions: string[] = [];

    // Check system hotkeys
    const systemConflict = this.systemHotkeys.get(accelerator);
    if (systemConflict) {
      conflicts.push(systemConflict);
    }

    // Check common application conflicts
    const appConflicts = this.checkApplicationConflicts(accelerator);
    conflicts.push(...appConflicts);

    // Generate suggestions if conflicts found
    if (conflicts.length > 0) {
      suggestions.push(...this.generateSuggestions(accelerator));
    }

    return {
      hasConflicts: conflicts.length > 0,
      conflicts,
      suggestions,
      canOverride: this.canOverride(conflicts)
    };
  }

  /**
   * Initialize known system hotkeys
   */
  private initializeSystemHotkeys(): void {
    switch (this.platform) {
      case 'win32':
        this.initializeWindowsSystemHotkeys();
        break;
      case 'darwin':
        this.initializeMacOSSystemHotkeys();
        break;
      case 'linux':
        this.initializeLinuxSystemHotkeys();
        break;
    }
  }

  /**
   * Initialize Windows system hotkeys
   */
  private initializeWindowsSystemHotkeys(): void {
    const windowsHotkeys = [
      { key: 'Ctrl+C', desc: 'Copy', severity: 'high' as const },
      { key: 'Ctrl+V', desc: 'Paste', severity: 'high' as const },
      { key: 'Ctrl+X', desc: 'Cut', severity: 'high' as const },
      { key: 'Ctrl+Z', desc: 'Undo', severity: 'high' as const },
      { key: 'Ctrl+Y', desc: 'Redo', severity: 'high' as const },
      { key: 'Ctrl+A', desc: 'Select All', severity: 'high' as const },
      { key: 'Ctrl+S', desc: 'Save', severity: 'high' as const },
      { key: 'Ctrl+O', desc: 'Open', severity: 'medium' as const },
      { key: 'Ctrl+N', desc: 'New', severity: 'medium' as const },
      { key: 'Ctrl+P', desc: 'Print', severity: 'medium' as const },
      { key: 'Alt+Tab', desc: 'Switch Windows', severity: 'high' as const },
      { key: 'Alt+F4', desc: 'Close Window', severity: 'high' as const },
      { key: 'Win+L', desc: 'Lock Screen', severity: 'high' as const },
      { key: 'Win+D', desc: 'Show Desktop', severity: 'medium' as const },
      { key: 'Win+R', desc: 'Run Dialog', severity: 'medium' as const }
    ];

    for (const hotkey of windowsHotkeys) {
      this.systemHotkeys.set(hotkey.key, {
        type: 'system',
        description: `Windows system shortcut: ${hotkey.desc}`,
        severity: hotkey.severity,
        source: 'Windows'
      });
    }
  }

  /**
   * Initialize macOS system hotkeys
   */
  private initializeMacOSSystemHotkeys(): void {
    const macHotkeys = [
      { key: 'Cmd+C', desc: 'Copy', severity: 'high' as const },
      { key: 'Cmd+V', desc: 'Paste', severity: 'high' as const },
      { key: 'Cmd+X', desc: 'Cut', severity: 'high' as const },
      { key: 'Cmd+Z', desc: 'Undo', severity: 'high' as const },
      { key: 'Cmd+Shift+Z', desc: 'Redo', severity: 'high' as const },
      { key: 'Cmd+A', desc: 'Select All', severity: 'high' as const },
      { key: 'Cmd+S', desc: 'Save', severity: 'high' as const },
      { key: 'Cmd+O', desc: 'Open', severity: 'medium' as const },
      { key: 'Cmd+N', desc: 'New', severity: 'medium' as const },
      { key: 'Cmd+P', desc: 'Print', severity: 'medium' as const },
      { key: 'Cmd+Tab', desc: 'Switch Apps', severity: 'high' as const },
      { key: 'Cmd+Q', desc: 'Quit App', severity: 'high' as const },
      { key: 'Cmd+W', desc: 'Close Window', severity: 'high' as const },
      { key: 'Cmd+Space', desc: 'Spotlight', severity: 'high' as const }
    ];

    for (const hotkey of macHotkeys) {
      this.systemHotkeys.set(hotkey.key, {
        type: 'system',
        description: `macOS system shortcut: ${hotkey.desc}`,
        severity: hotkey.severity,
        source: 'macOS'
      });
    }
  }

  /**
   * Initialize Linux system hotkeys
   */
  private initializeLinuxSystemHotkeys(): void {
    const linuxHotkeys = [
      { key: 'Ctrl+C', desc: 'Copy', severity: 'high' as const },
      { key: 'Ctrl+V', desc: 'Paste', severity: 'high' as const },
      { key: 'Ctrl+X', desc: 'Cut', severity: 'high' as const },
      { key: 'Ctrl+Z', desc: 'Undo', severity: 'high' as const },
      { key: 'Ctrl+Y', desc: 'Redo', severity: 'high' as const },
      { key: 'Ctrl+A', desc: 'Select All', severity: 'high' as const },
      { key: 'Ctrl+S', desc: 'Save', severity: 'high' as const },
      { key: 'Ctrl+O', desc: 'Open', severity: 'medium' as const },
      { key: 'Ctrl+N', desc: 'New', severity: 'medium' as const },
      { key: 'Ctrl+P', desc: 'Print', severity: 'medium' as const },
      { key: 'Alt+Tab', desc: 'Switch Windows', severity: 'high' as const },
      { key: 'Alt+F4', desc: 'Close Window', severity: 'high' as const },
      { key: 'Super+L', desc: 'Lock Screen', severity: 'high' as const }
    ];

    for (const hotkey of linuxHotkeys) {
      this.systemHotkeys.set(hotkey.key, {
        type: 'system',
        description: `Linux system shortcut: ${hotkey.desc}`,
        severity: hotkey.severity,
        source: 'Linux'
      });
    }
  }

  /**
   * Check for application conflicts
   */
  private checkApplicationConflicts(accelerator: string): ConflictInfo[] {
    const conflicts: ConflictInfo[] = [];

    // Common application shortcuts that might conflict
    const commonAppShortcuts = [
      'Ctrl+Shift+I', // Developer Tools
      'Ctrl+Shift+J', // Console
      'Ctrl+Shift+C', // Inspect Element
      'Ctrl+Shift+R', // Hard Refresh
      'Ctrl+Shift+T', // Reopen Tab
      'Ctrl+Shift+N', // New Incognito Window
      'F12', // Developer Tools
      'F5', // Refresh
      'Ctrl+F5' // Hard Refresh
    ];

    if (commonAppShortcuts.includes(accelerator)) {
      conflicts.push({
        type: 'application',
        description: `Common application shortcut: ${accelerator}`,
        severity: 'medium',
        source: 'Common Applications'
      });
    }

    return conflicts;
  }

  /**
   * Generate alternative suggestions
   */
  private generateSuggestions(accelerator: string): string[] {
    const suggestions: string[] = [];
    const parts = accelerator.split('+');
    
    if (parts.length < 2) {
      return suggestions;
    }

    const modifiers = parts.slice(0, -1);
    const key = parts[parts.length - 1];

    // Suggest adding more modifiers
    const availableModifiers = this.getAvailableModifiers();
    for (const modifier of availableModifiers) {
      if (!modifiers.includes(modifier)) {
        const suggestion = [...modifiers, modifier, key].join('+');
        if (!this.systemHotkeys.has(suggestion)) {
          suggestions.push(suggestion);
        }
      }
    }

    // Suggest alternative keys
    const alternativeKeys = this.getAlternativeKeys(key);
    for (const altKey of alternativeKeys) {
      const suggestion = [...modifiers, altKey].join('+');
      if (!this.systemHotkeys.has(suggestion)) {
        suggestions.push(suggestion);
      }
    }

    return suggestions.slice(0, 5); // Limit to 5 suggestions
  }

  /**
   * Get available modifiers for platform
   */
  private getAvailableModifiers(): string[] {
    switch (this.platform) {
      case 'win32':
        return ['Ctrl', 'Alt', 'Shift', 'Win'];
      case 'darwin':
        return ['Cmd', 'Ctrl', 'Alt', 'Shift'];
      case 'linux':
        return ['Ctrl', 'Alt', 'Shift', 'Super'];
      default:
        return ['Ctrl', 'Alt', 'Shift'];
    }
  }

  /**
   * Get alternative keys
   */
  private getAlternativeKeys(key: string): string[] {
    const alternatives: Record<string, string[]> = {
      'P': ['O', 'I', 'U'],
      'O': ['P', 'I', 'U'],
      'V': ['B', 'N', 'M'],
      'H': ['G', 'J', 'K'],
      'S': ['A', 'D', 'F'],
      'F1': ['F2', 'F3', 'F4'],
      'F2': ['F1', 'F3', 'F4'],
      'F3': ['F2', 'F4', 'F5'],
      'F4': ['F3', 'F5', 'F6']
    };

    return alternatives[key] || [];
  }

  /**
   * Check if conflicts can be overridden
   */
  private canOverride(conflicts: ConflictInfo[]): boolean {
    // Can't override high severity system conflicts
    return !conflicts.some(conflict => 
      conflict.type === 'system' && conflict.severity === 'high'
    );
  }
}
