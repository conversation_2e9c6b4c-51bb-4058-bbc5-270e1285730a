/**
 * SQLite database connection wrapper
 * Provides a unified interface for database operations with transaction support
 */

import Database from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import { DatabaseConnection, Transaction, PreparedStatement, DatabaseConfig } from './types';
import { LoggerFactory } from '../core/logger';

const logger = LoggerFactory.getInstance().getLogger('DatabaseConnection');

/**
 * Transaction implementation
 */
class DatabaseTransaction implements Transaction {
  public readonly id: string;
  public readonly startTime: Date;
  private _isActive: boolean = true;

  constructor(private db: Database.Database) {
    this.id = uuidv4();
    this.startTime = new Date();
  }

  async commit(): Promise<void> {
    if (!this._isActive) {
      throw new Error('Transaction is not active');
    }

    try {
      this.db.exec('COMMIT');
      this._isActive = false;
      logger.debug('Transaction committed', { transactionId: this.id });
    } catch (error) {
      logger.error('Failed to commit transaction', { transactionId: this.id, error });
      throw error;
    }
  }

  async rollback(): Promise<void> {
    if (!this._isActive) {
      return; // Already rolled back or committed
    }

    try {
      this.db.exec('ROLLBACK');
      this._isActive = false;
      logger.debug('Transaction rolled back', { transactionId: this.id });
    } catch (error) {
      logger.error('Failed to rollback transaction', { transactionId: this.id, error });
      throw error;
    }
  }

  isActive(): boolean {
    return this._isActive;
  }
}

/**
 * Prepared statement wrapper
 */
class DatabasePreparedStatement implements PreparedStatement {
  constructor(private stmt: Database.Statement) {}

  async run(params?: any[]): Promise<{ lastID?: number; changes: number }> {
    try {
      const result = this.stmt.run(params || []);

      // Handle case where result might be undefined
      if (!result) {
        return { changes: 0 };
      }

      return {
        lastID: result.lastInsertRowid as number,
        changes: result.changes || 0
      };
    } catch (error) {
      logger.error('Prepared statement run failed', { error, params });
      throw error;
    }
  }

  async get<T = any>(params?: any[]): Promise<T | undefined> {
    try {
      return this.stmt.get(params || []) as T;
    } catch (error) {
      logger.error('Prepared statement get failed', { error, params });
      throw error;
    }
  }

  async all<T = any>(params?: any[]): Promise<T[]> {
    try {
      const results = this.stmt.all(params || []) as T[];
      return results || [];
    } catch (error) {
      logger.error('Prepared statement all failed', { error, params });
      throw error;
    }
  }

  async finalize(): Promise<void> {
    try {
      // better-sqlite3 doesn't need explicit finalization
      // The statement will be garbage collected
    } catch (error) {
      logger.error('Failed to finalize prepared statement', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }
}

/**
 * SQLite database connection implementation
 */
export class SQLiteDatabaseConnection implements DatabaseConnection {
  private db: Database.Database | null = null;
  private config: DatabaseConfig;
  private isInitialized: boolean = false;

  constructor(config: DatabaseConfig) {
    this.config = config;
  }

  /**
   * Initialize the database connection
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Open database connection
      this.db = new Database(this.config.path);

      // Configure database performance settings
      this.configureDatabase();

      this.isInitialized = true;
      logger.info('Database connection initialized', { path: this.config.path });
    } catch (error) {
      logger.error('Failed to initialize database connection', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  /**
   * Run a SQL statement
   */
  async run(sql: string, params?: any[]): Promise<{ lastID?: number; changes: number }> {
    this.ensureInitialized();

    try {
      const result = this.db!.prepare(sql).run(params || []);

      // Handle case where result might be undefined
      if (!result) {
        return { changes: 0 };
      }

      logger.debug('SQL run executed', { sql, params, changes: result.changes });

      return {
        lastID: result.lastInsertRowid as number,
        changes: result.changes || 0
      };
    } catch (error) {
      logger.error('SQL run failed', { sql, params, error });
      throw error;
    }
  }

  /**
   * Get a single row
   */
  async get<T = any>(sql: string, params?: any[]): Promise<T | undefined> {
    this.ensureInitialized();

    try {
      const result = this.db!.prepare(sql).get(params || []) as T;
      logger.debug('SQL get executed', { sql, params, hasResult: !!result });
      return result;
    } catch (error) {
      logger.error('SQL get failed', { sql, params, error });
      throw error;
    }
  }

  /**
   * Get all rows
   */
  async all<T = any>(sql: string, params?: any[]): Promise<T[]> {
    this.ensureInitialized();

    try {
      const results = this.db!.prepare(sql).all(params || []) as T[];
      const safeResults = results || [];
      logger.debug('SQL all executed', { sql, params, rowCount: safeResults.length });
      return safeResults;
    } catch (error) {
      logger.error('SQL all failed', { sql, params, error });
      throw error;
    }
  }

  /**
   * Execute SQL without returning results
   */
  async exec(sql: string): Promise<void> {
    this.ensureInitialized();

    try {
      this.db!.exec(sql);
      logger.debug('SQL exec executed', { sql });
    } catch (error) {
      logger.error('SQL exec failed', { sql, error });
      throw error;
    }
  }

  /**
   * Prepare a SQL statement
   */
  async prepare(sql: string): Promise<PreparedStatement> {
    this.ensureInitialized();

    try {
      const stmt = this.db!.prepare(sql);
      logger.debug('SQL statement prepared', { sql });
      return new DatabasePreparedStatement(stmt);
    } catch (error) {
      logger.error('SQL prepare failed', { sql, error });
      throw error;
    }
  }

  /**
   * Close the database connection
   */
  async close(): Promise<void> {
    if (!this.isInitialized || !this.db) {
      return;
    }

    try {
      this.db.close();
      this.db = null;
      this.isInitialized = false;
      logger.info('Database connection closed');
    } catch (error) {
      logger.error('Failed to close database connection', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  /**
   * Create a database backup
   */
  async backup(destination: string): Promise<void> {
    this.ensureInitialized();

    try {
      // SQLite backup using VACUUM INTO
      this.db!.exec(`VACUUM INTO '${destination}'`);
      logger.info('Database backup created', { destination });
    } catch (error) {
      logger.error('Database backup failed', { destination, error });
      throw error;
    }
  }

  /**
   * Execute operations within a transaction
   */
  async transaction<T>(fn: (tx: Transaction) => Promise<T>): Promise<T> {
    this.ensureInitialized();

    const transaction = new DatabaseTransaction(this.db!);

    try {
      this.db!.exec('BEGIN TRANSACTION');
      logger.debug('Transaction started', { transactionId: transaction.id });

      const result = await fn(transaction);

      if (transaction.isActive()) {
        await transaction.commit();
      }

      return result;
    } catch (error) {
      if (transaction.isActive()) {
        await transaction.rollback();
      }
      logger.error('Transaction failed', { transactionId: transaction.id, error });
      throw error;
    }
  }

  /**
   * Configure database performance settings
   */
  private configureDatabase(): void {
    const { performance } = this.config;

    // Set journal mode
    this.db!.exec(`PRAGMA journal_mode = ${performance.journalMode}`);

    // Set synchronous mode
    this.db!.exec(`PRAGMA synchronous = ${performance.synchronous}`);

    // Set cache size
    this.db!.exec(`PRAGMA cache_size = ${performance.cacheSize}`);

    // Set memory-mapped I/O size
    this.db!.exec(`PRAGMA mmap_size = ${performance.mmapSize}`);

    // Enable foreign key constraints
    this.db!.exec('PRAGMA foreign_keys = ON');

    // Set busy timeout
    this.db!.exec('PRAGMA busy_timeout = 30000');

    logger.debug('Database configured', { performance });
  }

  /**
   * Ensure database is initialized
   */
  private ensureInitialized(): void {
    if (!this.isInitialized || !this.db) {
      throw new Error('Database connection not initialized');
    }
  }
}

/**
 * Factory function to create database connection
 */
export const createDatabaseConnection = (config: DatabaseConfig): SQLiteDatabaseConnection => {
  return new SQLiteDatabaseConnection(config);
};
