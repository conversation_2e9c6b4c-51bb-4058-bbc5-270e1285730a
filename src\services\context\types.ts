/**
 * Context Extractor Types and Interfaces
 * Defines all types used by the context extraction service
 */

// Screen capture types
export interface ScreenRegion {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface ScreenCaptureOptions {
  region?: ScreenRegion;
  format?: 'png' | 'jpg' | 'webp';
  quality?: number;
  includeOCR?: boolean;
  ocrLanguage?: string;
}

export interface DisplayInfo {
  displays: Array<{
    id: number;
    bounds: ScreenRegion;
    workArea: ScreenRegion;
    scaleFactor: number;
  }>;
}

export interface CaptureResult {
  imagePath: string;
  extractedText?: string;
  metadata: {
    timestamp: Date;
    region: ScreenRegion;
    displayInfo: DisplayInfo;
  };
}

// OCR types
export interface OCROptions {
  language?: string;
  pageSegMode?: number;
  ocrEngineMode?: number;
  whitelist?: string;
  blacklist?: string;
  preserveInterwordSpaces?: boolean;
}

export interface OCREngine {
  extractText(imagePath: string, options?: OCROptions): Promise<string>;
  extractTextFromBuffer(imageBuffer: Buffer, options?: OCROptions): Promise<string>;
  getSupportedLanguages(): string[];
  detectLanguage(imagePath: string): Promise<string>;
}

// Context extraction types
export interface ContextExtractionOptions {
  includeClipboard?: boolean;
  includeActiveWindow?: boolean;
  includeScreenCapture?: boolean;
  screenCaptureOptions?: ScreenCaptureOptions;
  maxTextLength?: number;
}

export interface ExtractedContext {
  clipboard?: string;
  activeWindow?: {
    title: string;
    content: string;
    processName?: string;
  };
  screenCapture?: CaptureResult;
  timestamp: Date;
  metadata: {
    source: string[];
    totalLength: number;
    language?: string;
  };
}

// File content types
export interface FileContentOptions {
  maxSize?: number;
  encoding?: BufferEncoding;
  preview?: boolean;
  previewLines?: number;
}

export interface FileContent {
  path: string;
  content: string;
  metadata: {
    size: number;
    extension: string;
    mimeType?: string;
    encoding: string;
    lastModified: Date;
  };
}

// Clipboard monitoring types
export interface ClipboardContent {
  text: string;
  timestamp: Date;
  source?: string;
}

export type ClipboardChangeListener = (content: ClipboardContent) => void;

// Window extraction types
export interface WindowInfo {
  title: string;
  processName: string;
  pid: number;
  bounds?: ScreenRegion;
}

export interface WindowContent {
  info: WindowInfo;
  text: string;
  url?: string;
  metadata: {
    extractionMethod: 'web' | 'native' | 'accessibility';
    timestamp: Date;
    confidence: number;
  };
}

// Error types
export class ContextExtractionError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly source: string,
    public readonly originalError?: Error
  ) {
    super(message);
    this.name = 'ContextExtractionError';
  }
}

export enum ContextErrorCode {
  SCREEN_CAPTURE_FAILED = 'SCREEN_CAPTURE_FAILED',
  OCR_FAILED = 'OCR_FAILED',
  CLIPBOARD_ACCESS_DENIED = 'CLIPBOARD_ACCESS_DENIED',
  WINDOW_ACCESS_DENIED = 'WINDOW_ACCESS_DENIED',
  FILE_READ_ERROR = 'FILE_READ_ERROR',
  UNSUPPORTED_FORMAT = 'UNSUPPORTED_FORMAT',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  INITIALIZATION_FAILED = 'INITIALIZATION_FAILED'
}

// Configuration types
export interface ContextExtractorConfig {
  ocr: {
    enabled: boolean;
    defaultLanguage: string;
    supportedLanguages: string[];
    confidence: number;
  };
  clipboard: {
    monitoringEnabled: boolean;
    monitoringInterval: number;
    maxHistorySize: number;
  };
  screenCapture: {
    defaultFormat: 'png' | 'jpg' | 'webp';
    defaultQuality: number;
    tempDirectory: string;
    cleanupInterval: number;
  };
  fileReader: {
    maxFileSize: number;
    supportedExtensions: string[];
    previewLines: number;
  };
  security: {
    requirePermissions: boolean;
    allowedSources: string[];
    sanitizeContent: boolean;
  };
}

// Service interfaces
export interface IScreenCaptureService {
  captureFullScreen(options?: ScreenCaptureOptions): Promise<CaptureResult>;
  captureRegion(region: ScreenRegion, options?: ScreenCaptureOptions): Promise<CaptureResult>;
  captureActiveWindow(options?: ScreenCaptureOptions): Promise<CaptureResult>;
  getDisplays(): DisplayInfo;
}

export interface IClipboardMonitor {
  startMonitoring(intervalMs?: number): void;
  stopMonitoring(): void;
  getCurrentContent(): ClipboardContent;
  getHistory(): ClipboardContent[];
  onContentChange(listener: ClipboardChangeListener): () => void;
  clearHistory(): void;
}

export interface IWindowTextExtractor {
  getActiveWindowText(): Promise<WindowContent>;
  getWindowText(windowId: number): Promise<WindowContent>;
  getAllWindows(): Promise<WindowInfo[]>;
  isWindowAccessible(windowId: number): Promise<boolean>;
}

export interface IFileContentReader {
  readFile(filePath: string, options?: FileContentOptions): Promise<FileContent>;
  readMultipleFiles(filePaths: string[], options?: FileContentOptions): Promise<FileContent[]>;
  getSupportedExtensions(): string[];
  isFileSupported(filePath: string): boolean;
}

export interface IContextExtractor {
  extractContext(options?: ContextExtractionOptions): Promise<ExtractedContext>;
  captureScreen(region?: ScreenRegion): Promise<string>;
  getClipboardText(): Promise<string>;
  getActiveWindowText(): Promise<string>;
  extractFromFile(filePath: string): Promise<string>;
  extractFromUrl(url: string): Promise<string>;
  configure(config: Partial<ContextExtractorConfig>): void;
  getConfig(): ContextExtractorConfig;
}
