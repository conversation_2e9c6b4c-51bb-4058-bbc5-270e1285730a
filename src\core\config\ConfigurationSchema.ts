/**
 * Configuration schema definitions and default values
 * Defines the structure and validation rules for all application settings
 */

import { ConfigurationSchema } from './types';

export const DEFAULT_CONFIG_SCHEMA: ConfigurationSchema = {
  version: '1.0.0',
  sections: {
    general: {
      title: 'General Settings',
      description: 'Basic application preferences',
      settings: {
        theme: {
          type: 'enum',
          title: 'Theme',
          description: 'Application color theme',
          defaultValue: 'system',
          required: true,
          options: ['light', 'dark', 'system'],
          category: 'basic'
        },
        language: {
          type: 'enum',
          title: 'Language',
          description: 'Application interface language',
          defaultValue: 'en',
          required: true,
          options: ['en', 'es', 'fr', 'de', 'zh', 'ja'],
          category: 'basic'
        },
        startup: {
          type: 'object',
          title: 'Startup Behavior',
          description: 'Application startup preferences',
          defaultValue: {
            autoStart: false,
            startMinimized: false,
            checkUpdates: true,
            restoreSession: true
          },
          required: true,
          category: 'basic'
        },
        notifications: {
          type: 'object',
          title: 'Notifications',
          description: 'Notification preferences',
          defaultValue: {
            enabled: true,
            sound: true,
            desktop: true,
            duration: 5000
          },
          required: true,
          category: 'basic'
        }
      }
    },

    hotkeys: {
      title: 'Hotkeys',
      description: 'Global keyboard shortcuts',
      settings: {
        showFloatingWindow: {
          type: 'string',
          title: 'Show Floating Window',
          description: 'Hotkey to show the floating prompt window',
          defaultValue: 'Ctrl+Shift+P',
          required: true,
          validation: [
            {
              type: 'pattern',
              value: /^(Ctrl|Alt|Shift|Cmd)(\+(Ctrl|Alt|Shift|Cmd))*\+[A-Z0-9]+$/,
              message: 'Invalid hotkey format'
            }
          ],
          category: 'basic'
        },
        captureScreen: {
          type: 'string',
          title: 'Capture Screen',
          description: 'Hotkey to capture screen and extract text',
          defaultValue: 'Ctrl+Shift+O',
          required: true,
          category: 'basic'
        },
        startVoiceInput: {
          type: 'string',
          title: 'Start Voice Input',
          description: 'Hotkey to start voice recording',
          defaultValue: 'Ctrl+Shift+V',
          required: true,
          category: 'basic'
        },
        quickSearch: {
          type: 'string',
          title: 'Quick Search',
          description: 'Hotkey to open quick search',
          defaultValue: 'Ctrl+Shift+S',
          required: true,
          category: 'basic'
        }
      }
    },

    ai: {
      title: 'AI Settings',
      description: 'AI service configuration',
      settings: {
        defaultProvider: {
          type: 'enum',
          title: 'Default AI Provider',
          description: 'Primary AI service provider',
          defaultValue: 'openai',
          required: true,
          options: ['openai', 'anthropic', 'local'],
          category: 'basic'
        },
        openai: {
          type: 'object',
          title: 'OpenAI Configuration',
          description: 'OpenAI API settings',
          defaultValue: {
            apiKey: '',
            model: 'gpt-3.5-turbo',
            temperature: 0.7,
            maxTokens: 1000,
            timeout: 30000
          },
          required: false,
          category: 'advanced'
        },
        anthropic: {
          type: 'object',
          title: 'Anthropic Configuration',
          description: 'Anthropic API settings',
          defaultValue: {
            apiKey: '',
            model: 'claude-3-sonnet',
            temperature: 0.7,
            maxTokens: 1000,
            timeout: 30000
          },
          required: false,
          category: 'advanced'
        },
        enhancement: {
          type: 'object',
          title: 'Prompt Enhancement',
          description: 'Default enhancement settings',
          defaultValue: {
            style: 'professional',
            length: 'detailed',
            tone: 'friendly',
            autoEnhance: false,
            contextInjection: true
          },
          required: true,
          category: 'basic'
        }
      }
    },

    voice: {
      title: 'Voice Settings',
      description: 'Speech-to-text configuration',
      settings: {
        defaultProvider: {
          type: 'enum',
          title: 'STT Provider',
          description: 'Speech-to-text service provider',
          defaultValue: 'openai-whisper',
          required: true,
          options: ['openai-whisper', 'google', 'azure', 'local'],
          category: 'basic'
        },
        language: {
          type: 'enum',
          title: 'Recognition Language',
          description: 'Primary language for speech recognition',
          defaultValue: 'en',
          required: true,
          options: ['en', 'es', 'fr', 'de', 'zh', 'ja', 'auto'],
          category: 'basic'
        },
        recording: {
          type: 'object',
          title: 'Recording Settings',
          description: 'Audio recording preferences',
          defaultValue: {
            maxDuration: 300,
            autoStop: true,
            noiseReduction: true,
            format: 'wav',
            sampleRate: 16000,
            channels: 1
          },
          required: true,
          category: 'advanced'
        },
        commands: {
          type: 'object',
          title: 'Voice Commands',
          description: 'Voice command settings',
          defaultValue: {
            enabled: true,
            sensitivity: 0.8,
            timeout: 5000,
            confirmationRequired: false
          },
          required: true,
          category: 'basic'
        }
      }
    }
  }
};

/**
 * Get default configuration values from schema
 */
export function getDefaultConfig(): any {
  const config: any = {};
  
  for (const [sectionName, section] of Object.entries(DEFAULT_CONFIG_SCHEMA.sections)) {
    config[sectionName] = {};
    
    for (const [settingKey, setting] of Object.entries(section.settings)) {
      config[sectionName][settingKey] = setting.defaultValue;
    }
  }
  
  return config;
}

/**
 * Get configuration setting by path
 */
export function getSettingByPath(path: string): any {
  const parts = path.split('.');
  if (parts.length < 2) {
    throw new Error('Invalid setting path. Expected format: section.setting');
  }
  
  const [sectionName, settingKey] = parts;
  const section = DEFAULT_CONFIG_SCHEMA.sections[sectionName];
  
  if (!section) {
    throw new Error(`Unknown configuration section: ${sectionName}`);
  }
  
  const setting = section.settings[settingKey];
  if (!setting) {
    throw new Error(`Unknown setting: ${settingKey} in section ${sectionName}`);
  }
  
  return setting;
}
