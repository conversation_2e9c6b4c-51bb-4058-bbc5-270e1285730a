/**
 * Audio Processor implementation
 * Handles audio format conversion, noise reduction, and volume normalization
 */

import {
  IAudioProcessor,
  AudioProcessingOptions,
  VoiceModuleError
} from './types';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('AudioProcessor');

/**
 * Audio Processor implementation using Web Audio API
 */
export class AudioProcessor implements IAudioProcessor {
  private audioContext: AudioContext | null = null;

  constructor() {
    this.initializeAudioContext();
  }

  /**
   * Initialize audio context
   */
  private initializeAudioContext(): void {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    } catch (error) {
      logger.error('Failed to initialize audio context', { error });
    }
  }

  /**
   * Convert audio format
   */
  async convertFormat(inputBlob: Blob, outputFormat: 'wav' | 'mp3' | 'ogg'): Promise<Blob> {
    try {
      if (!this.audioContext) {
        throw new VoiceModuleError('Audio context not available');
      }

      // For now, return the original blob as format conversion
      // requires additional libraries like lamejs for MP3 encoding
      logger.warn('Format conversion not fully implemented, returning original blob');
      return inputBlob;
    } catch (error) {
      logger.error('Format conversion failed', { error, outputFormat });
      throw new VoiceModuleError('Format conversion failed', error);
    }
  }

  /**
   * Reduce noise in audio
   */
  async reduceNoise(audioBlob: Blob): Promise<Blob> {
    try {
      if (!this.audioContext) {
        throw new VoiceModuleError('Audio context not available');
      }

      const arrayBuffer = await audioBlob.arrayBuffer();
      const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);

      // Create offline context for processing
      const offlineContext = new OfflineAudioContext(
        audioBuffer.numberOfChannels,
        audioBuffer.length,
        audioBuffer.sampleRate
      );

      // Create source
      const source = offlineContext.createBufferSource();
      source.buffer = audioBuffer;

      // Create noise reduction filter (simple high-pass filter)
      const highPassFilter = offlineContext.createBiquadFilter();
      highPassFilter.type = 'highpass';
      highPassFilter.frequency.value = 80; // Remove low-frequency noise
      highPassFilter.Q.value = 1;

      // Create noise gate (simple implementation)
      const compressor = offlineContext.createDynamicsCompressor();
      compressor.threshold.value = -50;
      compressor.knee.value = 40;
      compressor.ratio.value = 12;
      compressor.attack.value = 0;
      compressor.release.value = 0.25;

      // Connect nodes
      source.connect(highPassFilter);
      highPassFilter.connect(compressor);
      compressor.connect(offlineContext.destination);

      // Process audio
      source.start();
      const processedBuffer = await offlineContext.startRendering();

      // Convert back to blob
      const processedBlob = await this.audioBufferToBlob(processedBuffer);
      
      logger.debug('Noise reduction applied');
      return processedBlob;
    } catch (error) {
      logger.error('Noise reduction failed', { error });
      throw new VoiceModuleError('Noise reduction failed', error);
    }
  }

  /**
   * Normalize audio volume
   */
  async normalizeVolume(audioBlob: Blob): Promise<Blob> {
    try {
      if (!this.audioContext) {
        throw new VoiceModuleError('Audio context not available');
      }

      const arrayBuffer = await audioBlob.arrayBuffer();
      const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);

      // Create offline context for processing
      const offlineContext = new OfflineAudioContext(
        audioBuffer.numberOfChannels,
        audioBuffer.length,
        audioBuffer.sampleRate
      );

      // Create source
      const source = offlineContext.createBufferSource();
      source.buffer = audioBuffer;

      // Create gain node for normalization
      const gainNode = offlineContext.createGain();
      
      // Calculate peak amplitude
      let peak = 0;
      for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {
        const channelData = audioBuffer.getChannelData(channel);
        for (let i = 0; i < channelData.length; i++) {
          peak = Math.max(peak, Math.abs(channelData[i]));
        }
      }

      // Set gain to normalize to 70% of maximum to avoid clipping
      const targetLevel = 0.7;
      const gainValue = peak > 0 ? targetLevel / peak : 1;
      gainNode.gain.value = Math.min(gainValue, 3); // Limit maximum gain

      // Connect nodes
      source.connect(gainNode);
      gainNode.connect(offlineContext.destination);

      // Process audio
      source.start();
      const processedBuffer = await offlineContext.startRendering();

      // Convert back to blob
      const processedBlob = await this.audioBufferToBlob(processedBuffer);
      
      logger.debug('Volume normalization applied', { gainValue });
      return processedBlob;
    } catch (error) {
      logger.error('Volume normalization failed', { error });
      throw new VoiceModuleError('Volume normalization failed', error);
    }
  }

  /**
   * Process audio with multiple options
   */
  async processAudio(audioBlob: Blob, options: AudioProcessingOptions): Promise<Blob> {
    try {
      let processedBlob = audioBlob;

      // Apply noise reduction if requested
      if (options.noiseReduction) {
        processedBlob = await this.reduceNoise(processedBlob);
      }

      // Apply volume normalization if requested
      if (options.volumeNormalization) {
        processedBlob = await this.normalizeVolume(processedBlob);
      }

      // Apply format conversion if requested
      if (options.formatConversion) {
        processedBlob = await this.convertFormat(processedBlob, options.formatConversion);
      }

      logger.debug('Audio processing completed', { 
        originalSize: audioBlob.size,
        processedSize: processedBlob.size,
        options 
      });

      return processedBlob;
    } catch (error) {
      logger.error('Audio processing failed', { error, options });
      throw new VoiceModuleError('Audio processing failed', error);
    }
  }

  /**
   * Convert AudioBuffer to Blob
   */
  private async audioBufferToBlob(audioBuffer: AudioBuffer): Promise<Blob> {
    try {
      // Convert to WAV format
      const length = audioBuffer.length;
      const numberOfChannels = audioBuffer.numberOfChannels;
      const sampleRate = audioBuffer.sampleRate;
      const bytesPerSample = 2; // 16-bit
      const blockAlign = numberOfChannels * bytesPerSample;
      const byteRate = sampleRate * blockAlign;
      const dataSize = length * blockAlign;
      const bufferSize = 44 + dataSize;

      const arrayBuffer = new ArrayBuffer(bufferSize);
      const view = new DataView(arrayBuffer);

      // WAV header
      const writeString = (offset: number, string: string) => {
        for (let i = 0; i < string.length; i++) {
          view.setUint8(offset + i, string.charCodeAt(i));
        }
      };

      writeString(0, 'RIFF');
      view.setUint32(4, bufferSize - 8, true);
      writeString(8, 'WAVE');
      writeString(12, 'fmt ');
      view.setUint32(16, 16, true);
      view.setUint16(20, 1, true);
      view.setUint16(22, numberOfChannels, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, byteRate, true);
      view.setUint16(32, blockAlign, true);
      view.setUint16(34, 16, true);
      writeString(36, 'data');
      view.setUint32(40, dataSize, true);

      // Convert audio data
      let offset = 44;
      for (let i = 0; i < length; i++) {
        for (let channel = 0; channel < numberOfChannels; channel++) {
          const sample = audioBuffer.getChannelData(channel)[i];
          const intSample = Math.max(-1, Math.min(1, sample)) * 0x7FFF;
          view.setInt16(offset, intSample, true);
          offset += 2;
        }
      }

      return new Blob([arrayBuffer], { type: 'audio/wav' });
    } catch (error) {
      logger.error('Failed to convert AudioBuffer to Blob', { error });
      throw new VoiceModuleError('Failed to convert AudioBuffer to Blob', error);
    }
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    try {
      if (this.audioContext && this.audioContext.state !== 'closed') {
        this.audioContext.close().catch(() => {});
        this.audioContext = null;
      }
    } catch (error) {
      logger.error('Error during audio processor cleanup', { error });
    }
  }
}
