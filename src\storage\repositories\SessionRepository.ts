/**
 * Session repository implementation
 * Handles user session tracking and analytics
 */

import { BaseRepository } from '../BaseRepository';
import { DatabaseConnection, SessionEntity } from '../types';
import { Session, SessionActivity, CreateSessionRequest } from '../../types/models/User';

/**
 * Repository for session entities
 */
export class SessionRepository extends BaseRepository<Session> {
  constructor(db: DatabaseConnection) {
    super(db, 'sessions');
  }

  /**
   * Find sessions by user ID
   */
  async findByUserId(userId: string): Promise<Session[]> {
    return this.findAll({
      userId,
      orderBy: 'started_at',
      orderDirection: 'DESC'
    });
  }

  /**
   * Find active sessions
   */
  async findActiveSessions(): Promise<Session[]> {
    return this.findAll({
      isActive: true,
      orderBy: 'started_at',
      orderDirection: 'DESC'
    });
  }

  /**
   * Find current session for user
   */
  async findCurrentSession(userId?: string): Promise<Session | null> {
    const filter: any = { isActive: true };
    if (userId) {
      filter.userId = userId;
    }

    const sessions = await this.findAll({
      ...filter,
      orderBy: 'started_at',
      orderDirection: 'DESC',
      limit: 1
    });

    return sessions[0] || null;
  }

  /**
   * Create new session
   */
  async createSession(request: CreateSessionRequest): Promise<string> {
    const session: Session = {
      id: this.generateId(),
      userId: request.userId || 'anonymous',
      startedAt: new Date(),
      activities: [],
      metadata: { ...this.getDefaultMetadata(), ...request.metadata },
      duration: 0,
      isActive: true,
      promptsUsed: 0,
      errorsEncountered: 0
    };

    return this.save(session);
  }

  /**
   * End session
   */
  async endSession(sessionId: string): Promise<void> {
    const session = await this.findById(sessionId);
    if (!session) {
      throw new Error(`Session with ID ${sessionId} not found`);
    }

    const endedAt = new Date();
    const duration = endedAt.getTime() - session.startedAt.getTime();

    await this.update(sessionId, {
      endedAt,
      duration,
      isActive: false
    });
  }

  /**
   * Add activity to session
   */
  async addActivity(sessionId: string, activity: SessionActivity): Promise<void> {
    const session = await this.findById(sessionId);
    if (!session) {
      throw new Error(`Session with ID ${sessionId} not found`);
    }

    const updatedActivities = [...session.activities, activity];
    
    // Update counters based on activity type
    let promptsUsed = session.promptsUsed;
    let errorsEncountered = session.errorsEncountered;

    if (activity.type === 'prompt_used' || activity.type === 'prompt_created') {
      promptsUsed++;
    }

    if (activity.type === 'error_occurred') {
      errorsEncountered++;
    }

    await this.update(sessionId, {
      activities: updatedActivities,
      promptsUsed,
      errorsEncountered
    });
  }

  /**
   * Get session statistics
   */
  async getStatistics(): Promise<{
    total: number;
    active: number;
    averageDuration: number;
    totalPrompts: number;
    totalErrors: number;
    byUser: { userId: string; sessions: number }[];
  }> {
    const [total, active] = await Promise.all([
      this.count(),
      this.count({ isActive: true })
    ]);

    const avgDurationResult = await this.queryOne<{ avg_duration: number }>(
      'SELECT AVG(duration) as avg_duration FROM sessions WHERE duration > 0'
    );

    const totalPromptsResult = await this.queryOne<{ total_prompts: number }>(
      'SELECT SUM(prompts_used) as total_prompts FROM sessions'
    );

    const totalErrorsResult = await this.queryOne<{ total_errors: number }>(
      'SELECT SUM(errors_encountered) as total_errors FROM sessions'
    );

    const byUserRows = await this.query<{ user_id: string; sessions: number }>(
      'SELECT user_id, COUNT(*) as sessions FROM sessions WHERE user_id IS NOT NULL GROUP BY user_id'
    );

    const byUser = byUserRows.map(row => ({
      userId: row.user_id,
      sessions: row.sessions
    }));

    return {
      total,
      active,
      averageDuration: avgDurationResult?.avg_duration || 0,
      totalPrompts: totalPromptsResult?.total_prompts || 0,
      totalErrors: totalErrorsResult?.total_errors || 0,
      byUser
    };
  }

  /**
   * Cleanup old sessions
   */
  async cleanupOldSessions(olderThanDays: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const sql = `
      DELETE FROM ${this.tableName} 
      WHERE is_active = FALSE 
      AND (ended_at IS NOT NULL AND ended_at < ?)
      OR (ended_at IS NULL AND started_at < ?)
    `;

    const result = await this.db.run(sql, [cutoffDate, cutoffDate]);
    return result.changes;
  }

  /**
   * Get recent sessions
   */
  async getRecentSessions(limit: number = 20): Promise<Session[]> {
    return this.findAll({
      orderBy: 'started_at',
      orderDirection: 'DESC',
      limit
    });
  }

  /**
   * Map database row to Session entity
   */
  protected mapRowToEntity(row: any): Session {
    const activities = this.deserializeValue(row.activities, 'json') || [];
    const metadata = this.deserializeValue(row.metadata, 'json') || this.getDefaultMetadata();

    return {
      id: row.id,
      userId: row.user_id,
      startedAt: new Date(row.started_at),
      activities,
      metadata,
      duration: row.duration || 0,
      isActive: this.deserializeValue(row.is_active, 'boolean'),
      promptsUsed: row.prompts_used || 0,
      errorsEncountered: row.errors_encountered || 0,
      ...(row.ended_at && { endedAt: new Date(row.ended_at) })
    };
  }

  /**
   * Override save to handle session-specific fields
   */
  async save(entity: Session): Promise<string> {
    const sessionEntity: SessionEntity = {
      id: (entity as any).id || this.generateId(),
      startedAt: entity.startedAt,
      activities: JSON.stringify(entity.activities || []),
      metadata: JSON.stringify(entity.metadata),
      duration: entity.duration,
      isActive: entity.isActive,
      promptsUsed: entity.promptsUsed,
      errorsEncountered: entity.errorsEncountered,
      createdAt: entity.startedAt,
      updatedAt: new Date(),
      ...(entity.userId && { userId: entity.userId }),
      ...(entity.endedAt && { endedAt: entity.endedAt })
    };

    const columns = Object.keys(sessionEntity).filter(key => sessionEntity[key as keyof SessionEntity] !== undefined);
    const placeholders = columns.map(() => '?').join(', ');
    const values = columns.map(col => this.serializeValue(sessionEntity[col as keyof SessionEntity]));

    const sql = `INSERT OR REPLACE INTO ${this.tableName} (${columns.map(col => this.camelToSnake(col)).join(', ')}) VALUES (${placeholders})`;

    await this.db.run(sql, values);
    return sessionEntity.id;
  }

  /**
   * Get default session metadata
   */
  private getDefaultMetadata() {
    return {
      appVersion: '1.0.0',
      platform: process.platform,
      osVersion: process.version,
      screenResolution: '1920x1080',
      memoryUsage: {
        rss: 0,
        heapTotal: 0,
        heapUsed: 0,
        external: 0
      },
      performanceMetrics: {
        startupTime: 0,
        averageResponseTime: 0,
        peakMemoryUsage: 0,
        cpuUsage: 0
      }
    };
  }
}
