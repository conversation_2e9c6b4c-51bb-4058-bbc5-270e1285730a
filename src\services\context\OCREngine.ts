/**
 * OCR Engine Implementation
 * Provides text extraction from images using Tesseract.js
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import * as os from 'os';
import { OCREngine, OCROptions, ContextExtractionError, ContextErrorCode } from './types';
import { logger } from '../../core/logger';

export class TesseractOCREngine implements OCREngine {
  private tesseract: any = null;
  private initialized = false;
  private supportedLanguages: string[] = [
    'eng', 'spa', 'fra', 'deu', 'ita', 'por', 'rus', 'jpn', 'kor',
    'chi_sim', 'chi_tra', 'ara', 'hin', 'tha', 'vie'
  ];

  constructor() {
    this.initializeTesseract().catch(error => {
      logger.error('Failed to initialize Tesseract OCR', { error: error.message });
    });
  }

  /**
   * Extract text from image file
   */
  async extractText(imagePath: string, options: OCROptions = {}): Promise<string> {
    await this.ensureInitialized();

    try {
      // Verify file exists
      await fs.access(imagePath);

      const startTime = Date.now();
      logger.debug('Starting OCR text extraction', { imagePath, options });

      const { data: { text, confidence } } = await this.tesseract.recognize(imagePath, {
        lang: options.language || 'eng',
        options: this.buildTesseractOptions(options)
      });

      const duration = Date.now() - startTime;
      logger.debug('OCR extraction completed', { 
        imagePath, 
        duration, 
        confidence,
        textLength: text.length 
      });

      if (confidence < 30) {
        logger.warn('Low OCR confidence detected', { confidence, imagePath });
      }

      return this.cleanExtractedText(text);
    } catch (error) {
      logger.error('OCR text extraction failed', { 
        imagePath, 
        error: error instanceof Error ? error.message : String(error) 
      });
      
      throw new ContextExtractionError(
        `OCR extraction failed: ${error instanceof Error ? error.message : String(error)}`,
        ContextErrorCode.OCR_FAILED,
        'OCREngine',
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Extract text from image buffer
   */
  async extractTextFromBuffer(imageBuffer: Buffer, options: OCROptions = {}): Promise<string> {
    const tempPath = path.join(os.tmpdir(), `ocr_temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.png`);
    
    try {
      await fs.writeFile(tempPath, imageBuffer);
      const result = await this.extractText(tempPath, options);
      return result;
    } finally {
      // Clean up temp file
      try {
        await fs.unlink(tempPath);
      } catch (error) {
        logger.warn('Failed to clean up temporary OCR file', { tempPath, error });
      }
    }
  }

  /**
   * Get list of supported languages
   */
  getSupportedLanguages(): string[] {
    return [...this.supportedLanguages];
  }

  /**
   * Detect language of text in image
   */
  async detectLanguage(imagePath: string): Promise<string> {
    try {
      // Extract text with multiple languages for detection
      const text = await this.extractText(imagePath, { 
        language: 'eng+spa+fra+deu+chi_sim' 
      });

      return this.detectLanguageFromText(text);
    } catch (error) {
      logger.warn('Language detection failed, defaulting to English', { 
        imagePath, 
        error: error instanceof Error ? error.message : String(error) 
      });
      return 'eng';
    }
  }

  /**
   * Initialize Tesseract worker
   */
  private async initializeTesseract(): Promise<void> {
    try {
      logger.debug('Initializing Tesseract OCR engine');

      // Dynamic import to handle potential module loading issues
      try {
        const tesseractModule = await import('tesseract.js');
        const { createWorker } = tesseractModule;

        this.tesseract = await createWorker();
        this.initialized = true;

        logger.info('Tesseract OCR engine initialized successfully');
      } catch (importError) {
        logger.warn('Tesseract.js not available, OCR functionality will be disabled', {
          error: importError instanceof Error ? importError.message : String(importError)
        });

        // Create a mock tesseract object for graceful degradation
        this.tesseract = {
          recognize: async () => ({ data: { text: '[OCR not available - tesseract.js not installed]', confidence: 0 } }),
          terminate: async () => {}
        };
        this.initialized = true;
      }
    } catch (error) {
      logger.error('Failed to initialize Tesseract OCR engine', {
        error: error instanceof Error ? error.message : String(error)
      });

      throw new ContextExtractionError(
        'Failed to initialize OCR engine',
        ContextErrorCode.INITIALIZATION_FAILED,
        'OCREngine',
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Ensure OCR engine is initialized
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.initializeTesseract();
    }
  }

  /**
   * Build Tesseract options from OCR options
   */
  private buildTesseractOptions(options: OCROptions): Record<string, string> {
    const tesseractOptions: Record<string, string> = {};

    if (options.pageSegMode !== undefined) {
      tesseractOptions.tessedit_pageseg_mode = options.pageSegMode.toString();
    }

    if (options.ocrEngineMode !== undefined) {
      tesseractOptions.tessedit_ocr_engine_mode = options.ocrEngineMode.toString();
    }

    if (options.whitelist) {
      tesseractOptions.tessedit_char_whitelist = options.whitelist;
    }

    if (options.blacklist) {
      tesseractOptions.tessedit_char_blacklist = options.blacklist;
    }

    if (options.preserveInterwordSpaces !== undefined) {
      tesseractOptions.preserve_interword_spaces = options.preserveInterwordSpaces ? '1' : '0';
    }

    return tesseractOptions;
  }

  /**
   * Clean and normalize extracted text
   */
  private cleanExtractedText(text: string): string {
    return text
      .replace(/\n\s*\n/g, '\n') // Remove multiple empty lines
      .replace(/^\s+|\s+$/g, '') // Trim whitespace
      .replace(/\s+/g, ' ') // Normalize whitespace
      .replace(/[^\x20-\x7E\u00A0-\uFFFF]/g, '') // Remove non-printable characters
      .trim();
  }

  /**
   * Detect language from extracted text
   */
  private detectLanguageFromText(text: string): string {
    // Simple language detection based on character patterns
    const cleanText = text.toLowerCase();

    // Chinese characters
    if (/[\u4e00-\u9fff]/.test(text)) {
      return 'chi_sim';
    }

    // Japanese characters (Hiragana, Katakana)
    if (/[\u3040-\u309f\u30a0-\u30ff]/.test(text)) {
      return 'jpn';
    }

    // Korean characters
    if (/[\uac00-\ud7af]/.test(text)) {
      return 'kor';
    }

    // Arabic characters
    if (/[\u0600-\u06ff]/.test(text)) {
      return 'ara';
    }

    // Russian/Cyrillic characters
    if (/[\u0400-\u04ff]/.test(text)) {
      return 'rus';
    }

    // European language detection
    if (/[àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]/.test(cleanText)) {
      return 'fra'; // French
    }

    if (/[äöüß]/.test(cleanText)) {
      return 'deu'; // German
    }

    if (/[ñáéíóúü¿¡]/.test(cleanText)) {
      return 'spa'; // Spanish
    }

    if (/[àáâãçéêíóôõú]/.test(cleanText)) {
      return 'por'; // Portuguese
    }

    if (/[àèéìíîòóù]/.test(cleanText)) {
      return 'ita'; // Italian
    }

    // Default to English
    return 'eng';
  }

  /**
   * Cleanup resources
   */
  async destroy(): Promise<void> {
    if (this.tesseract) {
      try {
        await this.tesseract.terminate();
        this.tesseract = null;
        this.initialized = false;
        logger.debug('Tesseract OCR engine terminated');
      } catch (error) {
        logger.warn('Error terminating Tesseract OCR engine', { error });
      }
    }
  }
}
