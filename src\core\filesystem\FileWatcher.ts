/**
 * File change monitoring utilities
 * Cross-platform file watching with event handling and filtering
 */

import * as fs from 'fs';
import * as path from 'path';
import { EventEmitter } from 'events';
import { FileWatcher as IFileWatcher, WatchOptions, WatchEvent, WatchEventType, FileMetadata } from './types';
import { PathUtils } from './PathUtils';
import { LoggerFactory } from '../logger';

const logger = LoggerFactory.getInstance().getLogger('FileWatcher');

export class FileWatcher extends EventEmitter implements IFileWatcher {
  public readonly path: string;
  public readonly options: WatchOptions;
  private watcher: fs.FSWatcher | null = null;
  private _isActive: boolean = false;
  private debounceTimers: Map<string, NodeJS.Timeout> = new Map();
  private writeFinishTimers: Map<string, NodeJS.Timeout> = new Map();

  constructor(watchPath: string, options: WatchOptions = {}) {
    super();
    this.path = PathUtils.normalize(watchPath);
    this.options = {
      recursive: false,
      persistent: true,
      ignoreInitial: true,
      followSymlinks: false,
      depth: undefined,
      awaitWriteFinish: false,
      ...options
    };
  }

  get isActive(): boolean {
    return this._isActive;
  }

  /**
   * Start watching the file or directory
   */
  async start(): Promise<void> {
    if (this._isActive) {
      return;
    }

    try {
      // Validate path exists
      if (!fs.existsSync(this.path)) {
        throw new Error(`Watch path does not exist: ${this.path}`);
      }

      // Create watcher
      this.watcher = fs.watch(this.path, {
        recursive: this.options.recursive,
        persistent: this.options.persistent
      });

      this.watcher.on('change', (eventType, filename) => {
        this.handleWatchEvent(eventType as WatchEventType, filename ? filename.toString() : '');
      });

      this.watcher.on('error', (error) => {
        this.handleError(error);
      });

      this._isActive = true;
      logger.info('File watcher started', { path: this.path, options: this.options });

      // Emit initial events if not ignoring
      if (!this.options.ignoreInitial) {
        await this.emitInitialEvents();
      }
    } catch (error) {
      logger.error('Failed to start file watcher', error);
      throw error;
    }
  }

  /**
   * Stop watching
   */
  async close(): Promise<void> {
    if (!this._isActive) {
      return;
    }

    try {
      // Clear all timers
      this.debounceTimers.forEach(timer => clearTimeout(timer));
      this.writeFinishTimers.forEach(timer => clearTimeout(timer));
      this.debounceTimers.clear();
      this.writeFinishTimers.clear();

      // Close watcher
      if (this.watcher) {
        this.watcher.close();
        this.watcher = null;
      }

      this._isActive = false;
      this.removeAllListeners();
      
      logger.info('File watcher stopped', { path: this.path });
    } catch (error) {
      logger.error('Error stopping file watcher', error);
      throw error;
    }
  }

  private handleWatchEvent(eventType: WatchEventType, filename: string | null): void {
    if (!filename) {
      return;
    }

    const fullPath = path.join(this.path, filename);
    
    // Apply filters
    if (this.shouldIgnorePath(fullPath)) {
      return;
    }

    // Handle write finish detection
    if (this.options.awaitWriteFinish && (eventType === 'change' || eventType === 'add')) {
      this.handleWriteFinish(fullPath, eventType);
      return;
    }

    // Debounce events
    this.debounceEvent(fullPath, eventType);
  }

  private debounceEvent(filePath: string, eventType: WatchEventType): void {
    const debounceKey = `${filePath}:${eventType}`;
    
    // Clear existing timer
    const existingTimer = this.debounceTimers.get(debounceKey);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Set new timer
    const timer = setTimeout(() => {
      this.emitWatchEvent(eventType, filePath);
      this.debounceTimers.delete(debounceKey);
    }, 100); // 100ms debounce

    this.debounceTimers.set(debounceKey, timer);
  }

  private handleWriteFinish(filePath: string, eventType: WatchEventType): void {
    const writeFinishOptions = typeof this.options.awaitWriteFinish === 'object' 
      ? this.options.awaitWriteFinish 
      : { stabilityThreshold: 2000, pollInterval: 100 };

    const stabilityThreshold = writeFinishOptions.stabilityThreshold || 2000;
    const pollInterval = writeFinishOptions.pollInterval || 100;

    // Clear existing timer
    const existingTimer = this.writeFinishTimers.get(filePath);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    let lastSize = -1;
    let lastMtime = -1;
    let stableCount = 0;

    const checkStability = () => {
      try {
        const stats = fs.statSync(filePath);
        const currentSize = stats.size;
        const currentMtime = stats.mtime.getTime();

        if (currentSize === lastSize && currentMtime === lastMtime) {
          stableCount++;
          if (stableCount * pollInterval >= stabilityThreshold) {
            // File is stable, emit event
            this.emitWatchEvent(eventType, filePath);
            this.writeFinishTimers.delete(filePath);
            return;
          }
        } else {
          stableCount = 0;
          lastSize = currentSize;
          lastMtime = currentMtime;
        }

        // Continue checking
        const timer = setTimeout(checkStability, pollInterval);
        this.writeFinishTimers.set(filePath, timer);
      } catch (error) {
        // File might have been deleted or is inaccessible
        this.writeFinishTimers.delete(filePath);
        if ((error as any).code !== 'ENOENT') {
          logger.warn('Error checking file stability', { path: filePath, error });
        }
      }
    };

    // Start stability check
    const timer = setTimeout(checkStability, pollInterval);
    this.writeFinishTimers.set(filePath, timer);
  }

  private async emitWatchEvent(eventType: WatchEventType, filePath: string): Promise<void> {
    try {
      let stats: FileMetadata | undefined;

      // Get file stats if file exists
      if (fs.existsSync(filePath)) {
        stats = await this.getFileMetadata(filePath);
      }

      const event: WatchEvent = {
        type: eventType,
        path: filePath,
        stats,
        timestamp: new Date()
      };

      this.emit('change', event);
      logger.debug('File watch event emitted', { event });
    } catch (error) {
      logger.error('Error emitting watch event', { path: filePath, error });
    }
  }

  private async emitInitialEvents(): Promise<void> {
    try {
      const stats = fs.statSync(this.path);
      
      if (stats.isDirectory()) {
        await this.emitDirectoryEvents(this.path, 0);
      } else {
        this.emitWatchEvent('add', this.path);
      }
    } catch (error) {
      logger.error('Error emitting initial events', error);
    }
  }

  private async emitDirectoryEvents(dirPath: string, currentDepth: number): Promise<void> {
    if (this.options.depth !== undefined && currentDepth >= this.options.depth) {
      return;
    }

    try {
      const entries = fs.readdirSync(dirPath, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        if (this.shouldIgnorePath(fullPath)) {
          continue;
        }

        if (entry.isDirectory()) {
          this.emitWatchEvent('addDir', fullPath);
          
          if (this.options.recursive) {
            await this.emitDirectoryEvents(fullPath, currentDepth + 1);
          }
        } else {
          this.emitWatchEvent('add', fullPath);
        }
      }
    } catch (error) {
      logger.error('Error reading directory for initial events', { path: dirPath, error });
    }
  }

  private shouldIgnorePath(filePath: string): boolean {
    if (!this.options.ignored) {
      return false;
    }

    const ignored = this.options.ignored;

    if (typeof ignored === 'string') {
      return filePath.includes(ignored);
    }

    if (ignored instanceof RegExp) {
      return ignored.test(filePath);
    }

    if (typeof ignored === 'function') {
      return ignored(filePath);
    }

    return false;
  }

  private async getFileMetadata(filePath: string): Promise<FileMetadata> {
    const stats = fs.statSync(filePath);
    const parsedPath = path.parse(filePath);

    return {
      path: filePath,
      name: parsedPath.name,
      extension: parsedPath.ext,
      size: stats.size,
      type: stats.isDirectory() ? 'directory' : stats.isSymbolicLink() ? 'symlink' : 'file',
      permissions: {
        readable: true, // Simplified for now
        writable: true,
        executable: true,
        mode: stats.mode
      },
      createdAt: stats.birthtime,
      modifiedAt: stats.mtime,
      accessedAt: stats.atime,
      isHidden: parsedPath.name.startsWith('.')
    };
  }

  private handleError(error: Error): void {
    logger.error('File watcher error', error);
    this.emit('error', error);
  }
}

/**
 * Factory function to create file watchers
 */
export function createFileWatcher(path: string, options?: WatchOptions): FileWatcher {
  return new FileWatcher(path, options);
}

/**
 * Watch multiple paths with a single event handler
 */
export class MultiFileWatcher extends EventEmitter {
  private watchers: Map<string, FileWatcher> = new Map();

  async addPath(path: string, options?: WatchOptions): Promise<void> {
    if (this.watchers.has(path)) {
      return;
    }

    const watcher = new FileWatcher(path, options);
    
    watcher.on('change', (event) => {
      this.emit('change', event);
    });

    watcher.on('error', (error) => {
      this.emit('error', error);
    });

    await watcher.start();
    this.watchers.set(path, watcher);
  }

  async removePath(path: string): Promise<void> {
    const watcher = this.watchers.get(path);
    if (watcher) {
      await watcher.close();
      this.watchers.delete(path);
    }
  }

  async close(): Promise<void> {
    const closePromises = Array.from(this.watchers.values()).map(watcher => watcher.close());
    await Promise.all(closePromises);
    this.watchers.clear();
    this.removeAllListeners();
  }

  get watchedPaths(): string[] {
    return Array.from(this.watchers.keys());
  }
}
