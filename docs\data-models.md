# Data Models Specification

## 🎯 Purpose

This document defines all data models, TypeScript interfaces, validation schemas, and serialization formats used throughout the PromptPilot Desktop application, ensuring consistent data structures and type safety.

## 📋 Model Categories

### Core Business Models
- Prompt and template models
- Category and tag models
- User and session models
- Configuration models

### System Models
- Error and logging models
- Analytics and metrics models
- File system models
- Security models

### API Models
- Request and response models
- Event and message models
- Validation and schema models

## 🏗️ Core Business Models

### Prompt Models
```typescript
// Base prompt interface
interface BasePrompt {
  readonly id: string;
  title: string;
  content: string;
  description?: string;
  readonly createdAt: Date;
  updatedAt: Date;
}

// Full prompt model
interface Prompt extends BasePrompt {
  categoryId: string;
  tags: readonly string[];
  variables: readonly PromptVariable[];
  metadata: PromptMetadata;
  version: number;
  parentId?: string;
  isTemplate: boolean;
  isFavorite: boolean;
  isArchived: boolean;
  usage: PromptUsage;
  
  // Computed properties
  readonly estimatedTokens: number;
  readonly complexity: PromptComplexity;
  readonly lastUsed?: Date;
}

// Prompt variable definition
interface PromptVariable {
  readonly name: string;
  readonly type: VariableType;
  readonly required: boolean;
  readonly defaultValue?: unknown;
  readonly description: string;
  readonly validation?: readonly ValidationRule[];
  readonly options?: readonly unknown[]; // For enum types
  readonly placeholder?: string;
  readonly helpText?: string;
}

// Variable types
type VariableType = 
  | 'string' 
  | 'number' 
  | 'boolean' 
  | 'array' 
  | 'object' 
  | 'enum' 
  | 'date' 
  | 'url' 
  | 'email' 
  | 'file';

// Prompt metadata
interface PromptMetadata {
  readonly author: string;
  readonly source: PromptSource;
  readonly language: string;
  readonly aiModel?: string;
  readonly estimatedTokens: number;
  readonly complexity: PromptComplexity;
  readonly keywords: readonly string[];
  readonly relatedPrompts: readonly string[];
  readonly customFields: Readonly<Record<string, unknown>>;
}

// Prompt source types
type PromptSource = 'user' | 'system' | 'imported' | 'generated' | 'shared';

// Prompt complexity levels
type PromptComplexity = 'simple' | 'medium' | 'complex' | 'expert';

// Prompt usage statistics
interface PromptUsage {
  count: number;
  readonly lastUsed: Date;
  readonly averageRating?: number;
  readonly successRate?: number;
  readonly totalTokens: number;
  readonly totalCost: number;
  readonly averageExecutionTime: number;
  readonly contexts: readonly UsageContext[];
}

// Usage context tracking
interface UsageContext {
  readonly type: UsageContextType;
  readonly timestamp: Date;
  readonly metadata?: Readonly<Record<string, unknown>>;
}

type UsageContextType = 'manual' | 'voice' | 'hotkey' | 'api' | 'scheduled';

// Template-specific model
interface PromptTemplate extends Prompt {
  readonly templateEngine: TemplateEngine;
  readonly requiredVariables: readonly string[];
  readonly optionalVariables: readonly string[];
  readonly examples: readonly TemplateExample[];
  readonly documentation?: string;
  readonly category: 'form' | 'workflow' | 'snippet' | 'macro';
}

type TemplateEngine = 'handlebars' | 'mustache' | 'simple' | 'liquid';

// Template example
interface TemplateExample {
  readonly name: string;
  readonly description: string;
  readonly variables: Readonly<Record<string, unknown>>;
  readonly expectedOutput: string;
  readonly tags: readonly string[];
}

// Prompt version for history tracking
interface PromptVersion {
  readonly id: string;
  readonly promptId: string;
  readonly version: number;
  readonly title: string;
  readonly content: string;
  readonly description?: string;
  readonly changesSummary: string;
  readonly createdAt: Date;
  readonly createdBy: string;
  readonly diff?: PromptDiff;
}

// Diff representation
interface PromptDiff {
  readonly additions: readonly DiffChunk[];
  readonly deletions: readonly DiffChunk[];
  readonly modifications: readonly DiffChunk[];
}

interface DiffChunk {
  readonly type: 'addition' | 'deletion' | 'modification';
  readonly content: string;
  readonly lineNumber: number;
  readonly context?: string;
}
```

### Category and Organization Models
```typescript
// Category model
interface Category {
  readonly id: string;
  name: string;
  description: string;
  readonly parentId?: string;
  color?: string;
  icon?: string;
  sortOrder: number;
  readonly promptCount: number;
  readonly isSystem: boolean;
  readonly metadata: CategoryMetadata;
  
  // Computed properties
  readonly path: readonly string[];
  readonly depth: number;
  readonly hasChildren: boolean;
}

// Category metadata
interface CategoryMetadata {
  readonly createdAt: Date;
  readonly updatedAt: Date;
  readonly lastUsed?: Date;
  readonly totalUsage: number;
  readonly averageRating?: number;
  readonly customFields: Readonly<Record<string, unknown>>;
}

// Category tree structure
interface CategoryTree {
  readonly category: Category;
  readonly children: readonly CategoryTree[];
  readonly promptCount: number;
  readonly totalPromptCount: number; // Including children
  readonly depth: number;
}

// Tag model
interface Tag {
  readonly name: string;
  color?: string;
  description?: string;
  readonly usageCount: number;
  readonly createdAt: Date;
  readonly lastUsedAt?: Date;
  readonly relatedTags: readonly string[];
  readonly category?: string;
}

// Tag statistics
interface TagStatistics {
  readonly tag: Tag;
  readonly promptCount: number;
  readonly recentUsage: number; // Last 30 days
  readonly trending: boolean;
  readonly coOccurringTags: readonly TagCoOccurrence[];
}

interface TagCoOccurrence {
  readonly tagName: string;
  readonly frequency: number;
  readonly strength: number; // 0-1
}
```

### User and Session Models
```typescript
// User model
interface User {
  readonly id: string;
  readonly username?: string;
  readonly email?: string;
  readonly displayName?: string;
  readonly avatar?: string;
  readonly preferences: UserPreferences;
  readonly subscription?: Subscription;
  readonly metadata: UserMetadata;
  
  // Computed properties
  readonly isActive: boolean;
  readonly membershipDuration: number; // days
  readonly totalPrompts: number;
}

// User preferences
interface UserPreferences {
  readonly appearance: AppearancePreferences;
  readonly behavior: BehaviorPreferences;
  readonly privacy: PrivacyPreferences;
  readonly ai: AIPreferences;
  readonly voice: VoicePreferences;
  readonly hotkeys: HotkeyPreferences;
  readonly notifications: NotificationPreferences;
}

interface AppearancePreferences {
  readonly theme: ThemeMode;
  readonly language: string;
  readonly fontSize: FontSize;
  readonly colorScheme?: string;
  readonly compactMode: boolean;
  readonly animations: boolean;
}

type ThemeMode = 'light' | 'dark' | 'system' | 'auto';
type FontSize = 'small' | 'medium' | 'large' | 'extra-large';

interface BehaviorPreferences {
  readonly autoSave: boolean;
  readonly autoBackup: boolean;
  readonly confirmDeletions: boolean;
  readonly rememberWindowState: boolean;
  readonly startMinimized: boolean;
  readonly checkUpdates: boolean;
}

interface PrivacyPreferences {
  readonly analytics: boolean;
  readonly crashReports: boolean;
  readonly usageStatistics: boolean;
  readonly dataSharing: boolean;
  readonly anonymizeData: boolean;
  readonly retentionPeriod: number; // days
}

interface AIPreferences {
  readonly defaultProvider: string;
  readonly defaultModel: string;
  readonly temperature: number;
  readonly maxTokens: number;
  readonly autoEnhance: boolean;
  readonly contextInjection: boolean;
  readonly costWarnings: boolean;
  readonly customInstructions?: string;
}

interface VoicePreferences {
  readonly defaultLanguage: string;
  readonly provider: string;
  readonly sensitivity: number;
  readonly noiseReduction: boolean;
  readonly autoStop: boolean;
  readonly maxDuration: number; // seconds
  readonly commands: boolean;
}

interface HotkeyPreferences {
  readonly showFloatingWindow: string;
  readonly captureScreen: string;
  readonly startVoiceInput: string;
  readonly quickSearch: string;
  readonly enhanceClipboard: string;
  readonly customHotkeys: Readonly<Record<string, string>>;
}

interface NotificationPreferences {
  readonly enabled: boolean;
  readonly sound: boolean;
  readonly desktop: boolean;
  readonly duration: number;
  readonly position: NotificationPosition;
  readonly types: readonly NotificationType[];
}

type NotificationPosition = 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
type NotificationType = 'success' | 'error' | 'warning' | 'info' | 'update';

// User metadata
interface UserMetadata {
  readonly createdAt: Date;
  readonly lastLoginAt?: Date;
  readonly lastActiveAt?: Date;
  readonly loginCount: number;
  readonly appVersion: string;
  readonly platform: string;
  readonly timezone: string;
  readonly locale: string;
}

// Subscription model
interface Subscription {
  readonly id: string;
  readonly plan: SubscriptionPlan;
  readonly status: SubscriptionStatus;
  readonly startDate: Date;
  readonly endDate?: Date;
  readonly features: readonly string[];
  readonly limits: SubscriptionLimits;
  readonly billing: BillingInfo;
}

type SubscriptionPlan = 'free' | 'basic' | 'pro' | 'enterprise';
type SubscriptionStatus = 'active' | 'inactive' | 'cancelled' | 'expired' | 'trial';

interface SubscriptionLimits {
  readonly maxPrompts: number;
  readonly maxTokensPerMonth: number;
  readonly maxVoiceMinutesPerMonth: number;
  readonly maxStorageGB: number;
  readonly aiProviders: readonly string[];
  readonly advancedFeatures: boolean;
}

interface BillingInfo {
  readonly customerId?: string;
  readonly subscriptionId?: string;
  readonly nextBillingDate?: Date;
  readonly amount: number;
  readonly currency: string;
  readonly interval: 'monthly' | 'yearly';
}

// Session model
interface Session {
  readonly id: string;
  readonly userId?: string;
  readonly startedAt: Date;
  endedAt?: Date;
  readonly activities: readonly SessionActivity[];
  readonly metadata: SessionMetadata;
  
  // Computed properties
  readonly duration: number; // milliseconds
  readonly isActive: boolean;
  readonly promptsUsed: number;
  readonly errorsEncountered: number;
}

interface SessionActivity {
  readonly id: string;
  readonly type: ActivityType;
  readonly timestamp: Date;
  readonly data: Readonly<Record<string, unknown>>;
  readonly duration?: number;
  readonly success?: boolean;
}

type ActivityType = 
  | 'prompt_created'
  | 'prompt_used'
  | 'prompt_enhanced'
  | 'voice_command'
  | 'screen_capture'
  | 'search_performed'
  | 'settings_changed'
  | 'error_occurred';

interface SessionMetadata {
  readonly appVersion: string;
  readonly platform: string;
  readonly osVersion: string;
  readonly screenResolution: string;
  readonly memoryUsage: MemoryUsage;
  readonly performanceMetrics: PerformanceMetrics;
}

interface MemoryUsage {
  readonly rss: number;
  readonly heapTotal: number;
  readonly heapUsed: number;
  readonly external: number;
}

interface PerformanceMetrics {
  readonly startupTime: number;
  readonly averageResponseTime: number;
  readonly peakMemoryUsage: number;
  readonly cpuUsage: number;
}
```

## 🔧 System Models

### Configuration Models
```typescript
// Configuration schema
interface ConfigurationSchema {
  readonly version: string;
  readonly sections: Readonly<Record<string, ConfigSection>>;
  readonly metadata: SchemaMetadata;
}

interface ConfigSection {
  readonly title: string;
  readonly description: string;
  readonly icon?: string;
  readonly order: number;
  readonly settings: Readonly<Record<string, ConfigSetting>>;
}

interface ConfigSetting {
  readonly type: ConfigSettingType;
  readonly title: string;
  readonly description: string;
  readonly defaultValue: unknown;
  readonly required: boolean;
  readonly validation?: readonly ValidationRule[];
  readonly options?: readonly ConfigOption[];
  readonly sensitive: boolean;
  readonly restartRequired: boolean;
  readonly category: SettingCategory;
  readonly dependencies?: readonly string[];
  readonly deprecated?: boolean;
  readonly since?: string;
}

type ConfigSettingType = 
  | 'string' 
  | 'number' 
  | 'boolean' 
  | 'array' 
  | 'object' 
  | 'enum' 
  | 'color' 
  | 'file' 
  | 'directory';

type SettingCategory = 'basic' | 'advanced' | 'expert' | 'debug';

interface ConfigOption {
  readonly value: unknown;
  readonly label: string;
  readonly description?: string;
  readonly icon?: string;
  readonly disabled?: boolean;
}

interface SchemaMetadata {
  readonly version: string;
  readonly createdAt: Date;
  readonly updatedAt: Date;
  readonly author: string;
  readonly description: string;
}

// Configuration value
interface ConfigValue {
  readonly key: string;
  readonly value: unknown;
  readonly type: ConfigSettingType;
  readonly source: ConfigSource;
  readonly timestamp: Date;
  readonly valid: boolean;
  readonly encrypted: boolean;
}

type ConfigSource = 'default' | 'user' | 'system' | 'environment' | 'import';

// Configuration change event
interface ConfigChangeEvent {
  readonly key: string;
  readonly oldValue: unknown;
  readonly newValue: unknown;
  readonly source: ConfigSource;
  readonly timestamp: Date;
  readonly userId?: string;
  readonly sessionId?: string;
}
```

### Error and Logging Models
```typescript
// Error model
interface AppError {
  readonly id: string;
  readonly type: ErrorType;
  readonly code: string;
  readonly message: string;
  readonly stack?: string;
  readonly cause?: AppError;
  readonly context: ErrorContext;
  readonly timestamp: Date;
  readonly severity: ErrorSeverity;
  readonly resolved: boolean;
  readonly metadata: ErrorMetadata;
}

type ErrorType = 
  | 'validation'
  | 'network'
  | 'filesystem'
  | 'database'
  | 'ai_provider'
  | 'stt_provider'
  | 'permission'
  | 'configuration'
  | 'unknown';

type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

interface ErrorContext {
  readonly component: string;
  readonly action: string;
  readonly userId?: string;
  readonly sessionId?: string;
  readonly requestId?: string;
  readonly userAgent?: string;
  readonly url?: string;
  readonly data?: Readonly<Record<string, unknown>>;
}

interface ErrorMetadata {
  readonly appVersion: string;
  readonly platform: string;
  readonly osVersion: string;
  readonly memoryUsage: number;
  readonly cpuUsage: number;
  readonly diskSpace: number;
  readonly networkStatus: string;
}

// Log entry model
interface LogEntry {
  readonly id: string;
  readonly timestamp: Date;
  readonly level: LogLevel;
  readonly message: string;
  readonly component: string;
  readonly context: LogContext;
  readonly metadata?: Readonly<Record<string, unknown>>;
  readonly error?: AppError;
  readonly performance?: PerformanceInfo;
}

type LogLevel = 'trace' | 'debug' | 'info' | 'warn' | 'error' | 'fatal';

interface LogContext {
  readonly module?: string;
  readonly function?: string;
  readonly userId?: string;
  readonly sessionId?: string;
  readonly requestId?: string;
  readonly correlationId?: string;
  readonly tags?: readonly string[];
}

interface PerformanceInfo {
  readonly startTime: number;
  readonly endTime: number;
  readonly duration: number;
  readonly memoryBefore?: MemoryUsage;
  readonly memoryAfter?: MemoryUsage;
  readonly cpuUsage?: number;
}
```

### Analytics Models
```typescript
// Analytics event
interface AnalyticsEvent {
  readonly id: string;
  readonly name: string;
  readonly category: EventCategory;
  readonly properties: Readonly<Record<string, unknown>>;
  readonly timestamp: Date;
  readonly userId?: string;
  readonly sessionId?: string;
  readonly anonymousId?: string;
  readonly context: EventContext;
}

type EventCategory = 
  | 'user_action'
  | 'system_event'
  | 'performance'
  | 'error'
  | 'feature_usage'
  | 'conversion';

interface EventContext {
  readonly app: AppContext;
  readonly device: DeviceContext;
  readonly os: OSContext;
  readonly screen?: ScreenContext;
  readonly network?: NetworkContext;
}

interface AppContext {
  readonly name: string;
  readonly version: string;
  readonly build?: string;
  readonly environment: string;
  readonly installId: string;
}

interface DeviceContext {
  readonly id?: string;
  readonly type: DeviceType;
  readonly manufacturer?: string;
  readonly model?: string;
  readonly memory: number;
  readonly storage: number;
}

type DeviceType = 'desktop' | 'laptop' | 'tablet' | 'mobile' | 'server';

interface OSContext {
  readonly name: string;
  readonly version: string;
  readonly architecture: string;
  readonly locale: string;
  readonly timezone: string;
}

interface ScreenContext {
  readonly width: number;
  readonly height: number;
  readonly density: number;
  readonly colorDepth: number;
}

interface NetworkContext {
  readonly type: NetworkType;
  readonly speed?: string;
  readonly online: boolean;
}

type NetworkType = 'wifi' | 'ethernet' | 'cellular' | 'bluetooth' | 'unknown';

// Metrics model
interface Metric {
  readonly name: string;
  readonly value: number;
  readonly unit: MetricUnit;
  readonly type: MetricType;
  readonly tags: Readonly<Record<string, string>>;
  readonly timestamp: Date;
  readonly metadata?: Readonly<Record<string, unknown>>;
}

type MetricUnit = 'count' | 'bytes' | 'milliseconds' | 'seconds' | 'percent' | 'rate';
type MetricType = 'counter' | 'gauge' | 'histogram' | 'timer';
```

## 🔒 Validation Models

### Validation Rules
```typescript
// Validation rule
interface ValidationRule {
  readonly type: ValidationType;
  readonly value?: unknown;
  readonly message: string;
  readonly severity: ValidationSeverity;
  readonly validator?: ValidationFunction;
  readonly async?: boolean;
}

type ValidationType = 
  | 'required'
  | 'min'
  | 'max'
  | 'minLength'
  | 'maxLength'
  | 'pattern'
  | 'email'
  | 'url'
  | 'enum'
  | 'custom';

type ValidationSeverity = 'error' | 'warning' | 'info';
type ValidationFunction = (value: unknown, context?: ValidationContext) => boolean | Promise<boolean>;

interface ValidationContext {
  readonly field: string;
  readonly object: Readonly<Record<string, unknown>>;
  readonly path: readonly string[];
  readonly root: Readonly<Record<string, unknown>>;
}

// Validation result
interface ValidationResult {
  readonly valid: boolean;
  readonly errors: readonly ValidationError[];
  readonly warnings: readonly ValidationWarning[];
  readonly metadata?: Readonly<Record<string, unknown>>;
}

interface ValidationError {
  readonly field: string;
  readonly message: string;
  readonly code: string;
  readonly value: unknown;
  readonly rule: ValidationRule;
}

interface ValidationWarning {
  readonly field: string;
  readonly message: string;
  readonly code: string;
  readonly value: unknown;
  readonly rule: ValidationRule;
}

// Schema validation
interface Schema {
  readonly $schema?: string;
  readonly type: SchemaType;
  readonly properties?: Readonly<Record<string, Schema>>;
  readonly items?: Schema;
  readonly required?: readonly string[];
  readonly additionalProperties?: boolean | Schema;
  readonly enum?: readonly unknown[];
  readonly const?: unknown;
  readonly validation?: readonly ValidationRule[];
}

type SchemaType = 'object' | 'array' | 'string' | 'number' | 'boolean' | 'null';
```

This comprehensive data models specification provides a solid foundation for type-safe development throughout the PromptPilot Desktop application, ensuring consistency and maintainability across all modules.
