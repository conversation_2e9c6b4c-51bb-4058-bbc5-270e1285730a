/**
 * IPC Message Validator
 * Provides security validation, sanitization, and permission checking for IPC messages
 */

import { 
  MessageValidator, 
  SecurityPolicy, 
  IPCMessageType, 
  IPCErrorCode,
  IPCError 
} from './types';
import { logger } from '../../core/logger';

export class IPCValidator implements MessageValidator {
  private securityPolicy: SecurityPolicy;
  private rateLimitTracker: Map<string, { count: number; resetTime: number }>;

  constructor() {
    this.securityPolicy = this.createDefaultSecurityPolicy();
    this.rateLimitTracker = new Map();
  }

  /**
   * Validate if channel is allowed
   */
  validateChannel(channel: string): boolean {
    const isValid = this.securityPolicy.allowedChannels.has(channel);
    
    if (!isValid) {
      logger.warn('Invalid IPC channel attempted', { channel });
    }
    
    return isValid;
  }

  /**
   * Validate message data against schema
   */
  validateData(data: any, schema?: any): boolean {
    try {
      // Basic validation - check for null/undefined
      if (data === null || data === undefined) {
        return false;
      }

      // Check for dangerous properties
      if (typeof data === 'object') {
        const dangerousProps = ['__proto__', 'constructor', 'prototype'];
        for (const prop of dangerousProps) {
          if (prop in data) {
            logger.warn('Dangerous property detected in IPC data', { property: prop });
            return false;
          }
        }
      }

      // If schema is provided, validate against it
      if (schema) {
        return this.validateAgainstSchema(data, schema);
      }

      return true;
    } catch (error) {
      logger.error('Data validation failed', { error, data });
      return false;
    }
  }

  /**
   * Sanitize message data
   */
  sanitizeData(data: any): any {
    if (data === null || data === undefined) {
      return data;
    }

    if (typeof data !== 'object') {
      return data;
    }

    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeData(item));
    }

    // Create sanitized copy
    const sanitized: any = {};
    
    for (const [key, value] of Object.entries(data)) {
      // Skip dangerous properties
      if (key === '__proto__' || key === 'constructor' || key === 'prototype') {
        continue;
      }

      // Recursively sanitize nested objects
      if (typeof value === 'object' && value !== null) {
        sanitized[key] = this.sanitizeData(value);
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  /**
   * Check permissions for channel and source
   */
  checkPermissions(channel: string, source: string): boolean {
    const allowedSources = this.securityPolicy.channelPermissions.get(channel);
    
    if (!allowedSources) {
      logger.warn('No permissions defined for channel', { channel });
      return false;
    }

    const hasPermission = allowedSources.includes(source) || allowedSources.includes('*');
    
    if (!hasPermission) {
      logger.warn('Permission denied for IPC channel', { channel, source });
    }

    return hasPermission;
  }

  /**
   * Check rate limits for channel
   */
  checkRateLimit(channel: string): boolean {
    const rateLimit = this.securityPolicy.rateLimits.get(channel);
    
    if (!rateLimit) {
      return true; // No rate limit defined
    }

    const now = Date.now();
    const tracker = this.rateLimitTracker.get(channel);

    if (!tracker) {
      // First request for this channel
      this.rateLimitTracker.set(channel, {
        count: 1,
        resetTime: now + rateLimit.windowMs
      });
      return true;
    }

    // Check if window has expired
    if (now > tracker.resetTime) {
      tracker.count = 1;
      tracker.resetTime = now + rateLimit.windowMs;
      return true;
    }

    // Check if limit exceeded
    if (tracker.count >= rateLimit.maxRequests) {
      logger.warn('Rate limit exceeded for IPC channel', { 
        channel, 
        count: tracker.count, 
        limit: rateLimit.maxRequests 
      });
      return false;
    }

    tracker.count++;
    return true;
  }

  /**
   * Validate complete IPC message
   */
  validateMessage(channel: string, data: any, source: string): IPCError | null {
    // Check channel validity
    if (!this.validateChannel(channel)) {
      return {
        code: IPCErrorCode.INVALID_CHANNEL,
        message: `Invalid channel: ${channel}`
      };
    }

    // Check permissions
    if (!this.checkPermissions(channel, source)) {
      return {
        code: IPCErrorCode.PERMISSION_DENIED,
        message: `Permission denied for channel: ${channel}`
      };
    }

    // Check rate limits
    if (!this.checkRateLimit(channel)) {
      return {
        code: IPCErrorCode.RATE_LIMIT_EXCEEDED,
        message: `Rate limit exceeded for channel: ${channel}`
      };
    }

    // Validate data
    if (!this.validateData(data)) {
      return {
        code: IPCErrorCode.VALIDATION_FAILED,
        message: 'Message data validation failed'
      };
    }

    return null; // No errors
  }

  /**
   * Create default security policy
   */
  private createDefaultSecurityPolicy(): SecurityPolicy {
    // Define all allowed channels
    const allowedChannels = new Set(Object.values(IPCMessageType));

    // Define channel permissions (source -> channels)
    const channelPermissions = new Map<string, string[]>();
    
    // All channels accessible from renderer
    Object.values(IPCMessageType).forEach(channel => {
      channelPermissions.set(channel, ['renderer', 'main']);
    });

    // Define rate limits for high-frequency operations
    const rateLimits = new Map<string, { maxRequests: number; windowMs: number }>();
    
    // Voice operations - limit to prevent spam
    rateLimits.set(IPCMessageType.VOICE_START_RECORDING, { maxRequests: 10, windowMs: 60000 });
    rateLimits.set(IPCMessageType.VOICE_STOP_RECORDING, { maxRequests: 10, windowMs: 60000 });
    
    // Context operations - limit screen capture
    rateLimits.set(IPCMessageType.CONTEXT_CAPTURE_SCREEN, { maxRequests: 30, windowMs: 60000 });
    
    // AI operations - limit to prevent abuse
    rateLimits.set(IPCMessageType.AI_GENERATE, { maxRequests: 100, windowMs: 60000 });
    rateLimits.set(IPCMessageType.AI_ENHANCE, { maxRequests: 50, windowMs: 60000 });

    // Define sanitization rules
    const sanitizationRules = new Map<string, (data: any) => any>();
    
    // Default sanitization for all channels
    Object.values(IPCMessageType).forEach(channel => {
      sanitizationRules.set(channel, (data: any) => this.sanitizeData(data));
    });

    return {
      allowedChannels,
      channelPermissions,
      rateLimits,
      sanitizationRules
    };
  }

  /**
   * Validate data against schema (basic implementation)
   */
  private validateAgainstSchema(data: any, schema: any): boolean {
    // Basic schema validation - can be extended with a proper JSON schema validator
    try {
      if (schema.type) {
        const dataType = Array.isArray(data) ? 'array' : typeof data;
        if (dataType !== schema.type) {
          return false;
        }
      }

      if (schema.required && Array.isArray(schema.required)) {
        for (const field of schema.required) {
          if (!(field in data)) {
            return false;
          }
        }
      }

      if (schema.properties && typeof data === 'object') {
        for (const [key, value] of Object.entries(data)) {
          const propSchema = schema.properties[key];
          if (propSchema && !this.validateAgainstSchema(value, propSchema)) {
            return false;
          }
        }
      }

      return true;
    } catch (error) {
      logger.error('Schema validation error', { error, schema });
      return false;
    }
  }

  /**
   * Update security policy
   */
  updateSecurityPolicy(updates: Partial<SecurityPolicy>): void {
    if (updates.allowedChannels) {
      this.securityPolicy.allowedChannels = updates.allowedChannels;
    }
    
    if (updates.channelPermissions) {
      this.securityPolicy.channelPermissions = updates.channelPermissions;
    }
    
    if (updates.rateLimits) {
      this.securityPolicy.rateLimits = updates.rateLimits;
    }
    
    if (updates.sanitizationRules) {
      this.securityPolicy.sanitizationRules = updates.sanitizationRules;
    }

    logger.info('Security policy updated');
  }

  /**
   * Get current security policy
   */
  getSecurityPolicy(): SecurityPolicy {
    return { ...this.securityPolicy };
  }

  /**
   * Reset rate limit tracker
   */
  resetRateLimits(): void {
    this.rateLimitTracker.clear();
    logger.info('Rate limit tracker reset');
  }
}
