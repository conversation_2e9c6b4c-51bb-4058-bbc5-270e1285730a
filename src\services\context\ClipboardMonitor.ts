/**
 * Clipboard Monitor Service
 * Monitors clipboard changes and maintains history
 */

import { clipboard } from 'electron';
import { 
  IClipboardMonitor, 
  ClipboardContent, 
  ClipboardChangeListener,
  ContextExtractionError,
  ContextErrorCode
} from './types';
import { logger } from '../../core/logger';

export class ClipboardMonitor implements IClipboardMonitor {
  private lastClipboardContent: string = '';
  private listeners: Set<ClipboardChangeListener> = new Set();
  private monitoring = false;
  private monitorInterval: NodeJS.Timeout | null = null;
  private history: ClipboardContent[] = [];
  private maxHistorySize: number;
  private intervalMs: number;

  constructor(maxHistorySize: number = 50, defaultInterval: number = 1000) {
    this.maxHistorySize = maxHistorySize;
    this.intervalMs = defaultInterval;
    
    // Initialize with current clipboard content
    try {
      this.lastClipboardContent = clipboard.readText() || '';
      if (this.lastClipboardContent) {
        this.addToHistory(this.lastClipboardContent);
      }
    } catch (error) {
      logger.warn('Failed to read initial clipboard content', { error });
    }
  }

  /**
   * Start monitoring clipboard changes
   */
  startMonitoring(intervalMs: number = this.intervalMs): void {
    if (this.monitoring) {
      logger.debug('Clipboard monitoring already active');
      return;
    }

    this.intervalMs = intervalMs;
    this.monitoring = true;

    this.monitorInterval = setInterval(() => {
      this.checkClipboard();
    }, this.intervalMs);

    logger.info('Clipboard monitoring started', { intervalMs });
  }

  /**
   * Stop monitoring clipboard changes
   */
  stopMonitoring(): void {
    if (!this.monitoring) {
      return;
    }

    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }

    this.monitoring = false;
    logger.info('Clipboard monitoring stopped');
  }

  /**
   * Get current clipboard content
   */
  getCurrentContent(): ClipboardContent {
    try {
      const text = clipboard.readText() || '';
      return {
        text,
        timestamp: new Date(),
        source: 'current'
      };
    } catch (error) {
      logger.error('Failed to read current clipboard content', { error });
      throw new ContextExtractionError(
        'Failed to access clipboard',
        ContextErrorCode.CLIPBOARD_ACCESS_DENIED,
        'ClipboardMonitor',
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Get clipboard history
   */
  getHistory(): ClipboardContent[] {
    return [...this.history];
  }

  /**
   * Add listener for clipboard changes
   */
  onContentChange(listener: ClipboardChangeListener): () => void {
    this.listeners.add(listener);
    
    logger.debug('Clipboard change listener added', { 
      totalListeners: this.listeners.size 
    });

    // Return unsubscribe function
    return () => {
      this.listeners.delete(listener);
      logger.debug('Clipboard change listener removed', { 
        totalListeners: this.listeners.size 
      });
    };
  }

  /**
   * Clear clipboard history
   */
  clearHistory(): void {
    this.history = [];
    logger.debug('Clipboard history cleared');
  }

  /**
   * Set maximum history size
   */
  setMaxHistorySize(size: number): void {
    this.maxHistorySize = Math.max(1, size);
    
    // Trim history if needed
    if (this.history.length > this.maxHistorySize) {
      this.history = this.history.slice(-this.maxHistorySize);
    }

    logger.debug('Clipboard history size updated', { maxHistorySize: this.maxHistorySize });
  }

  /**
   * Get monitoring status
   */
  isMonitoring(): boolean {
    return this.monitoring;
  }

  /**
   * Get clipboard statistics
   */
  getStatistics(): {
    historySize: number;
    maxHistorySize: number;
    isMonitoring: boolean;
    listenerCount: number;
    intervalMs: number;
  } {
    return {
      historySize: this.history.length,
      maxHistorySize: this.maxHistorySize,
      isMonitoring: this.monitoring,
      listenerCount: this.listeners.size,
      intervalMs: this.intervalMs
    };
  }

  /**
   * Check clipboard for changes
   */
  private checkClipboard(): void {
    try {
      const currentContent = clipboard.readText() || '';

      // Check if content has changed
      if (currentContent !== this.lastClipboardContent) {
        this.lastClipboardContent = currentContent;
        
        // Only process non-empty content
        if (currentContent.trim()) {
          const clipboardContent: ClipboardContent = {
            text: currentContent,
            timestamp: new Date(),
            source: 'monitor'
          };

          this.addToHistory(currentContent);
          this.notifyListeners(clipboardContent);

          logger.debug('Clipboard change detected', { 
            contentLength: currentContent.length,
            historySize: this.history.length 
          });
        }
      }
    } catch (error) {
      logger.warn('Failed to check clipboard content', { 
        error: error instanceof Error ? error.message : String(error) 
      });

      // If clipboard access fails repeatedly, we might want to stop monitoring
      // This could happen if permissions are revoked
      if (error instanceof Error && error.message.includes('permission')) {
        logger.error('Clipboard permission denied, stopping monitoring');
        this.stopMonitoring();
      }
    }
  }

  /**
   * Add content to history
   */
  private addToHistory(content: string): void {
    // Don't add duplicate consecutive entries
    if (this.history.length > 0 && this.history[this.history.length - 1].text === content) {
      return;
    }

    const clipboardContent: ClipboardContent = {
      text: content,
      timestamp: new Date(),
      source: 'history'
    };

    this.history.push(clipboardContent);

    // Maintain history size limit
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
    }
  }

  /**
   * Notify all listeners of clipboard change
   */
  private notifyListeners(content: ClipboardContent): void {
    this.listeners.forEach(listener => {
      try {
        listener(content);
      } catch (error) {
        logger.error('Clipboard listener error', { 
          error: error instanceof Error ? error.message : String(error) 
        });
      }
    });
  }

  /**
   * Search clipboard history
   */
  searchHistory(query: string, limit: number = 10): ClipboardContent[] {
    const lowerQuery = query.toLowerCase();
    
    return this.history
      .filter(item => item.text.toLowerCase().includes(lowerQuery))
      .slice(-limit)
      .reverse(); // Most recent first
  }

  /**
   * Get recent clipboard entries
   */
  getRecentEntries(limit: number = 10): ClipboardContent[] {
    return this.history
      .slice(-limit)
      .reverse(); // Most recent first
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.stopMonitoring();
    this.listeners.clear();
    this.history = [];
    logger.debug('ClipboardMonitor destroyed');
  }
}
