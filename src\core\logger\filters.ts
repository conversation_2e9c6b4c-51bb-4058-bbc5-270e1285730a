/**
 * Log filters for controlling log output
 */

import { LogFilter, LogEntry, LogLevel } from './types';

export class LevelFilter implements LogFilter {
  constructor(private minLevel: LogLevel) {}

  shouldLog(entry: LogEntry): boolean {
    return entry.level >= this.minLevel;
  }
}

export class ModuleFilter implements LogFilter {
  constructor(private allowedModules: string[]) {}

  shouldLog(entry: LogEntry): boolean {
    if (!entry.context.module) return true;
    return this.allowedModules.includes(entry.context.module);
  }
}

export class RateLimitFilter implements LogFilter {
  private counts: Map<string, { count: number; resetTime: number }> = new Map();

  constructor(
    private maxCount: number,
    private windowMs: number
  ) {}

  shouldLog(entry: LogEntry): boolean {
    const key = `${entry.level}:${entry.message}`;
    const now = Date.now();
    const window = this.counts.get(key);

    if (!window || now > window.resetTime) {
      this.counts.set(key, { count: 1, resetTime: now + this.windowMs });
      return true;
    }

    if (window.count >= this.maxCount) {
      return false;
    }

    window.count++;
    return true;
  }
}

export class TagFilter implements LogFilter {
  constructor(
    private requiredTags: string[],
    private excludedTags: string[] = []
  ) {}

  shouldLog(entry: LogEntry): boolean {
    const tags = entry.metadata?.tags || [];

    // Check if any excluded tags are present
    if (this.excludedTags.some(tag => tags.includes(tag))) {
      return false;
    }

    // Check if all required tags are present
    if (this.requiredTags.length > 0) {
      return this.requiredTags.every(tag => tags.includes(tag));
    }

    return true;
  }
}

export class ContextFilter implements LogFilter {
  constructor(private contextMatchers: Record<string, any>) {}

  shouldLog(entry: LogEntry): boolean {
    for (const [key, value] of Object.entries(this.contextMatchers)) {
      if (entry.context[key] !== value) {
        return false;
      }
    }
    return true;
  }
}
