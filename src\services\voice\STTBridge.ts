/**
 * Speech-to-Text Bridge implementation
 * Provides unified interface for different STT providers with OpenAI Whisper as default
 */

import {
  ISTTService,
  STTServiceConfig,
  STTOptions,
  TranscriptionResult,
  TranscriptionChunk,
  STTServiceError
} from './types';
import { readFile } from '../../core/filesystem';
import { LoggerFactory } from '../../core/logger';
import { toError, getErrorMessage } from '../../core/utils/errorUtils';

const logger = LoggerFactory.getInstance().getLogger('STTBridge');

/**
 * STT Bridge implementation with OpenAI Whisper support
 */
export class STTBridge implements ISTTService {
  private config: STTServiceConfig;

  constructor(config: STTServiceConfig) {
    this.config = {
      timeout: 30000,
      retries: 3,
      ...config
    };
  }

  /**
   * Transcribe audio blob
   */
  async transcribe(audioBlob: Blob, options: STTOptions = {
    language: 'en-US',
    model: 'whisper-1',
    punctuation: true
  }): Promise<TranscriptionResult> {
    try {
      switch (this.config.provider) {
        case 'openai':
          return await this.transcribeWithOpenAI(audioBlob, options);
        case 'google':
          return await this.transcribeWithGoogle(audioBlob, options);
        case 'azure':
          return await this.transcribeWithAzure(audioBlob, options);
        case 'local':
          return await this.transcribeWithLocal(audioBlob, options);
        default:
          throw new STTServiceError(`Unsupported STT provider: ${this.config.provider}`);
      }
    } catch (error) {
      logger.error('Transcription failed', { error: toError(error), provider: this.config.provider });
      throw new STTServiceError('Transcription failed', toError(error));
    }
  }

  /**
   * Transcribe audio file
   */
  async transcribeFile(filePath: string, options: STTOptions = {
    language: 'en-US',
    model: 'whisper-1',
    punctuation: true
  }): Promise<TranscriptionResult> {
    try {
      const audioBuffer = await readFile(filePath) as Buffer;
      const audioBlob = new Blob([audioBuffer]);
      return await this.transcribe(audioBlob, options);
    } catch (error) {
      logger.error('File transcription failed', { error: toError(error), filePath });
      throw new STTServiceError('File transcription failed', toError(error));
    }
  }

  /**
   * Transcribe audio stream (placeholder implementation)
   */
  async transcribeStream(
    audioStream: ReadableStream,
    options: STTOptions = {
      language: 'en-US',
      model: 'whisper-1',
      punctuation: true
    }
  ): Promise<AsyncIterable<TranscriptionChunk>> {
    return this.transcribeStreamInternal(audioStream, options);
  }

  /**
   * Internal stream transcription implementation
   */
  private async *transcribeStreamInternal(
    audioStream: ReadableStream,
    options: STTOptions = {
      language: 'en-US',
      model: 'whisper-1',
      punctuation: true
    }
  ): AsyncIterable<TranscriptionChunk> {
    try {
      const reader = audioStream.getReader();
      let buffer = new Uint8Array();
      const chunkSize = 16000 * 2; // 1 second of 16kHz 16-bit audio

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        // Accumulate audio data
        const newBuffer = new Uint8Array(buffer.length + value.length);
        newBuffer.set(buffer);
        newBuffer.set(value, buffer.length);
        buffer = newBuffer;

        // Process when we have enough data
        if (buffer.length >= chunkSize) {
          const chunkBlob = new Blob([buffer.slice(0, chunkSize)]);
          buffer = buffer.slice(chunkSize);

          try {
            const result = await this.transcribe(chunkBlob, options);
            yield {
              text: result.text,
              isFinal: false,
              confidence: result.confidence,
              startTime: 0, // Would need proper timing
              endTime: 1
            };
          } catch (error) {
            logger.warn('Chunk transcription failed', { error });
            yield {
              text: '',
              isFinal: false,
              confidence: 0
            };
          }
        }
      }

      // Process remaining buffer
      if (buffer.length > 0) {
        const finalBlob = new Blob([buffer]);
        try {
          const result = await this.transcribe(finalBlob, options);
          yield {
            text: result.text,
            isFinal: true,
            confidence: result.confidence
          };
        } catch (error) {
          logger.warn('Final chunk transcription failed', { error });
        }
      }
    } catch (error) {
      logger.error('Stream transcription failed', { error: toError(error) });
      throw new STTServiceError('Stream transcription failed', toError(error));
    }
  }

  /**
   * Get supported languages
   */
  getSupportedLanguages(): string[] {
    // Common languages supported by most STT providers
    return [
      'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh',
      'ar', 'hi', 'tr', 'pl', 'nl', 'sv', 'da', 'no', 'fi', 'he',
      'th', 'vi', 'id', 'ms', 'tl', 'uk', 'bg', 'hr', 'cs', 'sk'
    ];
  }

  /**
   * Detect language from audio (placeholder implementation)
   */
  async detectLanguage(_audioBlob: Blob): Promise<string> {
    try {
      // For now, return default language
      // In a real implementation, this would use language detection
      return this.config.defaultModel?.includes('en') ? 'en' : 'en';
    } catch (error) {
      logger.error('Language detection failed', { error });
      return 'en'; // Fallback to English
    }
  }

  /**
   * Transcribe with OpenAI Whisper
   */
  private async transcribeWithOpenAI(audioBlob: Blob, options: STTOptions): Promise<TranscriptionResult> {
    if (!this.config.apiKey) {
      throw new STTServiceError('OpenAI API key is required');
    }

    const formData = new FormData();
    formData.append('file', audioBlob, 'audio.wav');
    formData.append('model', options.model || this.config.defaultModel || 'whisper-1');

    if (options.language) {
      formData.append('language', options.language);
    }

    if (options.punctuation) {
      formData.append('response_format', 'verbose_json');
    }

    if (options.wordTimestamps) {
      formData.append('timestamp_granularities[]', 'word');
    }

    const baseURL = this.config.baseURL || 'https://api.openai.com/v1';
    
    try {
      const response = await this.makeRequest(`${baseURL}/audio/transcriptions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`
        },
        body: formData
      });

      const data = await response.json();

      return {
        text: data.text || '',
        confidence: data.confidence || 0.9,
        language: data.language || options.language || 'en',
        duration: data.duration || 0,
        words: data.words || []
      };
    } catch (error) {
      throw new STTServiceError(`OpenAI transcription failed: ${getErrorMessage(error)}`, toError(error));
    }
  }

  /**
   * Transcribe with Google Speech-to-Text (placeholder)
   */
  private async transcribeWithGoogle(_audioBlob: Blob, _options: STTOptions): Promise<TranscriptionResult> {
    // Placeholder implementation
    throw new STTServiceError('Google Speech-to-Text provider not yet implemented');
  }

  /**
   * Transcribe with Azure Speech Services (placeholder)
   */
  private async transcribeWithAzure(_audioBlob: Blob, _options: STTOptions): Promise<TranscriptionResult> {
    // Placeholder implementation
    throw new STTServiceError('Azure Speech Services provider not yet implemented');
  }

  /**
   * Transcribe with local STT service (placeholder)
   */
  private async transcribeWithLocal(_audioBlob: Blob, _options: STTOptions): Promise<TranscriptionResult> {
    // Placeholder implementation for local STT
    // This could integrate with local Whisper, Vosk, or other local STT engines
    throw new STTServiceError('Local STT provider not yet implemented');
  }

  /**
   * Make HTTP request with retry logic
   */
  private async makeRequest(url: string, options: RequestInit): Promise<Response> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.config.retries!; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

        const response = await fetch(url, {
          ...options,
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return response;
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < this.config.retries!) {
          const delay = Math.pow(2, attempt - 1) * 1000; // Exponential backoff
          logger.warn(`Request failed, retrying in ${delay}ms`, { 
            attempt, 
            error: getErrorMessage(error),
            url 
          });
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw new STTServiceError(`Request failed after ${this.config.retries} attempts`, lastError!);
  }
}
