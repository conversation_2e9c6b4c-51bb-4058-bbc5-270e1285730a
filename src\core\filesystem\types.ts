/**
 * File system module type definitions
 * Cross-platform file system operations with security and error handling
 */

// File operation types
export type FileOperation = 'read' | 'write' | 'delete' | 'copy' | 'move' | 'create' | 'watch';
export type FileType = 'file' | 'directory' | 'symlink' | 'unknown';
export type FileEncoding = 'utf8' | 'ascii' | 'base64' | 'hex' | 'binary';
export type WatchEventType = 'add' | 'change' | 'unlink' | 'addDir' | 'unlinkDir' | 'error';

// File permissions
export interface FilePermissions {
  readonly readable: boolean;
  readonly writable: boolean;
  readonly executable: boolean;
  readonly mode: number;
}

// File metadata
export interface FileMetadata {
  readonly path: string;
  readonly name: string;
  readonly extension: string;
  readonly size: number;
  readonly type: FileType;
  readonly permissions: FilePermissions;
  readonly createdAt: Date;
  readonly modifiedAt: Date;
  readonly accessedAt: Date;
  readonly isHidden: boolean;
  readonly mimeType?: string;
}

// File operation options
export interface FileReadOptions {
  readonly encoding?: FileEncoding;
  readonly flag?: string;
  readonly signal?: AbortSignal;
}

export interface FileWriteOptions {
  readonly encoding?: FileEncoding;
  readonly mode?: number;
  readonly flag?: string;
  readonly signal?: AbortSignal;
  readonly atomic?: boolean;
  readonly backup?: boolean;
}

export interface DirectoryOptions {
  readonly recursive?: boolean;
  readonly mode?: number;
}

export interface CopyOptions {
  readonly overwrite?: boolean;
  readonly preserveTimestamps?: boolean;
  readonly filter?: (src: string, dest: string) => boolean;
  readonly recursive?: boolean;
}

export interface MoveOptions {
  readonly overwrite?: boolean;
}

// File watching options
export interface WatchOptions {
  readonly recursive?: boolean;
  readonly ignored?: string | RegExp | ((path: string) => boolean);
  readonly persistent?: boolean;
  readonly ignoreInitial?: boolean;
  readonly followSymlinks?: boolean;
  readonly depth?: number;
  readonly awaitWriteFinish?: boolean | {
    stabilityThreshold?: number;
    pollInterval?: number;
  };
}

// Watch event
export interface WatchEvent {
  readonly type: WatchEventType;
  readonly path: string;
  readonly stats?: FileMetadata;
  readonly timestamp: Date;
}

// File operation result
export interface FileOperationResult<T = void> {
  readonly success: boolean;
  readonly data?: T;
  readonly error?: Error;
  readonly path?: string;
  readonly operation: FileOperation;
  readonly timestamp: Date;
}

// Security context
export interface SecurityContext {
  readonly allowedPaths?: readonly string[];
  readonly deniedPaths?: readonly string[];
  readonly maxFileSize?: number;
  readonly allowedExtensions?: readonly string[];
  readonly deniedExtensions?: readonly string[];
  readonly requirePermissions?: boolean;
}

// Path validation result
export interface PathValidationResult {
  readonly valid: boolean;
  readonly normalized: string;
  readonly errors: readonly string[];
  readonly warnings: readonly string[];
}

// File system error
export interface FileSystemError extends Error {
  readonly code: string;
  readonly path?: string;
  readonly operation: FileOperation;
  readonly syscall?: string;
  readonly errno?: number;
}

// Backup options
export interface BackupOptions {
  readonly maxBackups?: number;
  readonly timestampFormat?: string;
  readonly compression?: boolean;
  readonly location?: string;
}

// File lock
export interface FileLock {
  readonly path: string;
  readonly exclusive: boolean;
  readonly timeout?: number;
  release(): Promise<void>;
}

// File stream options
export interface StreamOptions {
  readonly highWaterMark?: number;
  readonly encoding?: FileEncoding;
  readonly start?: number;
  readonly end?: number;
}

// Directory listing options
export interface ListOptions {
  readonly recursive?: boolean;
  readonly includeHidden?: boolean;
  readonly filter?: (metadata: FileMetadata) => boolean;
  readonly sort?: 'name' | 'size' | 'modified' | 'created';
  readonly order?: 'asc' | 'desc';
  readonly limit?: number;
}

// File system statistics
export interface FileSystemStats {
  readonly totalSpace: number;
  readonly freeSpace: number;
  readonly usedSpace: number;
  readonly availableSpace: number;
}

// Atomic operation context
export interface AtomicContext {
  readonly tempPath: string;
  readonly targetPath: string;
  readonly operation: FileOperation;
  commit(): Promise<void>;
  rollback(): Promise<void>;
}

// File system manager interface
export interface IFileSystemManager {
  // Basic operations
  exists(path: string): Promise<boolean>;
  readFile(path: string, options?: FileReadOptions): Promise<string | Buffer>;
  writeFile(path: string, data: string | Buffer, options?: FileWriteOptions): Promise<void>;
  deleteFile(path: string): Promise<void>;
  copyFile(src: string, dest: string, options?: CopyOptions): Promise<void>;
  moveFile(src: string, dest: string, options?: MoveOptions): Promise<void>;
  
  // Directory operations
  createDirectory(path: string, options?: DirectoryOptions): Promise<void>;
  deleteDirectory(path: string, options?: { recursive?: boolean }): Promise<void>;
  listDirectory(path: string, options?: ListOptions): Promise<FileMetadata[]>;
  
  // Metadata operations
  getMetadata(path: string): Promise<FileMetadata>;
  setPermissions(path: string, mode: number): Promise<void>;
  
  // Watching
  watch(path: string, options?: WatchOptions): Promise<FileWatcher>;
  
  // Security
  validatePath(path: string, context?: SecurityContext): PathValidationResult;
  
  // Utilities
  createBackup(path: string, options?: BackupOptions): Promise<string>;
  restoreBackup(backupPath: string, targetPath: string): Promise<void>;
  getStats(path?: string): Promise<FileSystemStats>;
}

// File watcher interface
export interface FileWatcher {
  readonly path: string;
  readonly options: WatchOptions;
  readonly isActive: boolean;
  
  on(event: 'change', listener: (event: WatchEvent) => void): this;
  on(event: 'error', listener: (error: Error) => void): this;
  
  close(): Promise<void>;
}
