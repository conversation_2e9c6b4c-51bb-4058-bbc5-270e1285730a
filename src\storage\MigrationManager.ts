/**
 * Database migration management
 * Handles schema versioning and database migrations
 */

import * as crypto from 'crypto';
import { DatabaseConnection, Migration, SchemaVersion } from './types';
import { LoggerFactory } from '../core/logger';

const logger = LoggerFactory.getInstance().getLogger('MigrationManager');

/**
 * Migration manager for database schema versioning
 */
export class MigrationManager {
  private migrations: Migration[] = [];

  constructor(private db: DatabaseConnection) {
    this.loadMigrations();
  }

  /**
   * Get current schema version
   */
  async getCurrentVersion(): Promise<number> {
    try {
      const result = await this.db.get<{ version: number }>(
        'SELECT version FROM schema_version ORDER BY version DESC LIMIT 1'
      );
      return result?.version || 0;
    } catch {
      // Table doesn't exist yet
      return 0;
    }
  }

  /**
   * Get all applied migrations
   */
  async getAppliedMigrations(): Promise<SchemaVersion[]> {
    try {
      const results = await this.db.all<SchemaVersion>(
        'SELECT version, description, applied_at as appliedAt, checksum FROM schema_version ORDER BY version'
      );
      return results.map(row => ({
        ...row,
        appliedAt: new Date(row.appliedAt)
      }));
    } catch {
      return [];
    }
  }

  /**
   * Run pending migrations
   */
  async migrate(): Promise<void> {
    const currentVersion = await this.getCurrentVersion();
    const pendingMigrations = this.migrations.filter(m => m.version > currentVersion);

    if (pendingMigrations.length === 0) {
      logger.info('No pending migrations');
      return;
    }

    logger.info(`Running ${pendingMigrations.length} pending migrations`);

    await this.db.transaction(async () => {
      for (const migration of pendingMigrations) {
        await this.runMigration(migration);
      }
    });

    logger.info('All migrations completed successfully');
  }

  /**
   * Rollback to a specific version
   */
  async rollbackTo(targetVersion: number): Promise<void> {
    const currentVersion = await this.getCurrentVersion();
    
    if (targetVersion >= currentVersion) {
      throw new Error(`Target version ${targetVersion} is not less than current version ${currentVersion}`);
    }

    const migrationsToRollback = this.migrations
      .filter(m => m.version > targetVersion && m.version <= currentVersion)
      .sort((a, b) => b.version - a.version); // Reverse order for rollback

    if (migrationsToRollback.length === 0) {
      logger.info('No migrations to rollback');
      return;
    }

    logger.info(`Rolling back ${migrationsToRollback.length} migrations to version ${targetVersion}`);

    await this.db.transaction(async () => {
      for (const migration of migrationsToRollback) {
        await this.rollbackMigration(migration);
      }
    });

    logger.info(`Rollback to version ${targetVersion} completed successfully`);
  }

  /**
   * Validate migration integrity
   */
  async validateMigrations(): Promise<boolean> {
    const appliedMigrations = await this.getAppliedMigrations();
    
    for (const applied of appliedMigrations) {
      const migration = this.migrations.find(m => m.version === applied.version);
      
      if (!migration) {
        logger.error(`Applied migration ${applied.version} not found in migration files`);
        return false;
      }

      if (migration.checksum && applied.checksum && migration.checksum !== applied.checksum) {
        logger.error(`Checksum mismatch for migration ${applied.version}`);
        return false;
      }
    }

    return true;
  }

  /**
   * Run a single migration
   */
  private async runMigration(migration: Migration): Promise<void> {
    logger.info(`Running migration ${migration.version}: ${migration.description}`);
    
    try {
      await migration.up(this.db);
      await this.recordMigration(migration);
      logger.info(`Migration ${migration.version} completed successfully`);
    } catch (error) {
      logger.error(`Migration ${migration.version} failed`, error);
      throw error;
    }
  }

  /**
   * Rollback a single migration
   */
  private async rollbackMigration(migration: Migration): Promise<void> {
    logger.info(`Rolling back migration ${migration.version}: ${migration.description}`);
    
    try {
      await migration.down(this.db);
      await this.removeMigrationRecord(migration.version);
      logger.info(`Migration ${migration.version} rolled back successfully`);
    } catch (error) {
      logger.error(`Migration ${migration.version} rollback failed`, error);
      throw error;
    }
  }

  /**
   * Record a completed migration
   */
  private async recordMigration(migration: Migration): Promise<void> {
    const checksum = migration.checksum || this.generateChecksum(migration);
    
    await this.db.run(
      `INSERT INTO schema_version (version, description, applied_at, checksum) 
       VALUES (?, ?, CURRENT_TIMESTAMP, ?)`,
      [migration.version, migration.description, checksum]
    );
  }

  /**
   * Remove migration record
   */
  private async removeMigrationRecord(version: number): Promise<void> {
    await this.db.run(
      'DELETE FROM schema_version WHERE version = ?',
      [version]
    );
  }

  /**
   * Generate checksum for migration
   */
  private generateChecksum(migration: Migration): string {
    const content = `${migration.version}:${migration.description}:${migration.up.toString()}:${migration.down.toString()}`;
    return crypto.createHash('sha256').update(content).digest('hex');
  }

  /**
   * Load all migrations
   */
  private loadMigrations(): void {
    this.migrations = [
      {
        version: 1,
        description: 'Initial schema',
        up: async (db: DatabaseConnection) => {
          // Create schema_version table first
          await db.exec(`
            CREATE TABLE IF NOT EXISTS schema_version (
              version INTEGER PRIMARY KEY,
              description TEXT NOT NULL,
              applied_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              checksum TEXT
            );
          `);

          // Create categories table
          await db.exec(`
            CREATE TABLE categories (
              id TEXT PRIMARY KEY,
              name TEXT NOT NULL UNIQUE,
              description TEXT,
              parent_id TEXT,
              color TEXT,
              icon TEXT,
              sort_order INTEGER DEFAULT 0,
              prompt_count INTEGER DEFAULT 0,
              is_system BOOLEAN DEFAULT FALSE,
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              
              FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE CASCADE,
              
              CHECK (length(name) > 0),
              CHECK (sort_order >= 0),
              CHECK (prompt_count >= 0),
              CHECK (color IS NULL OR length(color) = 7)
            );
          `);

          // Create prompts table
          await db.exec(`
            CREATE TABLE prompts (
              id TEXT PRIMARY KEY,
              title TEXT NOT NULL,
              content TEXT NOT NULL,
              description TEXT,
              category_id TEXT NOT NULL,
              tags TEXT,
              variables TEXT,
              metadata TEXT NOT NULL,
              version INTEGER DEFAULT 1,
              parent_id TEXT,
              is_template BOOLEAN DEFAULT FALSE,
              is_favorite BOOLEAN DEFAULT FALSE,
              is_archived BOOLEAN DEFAULT FALSE,
              usage_count INTEGER DEFAULT 0,
              last_used_at DATETIME,
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              created_by TEXT DEFAULT 'user',
              
              FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
              FOREIGN KEY (parent_id) REFERENCES prompts(id) ON DELETE SET NULL,
              
              CHECK (length(title) > 0),
              CHECK (length(content) > 0),
              CHECK (version > 0),
              CHECK (usage_count >= 0)
            );
          `);

          // Create tags table
          await db.exec(`
            CREATE TABLE tags (
              name TEXT PRIMARY KEY,
              color TEXT,
              description TEXT,
              usage_count INTEGER DEFAULT 0,
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              last_used_at DATETIME,
              
              CHECK (length(name) > 0),
              CHECK (usage_count >= 0),
              CHECK (color IS NULL OR length(color) = 7)
            );
          `);

          // Create settings table
          await db.exec(`
            CREATE TABLE settings (
              key TEXT PRIMARY KEY,
              value TEXT NOT NULL,
              value_type TEXT NOT NULL,
              category TEXT DEFAULT 'general',
              description TEXT,
              is_sensitive BOOLEAN DEFAULT FALSE,
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              
              CHECK (length(key) > 0),
              CHECK (value_type IN ('string', 'number', 'boolean', 'json'))
            );
          `);

          // Create usage analytics table
          await db.exec(`
            CREATE TABLE usage_analytics (
              id TEXT PRIMARY KEY,
              prompt_id TEXT NOT NULL,
              session_id TEXT,
              used_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              context_type TEXT,
              enhancement_used BOOLEAN DEFAULT FALSE,
              rating INTEGER,
              success BOOLEAN,
              execution_time_ms INTEGER,
              tokens_used INTEGER,
              cost REAL,
              metadata TEXT,
              
              FOREIGN KEY (prompt_id) REFERENCES prompts(id) ON DELETE CASCADE,
              
              CHECK (rating IS NULL OR (rating >= 1 AND rating <= 5)),
              CHECK (execution_time_ms IS NULL OR execution_time_ms >= 0),
              CHECK (tokens_used IS NULL OR tokens_used >= 0),
              CHECK (cost IS NULL OR cost >= 0)
            );
          `);

          // Create indexes
          await this.createIndexes(db);
          
          // Insert default data
          await this.insertDefaultData(db);
        },
        down: async (db: DatabaseConnection) => {
          const tables = ['usage_analytics', 'prompts', 'categories', 'tags', 'settings'];
          for (const table of tables) {
            await db.exec(`DROP TABLE IF EXISTS ${table}`);
          }
        }
      }
    ];
  }

  /**
   * Create database indexes
   */
  private async createIndexes(db: DatabaseConnection): Promise<void> {
    const indexes = [
      'CREATE INDEX idx_prompts_category_id ON prompts(category_id)',
      'CREATE INDEX idx_prompts_created_at ON prompts(created_at DESC)',
      'CREATE INDEX idx_prompts_updated_at ON prompts(updated_at DESC)',
      'CREATE INDEX idx_prompts_last_used_at ON prompts(last_used_at DESC)',
      'CREATE INDEX idx_prompts_usage_count ON prompts(usage_count DESC)',
      'CREATE INDEX idx_prompts_is_favorite ON prompts(is_favorite) WHERE is_favorite = TRUE',
      'CREATE INDEX idx_prompts_is_template ON prompts(is_template) WHERE is_template = TRUE',
      'CREATE INDEX idx_prompts_is_archived ON prompts(is_archived)',
      'CREATE INDEX idx_prompts_parent_id ON prompts(parent_id)',
      
      'CREATE INDEX idx_categories_parent_id ON categories(parent_id)',
      'CREATE INDEX idx_categories_sort_order ON categories(sort_order)',
      'CREATE INDEX idx_categories_name ON categories(name)',
      
      'CREATE INDEX idx_tags_usage_count ON tags(usage_count DESC)',
      'CREATE INDEX idx_tags_last_used_at ON tags(last_used_at DESC)',
      
      'CREATE INDEX idx_settings_category ON settings(category)',
      'CREATE INDEX idx_settings_is_sensitive ON settings(is_sensitive)',
      
      'CREATE INDEX idx_usage_analytics_prompt_id ON usage_analytics(prompt_id)',
      'CREATE INDEX idx_usage_analytics_used_at ON usage_analytics(used_at DESC)',
      'CREATE INDEX idx_usage_analytics_session_id ON usage_analytics(session_id)',
      'CREATE INDEX idx_usage_analytics_context_type ON usage_analytics(context_type)',
      'CREATE INDEX idx_usage_analytics_rating ON usage_analytics(rating)'
    ];

    for (const indexSql of indexes) {
      await db.exec(indexSql);
    }
  }

  /**
   * Insert default data
   */
  private async insertDefaultData(db: DatabaseConnection): Promise<void> {
    // Insert default categories
    const categories = [
      { id: 'general', name: 'General', description: 'General purpose prompts', isSystem: true, sortOrder: 0 },
      { id: 'development', name: 'Development', description: 'Programming and development prompts', isSystem: true, sortOrder: 1 },
      { id: 'writing', name: 'Writing', description: 'Content creation and writing prompts', isSystem: true, sortOrder: 2 },
      { id: 'analysis', name: 'Analysis', description: 'Data analysis and research prompts', isSystem: true, sortOrder: 3 },
      { id: 'templates', name: 'Templates', description: 'Reusable prompt templates', isSystem: true, sortOrder: 4 }
    ];

    for (const category of categories) {
      await db.run(
        `INSERT INTO categories (id, name, description, is_system, sort_order) VALUES (?, ?, ?, ?, ?)`,
        [category.id, category.name, category.description, category.isSystem, category.sortOrder]
      );
    }

    // Insert default settings
    const settings = [
      { key: 'app.version', value: '1.0.0', valueType: 'string', category: 'system', description: 'Application version' },
      { key: 'app.first_run', value: 'true', valueType: 'boolean', category: 'system', description: 'First time running the app' },
      { key: 'ui.theme', value: 'system', valueType: 'string', category: 'appearance', description: 'UI theme preference' },
      { key: 'hotkeys.show_floating_window', value: 'Ctrl+Shift+P', valueType: 'string', category: 'hotkeys', description: 'Show floating window hotkey' },
      { key: 'ai.default_provider', value: 'openai', valueType: 'string', category: 'ai', description: 'Default AI provider' },
      { key: 'voice.default_language', value: 'en', valueType: 'string', category: 'voice', description: 'Default voice recognition language' }
    ];

    for (const setting of settings) {
      await db.run(
        `INSERT INTO settings (key, value, value_type, category, description) VALUES (?, ?, ?, ?, ?)`,
        [setting.key, setting.value, setting.valueType, setting.category, setting.description]
      );
    }
  }
}
